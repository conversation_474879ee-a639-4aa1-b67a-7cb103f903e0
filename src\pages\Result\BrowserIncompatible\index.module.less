.Content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 75vh;
  padding: 24px;
  min-height: 400px;
  color: var(--td-brand-color);

  .title {
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    margin-top: 8px;
    color: var(--td-text-color-primary);
  }
  .description {
    margin: 8px 0 32px;
    font-size: 14px;
    line-height: 22px;
    color: var(--td-text-color-secondary);
  }

  .rightButton {
    margin-left: 8px;
  }

  .resultSlotContainer {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    color: var(--td-text-color-secondary);

    .recommendContainer {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: space-between;
      top: 175px;
      padding: 24px 48px;
      width: 640px;
      background: var(--td-bg-color-container);
      box-shadow: 0 1px 2px var(--td-shadow-1);
      border-radius: 3px;

      .recommendBrowser {
        display: flex;

        img {
          width: 36.67px;
          height: 36.67px;
        }
      }
      .recommendBrowser > div {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        height: 70px;
      }
      .recommendBrowser > div + div {
        margin-left: 40px;
      }
    }
  }
}
