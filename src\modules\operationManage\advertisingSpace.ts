import { RootState } from '../store';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import { getAdvertListApi, deleteAdvertApi, openOrCloseAdvertApi, addOrUpdateAdvertApi } from 'api';
import type { IGetAdvertListApi, IOpenOrCloseAdvertApi, IAddOrUpdateAdvertApi } from 'api';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'advertisingSpace';

const initialState = {
  loading: true,
  formLoading: false,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  list: [] as Array<any>,
  carouselImageList: [] as Array<any>,
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getAdvertList = useAsyncThunkWithStatus(`getAdvertList`, async (params) => {
  const {
    data: { rows, total },
  } = await getAdvertListApi(params as IGetAdvertListApi);
  const { pageNum, pageSize } = params;
  return {
    total,
    rows,
    pageNum,
    pageSize,
  };
});

export const getCarouselImageList = useAsyncThunkWithStatus(`getCarouselImageList`, async () => {
  const {
    data: { rows, total },
  } = await getAdvertListApi({ pageNum: 1, pageSize: 4 } as IGetAdvertListApi);
  return {
    total,
    rows,
  };
});

export const deleteAdvert = useAsyncThunkWithStatus(`deleteAdvert`, async (id, { dispatch, getState }) => {
  await deleteAdvertApi(id as number);
  const {
    advertisingSpace: { pageNum, pageSize },
  } = getState() as RootState;
  MessagePlugin.success('删除成功！');
  await dispatch(
    getAdvertList({
      pageNum,
      pageSize,
    }),
  );
});

export const openOrCloseAdvert = useAsyncThunkWithStatus(
  `openOrCloseAdvert`,
  async (params, { dispatch, getState }) => {
    await openOrCloseAdvertApi(params as IOpenOrCloseAdvertApi);
    const {
      advertisingSpace: { pageNum, pageSize },
    } = getState() as RootState;
    await dispatch(
      getAdvertList({
        pageNum,
        pageSize,
      }),
    );
  },
);

export const addOrUpdateAdvert = useAsyncThunkWithStatus(`addOrUpdateAdvert`, async (params) => {
  await addOrUpdateAdvertApi(params as IAddOrUpdateAdvertApi);
});

const advertisingSpaceSliceSlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  cases: [
    {
      thunk: getAdvertList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.total = action.payload.total;
        state.list = action.payload.rows;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: openOrCloseAdvert,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state) => {
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: addOrUpdateAdvert,
      pending: (state) => {
        state.formLoading = true;
      },
      fulfilled: (state) => {
        state.formLoading = false;
      },
      rejected: (state) => {
        state.formLoading = false;
      },
    },
    {
      thunk: getCarouselImageList,
      pending: () => {},
      fulfilled: (state, action) => {
        state.carouselImageList = action.payload.rows;
      },
      rejected: () => {},
    },
  ],
});

export const { clearPageState } = advertisingSpaceSliceSlice.actions;

export const selectAdvertisingSpace = (state: RootState) => state.advertisingSpace;

export default advertisingSpaceSliceSlice.reducer;
