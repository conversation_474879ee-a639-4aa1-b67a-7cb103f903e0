import { createAsyncThunk, AsyncThunk, ActionReducerMapBuilder, PayloadAction, Draft } from '@reduxjs/toolkit';
import { RootState } from 'modules/store';

type AsyncThunkConfig = {
  state: RootState;
};

type ThunkAPI = {
  getState: () => RootState;
  dispatch: any;
  extra: unknown;
  requestId: string;
  signal: AbortSignal;
  rejectWithValue: any;
};

export const useAsyncThunkWithStatus = <Returned, ThunkArg>(
  typePrefix: string,
  payloadCreator: (arg: ThunkArg, thunkAPI: ThunkAPI) => Promise<Returned>,
): AsyncThunk<Returned, ThunkArg, AsyncThunkConfig> =>
  createAsyncThunk<Returned, ThunkArg, AsyncThunkConfig>(typePrefix, async (arg, thunkAPI) => {
    try {
      return await payloadCreator(arg, thunkAPI);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  });

type PendingCallback<S> = (state: Draft<S>) => void;
type FulfilledCallback<S, P> = (state: Draft<S>, action: PayloadAction<P>) => void;
type RejectedCallback<S> = (state: Draft<S>) => void;

export const addCaseHelper = <S, P>(
  builder: ActionReducerMapBuilder<S>,
  thunk: AsyncThunk<P, any, {}>,
  pendingCallback: PendingCallback<S>,
  fulfilledCallback: FulfilledCallback<S, P>,
  rejectedCallback: RejectedCallback<S>,
) => {
  builder
    .addCase(thunk.pending, (state) => pendingCallback(state as Draft<S>))
    .addCase(thunk.fulfilled, (state, action) => fulfilledCallback(state as Draft<S>, action as PayloadAction<P>))
    .addCase(thunk.rejected, (state) => rejectedCallback(state as Draft<S>));
};
