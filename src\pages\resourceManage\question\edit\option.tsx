import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react';
import { Table, Radio } from 'tdesign-react';
import { useLocation } from 'react-router-dom';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IOptions, option, MENU_CONFIG, EDITOR_STYLE } from '../consts';
import { Editor } from 'components';
import './index.module.less';
import { addSpansBackToFormulas } from 'utils/LaTeX';

import { useAppDispatch } from 'modules/store';
import { setOption, setAnswer } from 'modules/resourceManage/question';

type KeyType = 'A' | 'B' | 'C' | 'D' | '';

interface ISelectTable {
  ref: any;
  options: any;
  onChangeOption: (options: IOptions[]) => void;
}

const SelectTable: React.FC<ISelectTable> = forwardRef(({ options, onChangeOption }, ref) => {
  const dispatch = useAppDispatch();
  const [selectedOption, setSelectedOption] = useState<KeyType>(options.answer ?? '');
  const [optionList, setOptionsList] = useState<IOptions[]>(options.optionVoList ?? option);

  const { state } = useLocation();

  // 创建一个对象用于存储多个 editor 的 ref
  const editorRefs = useRef<{ [key: string]: any }>({});

  const handleRadioChange = (key: KeyType) => {
    setSelectedOption(key);
    const answer = optionList.find((i) => i.opt === key);
    if (answer) {
      dispatch(setAnswer(answer));
    }
  };

  /**
   * handleChange 事件：当用户在某一行输入时触发
   * @param val 富文本编辑器里的值
   * @param row 表格里某一行的值
   */
  const handleChange = (val: string, row: any) => {
    setOptionsList((prevOptionList) => {
      const updatedData = prevOptionList.map((item) => {
        if (item.oid === row.oid) {
          return { ...item, optionContent: val };
        }
        return item;
      });
      dispatch(setOption(updatedData));
      onChangeOption(updatedData);
      return updatedData;
    });
  };

  // 使用 useImperativeHandle 向父组件暴露方法
  useImperativeHandle(ref, () => ({
    // 清空编辑器内容
    clearContent: (optionVoList: IOptions[]) => {
      if (!options.questionId) {
        setSelectedOption('');
        Object.values(editorRefs.current).forEach((ref) => {
          ref.clearContent();
        });
      } else {
        Object.values(editorRefs.current).forEach((ref, i) => {
          ref.clearContent(optionVoList[i].optionContent);
        });
      }
    },
  }));

  return (
    <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
      <Table
        data={optionList}
        columns={[
          {
            title: '序号',
            fixed: 'left',
            ellipsis: true,
            colKey: 'opt',
          },
          {
            title: '选项',
            colKey: 'option',
            width: '65%',
            cell({ row }) {
              return (
                <Editor
                  ref={(editor: any) => {
                    // 将每个编辑器的 ref 存储到 editorRefs
                    editorRefs.current[row.opt] = editor;
                  }}
                  onChange={(res: string) => handleChange(res, row)}
                  editorStyle={EDITOR_STYLE}
                  toolbar={MENU_CONFIG}
                  text={addSpansBackToFormulas(row.optionContent)}
                  maxLength={200}
                ></Editor>
              );
            },
          },
          {
            title: '设为正确选项',
            ellipsis: true,
            colKey: 'answer',
            cell({ row: { opt } }) {
              return (
                <Radio
                  disabled={state.type === 'edit'}
                  name={opt}
                  value={opt}
                  checked={selectedOption === opt}
                  onChange={() => handleRadioChange(opt as KeyType)}
                />
              );
            },
          },
        ]}
        rowKey='oid'
        hover
      />
    </div>
  );
});

export default SelectTable;
