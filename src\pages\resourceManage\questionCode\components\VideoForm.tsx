/* eslint-disable no-nested-ternary */
import React, { Fragment, memo, useEffect, useMemo, useRef, useState } from 'react';
import {
  Form,
  Loading,
  type FormInstanceFunctions,
  FormProps,
  InputNumber,
  Space,
  Button,
  Input,
  Textarea,
} from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import TcVideoPlayer, { IRef, TcVideoPlayerProps } from 'components/TcVideoPlayer';
import { strIsNull } from 'utils/tool';
import { IPreviewVideoBo, IQuesCodeEntity } from 'types/resourceManage';
import {
  saveQuesCode,
  editQuesCode,
  fetchQuesCodeById,
  selectQuesCodeState,
  previewVideo,
  clearState,
} from 'modules/resourceManage/questionCodeRedux';
import { Editor } from 'components';

const { FormItem } = Form;

// Define the video info interface based on the properties used in the component
interface IVideoInfo {
  id?: number;
  fileID?: string;
  fileName?: string;
  fileSize?: number;
  timelen?: number;
  appID?: string;
  psign?: string;
}

interface VideoFormProps {
  disabled?: boolean;
  id: number;
  onChangeVideo: (val: IVideoInfo) => void;
}

const VideoForm: React.FC<VideoFormProps> = ({ disabled, id, onChangeVideo }) => {
  const dispatch = useAppDispatch();
  const { chapterInfoLoading, chapterInfo, previewVideoLoading, previewVideoInfo, loading } =
    useAppSelector(selectQuesCodeState);
  const formRef = useRef<FormInstanceFunctions>();
  const [form] = Form.useForm();
  const fileId = Form.useWatch('fileId', form);
  const [videoInfo, setVideoInfo] = useState<IVideoInfo>({});
  const playerRef = useRef<IRef | null>(null);
  const [options, setOptions] = useState<TcVideoPlayerProps['options'] | null>(null);

  const INITIAL_DATA = {
    appId: '',
    fileId: '',
    codeName: '',
  };

  const rules: FormProps['rules'] = {
    codeName: [
      {
        required: true,
        message: `二维码标题不能为空`,
        trigger: 'all',
      },
    ],
  };

  useEffect(() => {
    // Only fetch QR code data if we have a valid ID (not 0 for new records)
    if (id && id > 0) {
      dispatch(fetchQuesCodeById(id));
    } else {
      // 如果是新增模式 (id === 0)，清除之前的数据
      dispatch(clearState());
      formRef.current?.setFieldsValue(INITIAL_DATA);
      setVideoInfo({});
      setOptions(null);
    }
  }, [id]);

  useEffect(() => {
    if (!strIsNull(chapterInfo)) {
      // 处理视频信息，可能是JSON字符串需要解析
      let videoData = null;
      if (!strIsNull(chapterInfo.pvwVideoVo)) {
        try {
          // 如果是字符串，尝试解析为JSON
          videoData =
            typeof chapterInfo.pvwVideoVo === 'string' ? JSON.parse(chapterInfo.pvwVideoVo) : chapterInfo.pvwVideoVo;
        } catch (error) {
          videoData = null;
        }
      }

      if (videoData) {
        const info = {
          appId: videoData.appID,
          fileId: videoData.fileID,
          codeName: chapterInfo.codeName || '', // 加载现有的标题
          questionStem: chapterInfo.questionStem || '', // 加载现有的标题
          codeBrief: chapterInfo.codeBrief || '', // 加载现有的标题
        };

        setVideoInfo(videoData);
        onChangeVideo(videoData);
        formRef.current?.setFieldsValue(info);

        // 设置播放器选项以播放原来的视频
        playerRef.current?.onDestroy();
        setOptions({
          id: id || 1,
          appID: videoData.appID || '',
          fileID: videoData.fileID || '',
          psign: videoData.psign || '',
        } as TcVideoPlayerProps['options']);
      } else {
        // 如果是编辑模式但没有视频信息，仍然要设置标题
        const info = {
          ...INITIAL_DATA,
          codeName: chapterInfo.codeName || '',
          questionStem: chapterInfo.questionStem || '',
          codeBrief: chapterInfo.codeBrief || '',
        };
        formRef.current?.setFieldsValue(info);
        setVideoInfo({});
        setOptions(null); // 清除播放器选项
      }
    } else {
      formRef.current?.setFieldsValue(INITIAL_DATA);
      setVideoInfo({});
      setOptions(null); // 清除播放器选项
    }
  }, [chapterInfo]);

  useEffect(() => {
    if (!strIsNull(previewVideoInfo)) {
      setVideoInfo(previewVideoInfo);
      onChangeVideo(previewVideoInfo);
      // Set video player options
      playerRef.current?.onDestroy();
      setOptions({
        id: 1,
        appID: previewVideoInfo.appID || '',
        fileID: previewVideoInfo.fileID || '',
        psign: previewVideoInfo.psign || '',
      } as TcVideoPlayerProps['options']);
    }
  }, [previewVideoInfo]);

  // 组件卸载时清除状态
  useEffect(
    () => () => {
      // 清除播放器实例
      playerRef.current?.onDestroy();
    },
    [],
  );

  const handleSubmit: FormProps['onSubmit'] = ({ fields }) => {
    const form: IPreviewVideoBo = fields;
    dispatch(previewVideo(form));
  };

  const handleReset: FormProps['onReset'] = () => {};

  const fileSize = useMemo(() => {
    if (!strIsNull(videoInfo) && videoInfo.fileSize) {
      const temp = videoInfo.fileSize / 1024 / 1024;
      const value = temp >= 1024 ? `${(temp / 1024).toFixed(2)}GB` : `${temp.toFixed(2)}MB`;
      return value;
    }

    return '';
  }, [videoInfo]);

  const handleSaveVideo = () => {
    // Get the codeName from the form
    const formData = form.getFieldsValue(['codeName', 'codeBrief']);

    const bo: IQuesCodeEntity = {
      ...formData,
      id: id || 0, // For new records, use 0
      filesId: videoInfo?.id, // Video info is optional
      codeName: (formData.codeName as string) || '',
      questionStem: (formData.questionStem as string) || '',
      codeBrief: (formData.codeBrief as string) || '',
      codeUrl: '', // Will be generated when creating QR code
      isUse: 0,
    };

    if (id === 0) {
      // For new records, use saveQuesCode
      dispatch(saveQuesCode(bo));
    } else {
      // For existing records, use editQuesCode
      dispatch(editQuesCode(bo));
    }
  };

  return (
    <Loading loading={chapterInfoLoading}>
      <div style={{ padding: '20px', border: '1px solid #e7e7e7', borderRadius: '6px' }}>
        <h3 style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 500 }}>视频信息配置</h3>
        <div
          style={{
            display: 'flex',
            columnGap: '20px',
            flex: 'auto',
            padding: '10px 20px',
            border: '1px solid var(--td-border-level-1-color)',
            height: '500px',
          }}
        >
          <div style={{ flex: '1', minWidth: '50%' }}>
            <Form
              disabled={disabled === true}
              ref={formRef}
              form={form}
              initialData={INITIAL_DATA}
              resetType='initial'
              rules={rules}
              colon
              onSubmit={handleSubmit}
              onReset={handleReset}
              layout='vertical'
            >
              {/* 二维码标题是必选字段，始终显示 */}
              <FormItem label='二维码标题' name='codeName'>
                <Input style={{ width: '100%' }} placeholder='请输入二维码标题' />
              </FormItem>
              <FormItem label='题干' name='stem'>
                <Editor
                  ref={stemEditorRef}
                  border
                  onChange={(res: string) => handleChangeStem(res)}
                  text={addSpansBackToFormulas(row.stem)}
                  toolbar={STEM_MENU_CONFIG}
                  uploadImgConfig={{ maxFileSize: 2 }}
                  maxLength={800}
                  editorStyle={{ height: '200px', width: '100%' }}
                ></Editor>
              </FormItem>
              <FormItem label='视频简介' name='codeBrief'>
                <Textarea placeholder='请输入视频简介' />
              </FormItem>
              <FormItem label='应用ID' name='appId'>
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder='请输入应用ID'
                  theme='normal'
                  decimalPlaces={0}
                  allowInputOverLimit={false}
                  largeNumber
                />
              </FormItem>
              <FormItem label='FileID' name='fileId'>
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder='请输入FileID'
                  theme='normal'
                  decimalPlaces={0}
                  allowInputOverLimit={false}
                  largeNumber
                />
              </FormItem>

              {!strIsNull(videoInfo) && videoInfo.fileID === fileId && (
                <Fragment>
                  <FormItem label='视频名称'>
                    <span>{videoInfo?.fileName}</span>
                  </FormItem>
                  <FormItem label='视频大小'>
                    <span>{fileSize}</span>
                  </FormItem>
                  <FormItem label='视频时长'>
                    <span>{videoInfo?.timelen} S</span>
                  </FormItem>
                </Fragment>
              )}

              <FormItem>
                <Space>
                  {/* 预览按钮 - 当没有视频信息或视频信息不匹配时显示 */}
                  {(strIsNull(videoInfo) || videoInfo.fileID !== fileId) && (
                    <Button disabled={disabled === true} type='submit' theme='primary' loading={previewVideoLoading}>
                      预览视频
                    </Button>
                  )}

                  {/* 保存按钮 - 始终显示，因为二维码标题是必选的 */}
                  <Button disabled={disabled === true} theme='primary' loading={loading} onClick={handleSaveVideo}>
                    保存二维码信息
                  </Button>

                  {/* 重置按钮 - 始终显示 */}
                  <Button disabled={disabled === true} type='reset' theme='warning'>
                    重置
                  </Button>
                </Space>
              </FormItem>
            </Form>
          </div>
          {!strIsNull(options) && (
            <div
              style={{ flex: '1', minWidth: '50%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
            >
              <TcVideoPlayer
                ref={playerRef}
                options={options as TcVideoPlayerProps['options']}
                playerTarget={() => {}}
                width='100%'
                height='400px'
              />
            </div>
          )}
        </div>
      </div>
    </Loading>
  );
};

export default memo(VideoForm);
