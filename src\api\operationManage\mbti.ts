import request from 'utils/request/index';

export interface IGetMbtiListApi {
  pageSize: number;
  pageNum: number;
  name?: string;
}

export interface IOpenOrCloseMbtiExamApi {
  id: number;
  status: number;
}
export interface IMbtiExamTypeDetailApi {
  examId: number;
  mbtiPersonalityId: number;
}
export interface IMbtiExamTypeRelatedMajorApi {
  examId: number;
  mbtiPersonalityId: number;
  majorIdList: any[];
}
/**
 * 分页条件查询mbti列表
 * @param params
 * @returns
 */
export const getMbtiListApi = async (params: IGetMbtiListApi) => {
  const result = await request.get({
    url: '/mbti/getMbtiExamTypeList',
    params,
  });
  return result;
};

/**
 * 查询mbti考试列表(侧边栏单招考试列表)
 * @param params
 * @returns
 */
export const getMbtiAdminExamListApi = async () => {
  const result = await request.get({
    url: '/mbti/getMbtiAdminExamList',
  });
  return result;
};
/**
 * 开启/关闭 置顶
 * @returns
 */
export const openOrCloseMbtiExamApi = async (params: IOpenOrCloseMbtiExamApi) => {
  const result = await request.post({
    url: `/mbti/openOrCloseMbtiExam`,
    params,
  });
  return result;
};
/**
 * 查询mbti人格类型详情以及关联的专业情况
 * @returns
 * */
export const getMbtiExamTypeDetailApi = async (data: IMbtiExamTypeDetailApi) => {
  const result = await request.post({
    url: `/mbti/getMbtiExamTypeDetail`,
    data,
  });
  return result;
};
/**
 * mbti考试人格类型关联专业
 * @returns
 * */
export const mbtiExamTypeRelatedMajorApi = async (data: IMbtiExamTypeRelatedMajorApi) => {
  const result = await request.post({
    url: `/mbti/mbtiExamTypeRelatedMajor`,
    data,
  });
  return result;
};
