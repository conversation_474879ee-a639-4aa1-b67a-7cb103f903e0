import { IPage } from 'types';

export interface IGetResourceCourseListBo extends IPage {
  // 视频名称
  courseName?: string;
  // 科目Id
  subjectId?: number;
}

export interface IAddResourceCourseBo {
  // 科目Id
  subjectId: number;
  // 课程名称
  courseName: string;
  // 课程简介
  brief: string;
  // 课程描述
  description: string;
  // 课程封面
  coverImage: number;
  // 是否禁用(0-否 1-是)
  isDisable?: 0 | 1;
  // 课程讲师Id列表
  teacherIds: Array<number>;
}

export interface IEditResourceCourseBo {
  // 视频Id
  id: number;
  // 科目Id
  subjectId?: number;
  // 课程名称
  courseName?: string;
  // 课程简介
  brief?: string;
  // 课程描述
  description?: string;
  // 课程封面
  coverImage?: number;
  // 是否禁用(0-否 1-是)
  isDisable?: 0 | 1;
  // 课程讲师Id列表
  teacherIds?: Array<number>;
}

export interface IAddResourceCourseChapterBo {
  // 课程Id
  courseId: number;
  // 章节名称
  chapterName: string;
  // 父级Id
  parentId: number;
  // 排序
  orderNum: number;
  // 能否试看
  tryAndSee: 0 | 1;
}

export interface IEditResourceCourseChapterBo {
  // 章节Id
  id: number;
  // 章节名称
  chapterName?: string;
  // 排序
  orderNum?: number;
  // 能否试看
  tryAndSee?: 0 | 1;
  // 视频文件Id
  filesId?: number;
}

export interface IPreviewVideoBo {
  // 视频应用ID
  appId: string;
  // 视频文件ID
  fileId: string;
}

export interface IGetResourceLecturerListBo extends IPage {
  // 讲师姓名
  teacherName?: string;
  // 联系电话
  phoneNumber?: string;
}

export interface IAddResourceLecturerBo {
  // 讲师姓名
  teacherName: string;
  // 讲师手机号
  phoneNumber?: string;
  // 讲师职称
  technicalTitle: string;
  // 讲师简介
  brief: string;
  // 讲师照片Id
  teacherImgId?: number;
}

export interface IEditResourceLecturerBo extends IAddResourceLecturerBo {
  id: number;
}

export interface IQuesCodeQueryBo extends IPage {
  // 二维码标题
  codeName?: number;
  // 是否生成二维码 0：未生成 1：已生成
  isCreate?: string;
  // 是否使用 0：未使用 1：已使用
  isUse?: number;
  // 是否上传视频 0：未上传 1：已上传
  isUpload?: boolean;
}

export interface IQuesCodeEntity {
  // 主键ID
  id: number;
  // 二维码标题
  codeName: string;
  questionStem: string;
  // 二维码ID
  codeId?: number;
  // 二维码文件地址
  codeUrl: string;
  // 视频文件ID
  filesId?: number;
  // 二是否启用
  isUse: number;
  // 视频详情
  pvwVideoVo?: string;
  codeBrief?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
}
export interface IQrCode {
  // 主键ID
  id: number;
  // 二维码标题
  imgData: string;
}
