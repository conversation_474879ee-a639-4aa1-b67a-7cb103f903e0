import React, { useState, useEffect } from 'react';
import { Table, Tag, Switch, Button, MessagePlugin, Dialog, type DialogProps } from 'tdesign-react';

import type { TableProps, TdTagProps } from 'tdesign-react';

import { useAppDispatch, useAppSelector } from 'modules/store';
import {
  getSchoolExamInfo,
  selectSchool,
  onOffAppExamInfo,
  addSchoolAssociaExamInfo,
  getSchoolExamList, getEducationalReportedList,
  setEducationalId,
} from 'modules/directory';
import ExamChild from './ExamChild';
interface Status {
  label: string;
  theme: TdTagProps['theme'];
}

interface ITableExpandable {
  visibleProps?: object;
}

const statusNameListMap: Record<number | string, Status> = {
  0: { label: '未关联', theme: 'danger' },
  1: { label: '已关联', theme: 'success' },
  default: { label: '暂无该信息', theme: 'warning' },
};

export default function TableExpandable({ visibleProps }: ITableExpandable) {
  const dispatch = useAppDispatch();
  const pageState = useAppSelector(selectSchool);
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);
  const [visible, setVisible] = useState(false);
  const [rowData, setRowData] = useState({});
  const [type, setType] = useState('');
  const dispatchList = async (val: number, params: any) => {
    const API_LIST = {
      1: getSchoolExamInfo,
      2: onOffAppExamInfo,
      3: addSchoolAssociaExamInfo,
      4: getSchoolExamList,
      5: setEducationalId,
      6: getEducationalReportedList
    };
    await dispatch(API_LIST[val](params));
  };

  const switchChange = (value: boolean | number, row: any) => {
    dispatchList(2, {
      educationalId: visibleProps.educationalId,
      examId: row.examId,
      isOpen: value ? 1 : 0,
    });
  };

  const associationStatus = (row) => {
    dispatchList(3, {
      educationalId: visibleProps.educationalId,
      examId: row.examId,
      relation: row.isRelated === 1 ? 0 : 1,
    });
  };

  const {
    EXAM: { list, switchLoading, listLoading, listChild, reportedlistChild },
  } = pageState;
  useEffect(() => {
    dispatchList(5, visibleProps.educationalId);
    dispatchList(1, visibleProps.educationalId);
  }, []);

  const switchRow = (row) => (
    <Switch
      loading={switchLoading}
      onChange={(value) => {
        switchChange(value, row);
      }}
      size='large'
      value={row.isOpen === 1}
      label={['开', '关']}
    />
  );

  const associationStatusRow = (row) => {
    const isRelated = statusNameListMap[row === 0 || row === 1 ? row : 'default'];
    return (
      <Tag shape='round' theme={isRelated.theme} variant='light-outline'>
        {isRelated.label}
      </Tag>
    );
  };

  const operationRow = (row) => (
    <>
      <Button theme='primary' variant='text' onClick={() => associationStatus(row)}>
        {row.isRelated === 0 ? '关联院校' : '取消关联'}
      </Button>
      <Button
        theme='warning'
        variant='text'
        onClick={async () => {
          if (!row.educationalExamId) {
            MessagePlugin.warning('该考试未关联该院校，请先关联！');
          } else {
            await dispatchList(4, row.educationalExamId);
            rehandleClickOp(row, 1);
          }
        }}
      >
        配置普高生报录数据
      </Button>
      <Button
        theme='warning'
        variant='text'
        onClick={async () => {
          if (!row.educationalExamId) {
            MessagePlugin.warning('该考试未关联该院校，请先关联！');
          } else {
            await dispatchList(6, row.educationalExamId);
            rehandleClickOp(row, 2);
          }
        }}
      >
        配置三校生报录数据
      </Button>
    </>
  );

  const columns: () => TableProps['columns'] = () => [
    { colKey: 'examName', title: '考试类目' },
    {
      colKey: 'isOpen',
      title: '是否开启',
      cell: ({ row }) => switchRow(row),
    },
    {
      colKey: 'isRelated',
      title: '关联状态',
      cell: ({ row: { isRelated: association } }) => associationStatusRow(association),
    },
    {
      colKey: 'operation',
      title: '操作',
      width: 280,
      cell: ({ row }) => operationRow(row),
    },
  ];
  const onClickCloseBtn: DialogProps['onCloseBtnClick'] = () => {
    setVisible(false);
  };
  const rehandleClickOp = (val, type) => {
    setVisible(true);
    setRowData(val);
    setType(type);
  }
  return (
    <div>
      <Table
        loading={listLoading}
        bordered
        rowKey='examId'
        columns={columns()}
        data={list}
        expandOnRowClick={false}
        lazyLoad
        resizable
        expandIcon={false}
      ></Table>
      {visible && (
        <Dialog
          visible={visible}
          confirmBtn={null}
          cancelBtn={null}
          onCloseBtnClick={onClickCloseBtn}
          width={800}
        >
          <ExamChild list={type == 1 ? listChild : reportedlistChild} educationalExamId={rowData.educationalExamId} type={type}>
          </ExamChild>
        </Dialog>
      )}
    </div>




  );
}
