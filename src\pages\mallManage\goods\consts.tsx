import { TdTabPanelProps } from 'tdesign-react';
import VideoCourse from './VideoCourse';
import ElectronicTeachingAids from './ElectronicTeachingAids';

export const TAB_LIST: Array<TdTabPanelProps> = [
  {
    lazy: true,
    value: 1,
    label: '视频课程',
    // eslint-disable-next-line react/react-in-jsx-scope
    panel: <VideoCourse />,
  },
  {
    lazy: true,
    value: 2,
    label: '电子教辅',
    // eslint-disable-next-line react/react-in-jsx-scope
    panel: <ElectronicTeachingAids />,
  },
];
