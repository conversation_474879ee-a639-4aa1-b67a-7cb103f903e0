import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useSelector, useDispatch } from 'react-redux';

import global from './global';
import publicStore from './public';
import user from './user';

import systemUser from './system/user';
import systemRole from './system/role';
import systemMenu from './system/menu';
import systemDept from './system/dept';
import systemPost from './system/post';
import systemDict from './system/dict';
import systemConfig from './system/config';
import systemNotice from './system/notice';
import systemLog from './system/log';

import examManage from './examManage';
import { association, school, speciality } from './directory';
import userManage from './userManage';
import theCharts from './theCharts';
import pointsManagement from './pointsManagement';
import userFeedback from './userFeedback';
import advertisingSpace from './operationManage/advertisingSpace';
import examFrequentlyAsked from './operationManage/examFrequentlyAsked';
import MbtiAdminExam from './operationManage/mbti';
import privateDomainManage from './operationManage/privateDomainManage';
import informationRelease from './operationManage/InformationRelease';
import dynamicPublishing from './operationManage/dynamicPublishing';

import mallManageGoods from './mallManage/goods';
import mallManageConvertRecord from './mallManage/convertRecord';
import mallManageOrder from './mallManage/order';

import resourceManageQuestion from './resourceManage/question';
import resourceManageMaterial from './resourceManage/material';
import resourceManageCourse from './resourceManage/course';
import resourceManageLecturer from './resourceManage/lecturer';
import resourceManageQuesCode from './resourceManage/questionCodeRedux';

import srRankingMechanism from './srRanking/mechanism';
import srRankingMajors from './srRanking/majors';
import srRankingEducational from './srRanking/educational';

// 导入自定义中间件
import errorHandlingMiddleware from './error';

const reducer: any = combineReducers({
  global,
  publicStore,
  user,
  systemUser,
  systemRole,
  systemMenu,
  systemDept,
  systemPost,
  systemDict,
  systemConfig,
  systemNotice,
  systemLog,
  examManage,
  association,
  school,
  speciality,
  userManage,
  theCharts,
  pointsManagement,
  userFeedback,
  advertisingSpace,
  examFrequentlyAsked,
  MbtiAdminExam,
  privateDomainManage,
  informationRelease,
  dynamicPublishing,
  mallManageGoods,
  mallManageConvertRecord,
  mallManageOrder,
  resourceManageQuestion,
  resourceManageMaterial,
  resourceManageCourse,
  resourceManageLecturer,
  resourceManageQuesCode,
  srRankingMechanism,
  srRankingMajors,
  srRankingEducational,
});

export const store = configureStore({
  reducer,
  middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(errorHandlingMiddleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export default store;
