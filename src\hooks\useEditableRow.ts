import { useState, useRef, useEffect } from 'react';
import { MessagePlugin } from 'tdesign-react';

export const useEditableRow = (initialData, { addData, getData, delData }, educationalExamId) => {
  const [data, setData] = useState([...initialData]);
  const [editableRowKeys, setEditableRowKeys] = useState([]);
  const tableRef = useRef(null);
  const editMap = {};

  useEffect(() => {
    setData([...initialData]);
  }, [initialData]);

  const onEdit = (id) => {
    if (!editableRowKeys.includes(id)) {
      setEditableRowKeys([id]);
    }
  };

  const updateEditState = (id) => {
    const index = editableRowKeys.findIndex((t) => t === id);
    editableRowKeys.splice(index, 1);
    setEditableRowKeys([...editableRowKeys]);
  };

  const onCancel = (id) => {
    if (id.startsWith('new_')) {
      setData((prevData) => prevData.filter((item) => item.educationalReportedDataId !== id));
    }
    updateEditState(id);
    tableRef.current.clearValidateData();
  };

  const submitTo = async (params) => {
    await addData(params);
    await getData(educationalExamId);
  };

  const onSave = (id) => {
    const validateRowId = id.startsWith('new_') ? id : parseInt(id, 10);
    tableRef.current.validateRowData(validateRowId).then(async (params) => {
      if (params.result.length !== 0) return;
      if (params.trigger === 'parent' && params.result.length === 0 && validateRowId !== undefined) {
        const current = editMap[id];
        if (current) {
          const isNew = id.startsWith('new_');
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { educationalReportedDataId, ...rest } = current.editedRow;

          if (isNew) {
            await submitTo({ ...rest, educationalExamId });
          } else {
            await submitTo({ ...current.editedRow, educationalExamId });
          }
          MessagePlugin.success('保存成功');
        }
        updateEditState(id);
      }
    });
  };

  const onRowEdit = (params) => {
    const { row, col, value } = params;
    const oldRowData = editMap[row.educationalReportedDataId]?.editedRow || row;
    const editedRow = { ...oldRowData, [col.colKey]: value };
    editMap[row.educationalReportedDataId] = {
      ...params,
      editedRow,
    };
  };

  const onDelete = async (id) => {
    await delData(id);
    await getData(educationalExamId);
  };

  return {
    data,
    setData,
    editableRowKeys,
    setEditableRowKeys,
    tableRef,
    onEdit,
    onCancel,
    onSave,
    onRowEdit,
    onDelete,
  };
};
