import React, { useState, useEffect } from 'react';
import { Table, Tag, Button } from 'tdesign-react';

import type { TableProps, TdTagProps } from 'tdesign-react';

import { useAppDispatch, useAppSelector } from 'modules/store';
import { getMajorsExamList, selectSpeciality, addOrUpdateMajorsExam } from 'modules/directory/speciality';

interface Status {
  label: string;
  theme: TdTagProps['theme'];
}

interface ITableExpandable {
  visibleProps?: object;
}

const statusNameListMap: Record<number | string, Status> = {
  0: { label: '未关联', theme: 'danger' },
  1: { label: '已关联', theme: 'success' },
  default: { label: '暂无该信息', theme: 'warning' },
};

export default function TableExpandable({ visibleProps }: ITableExpandable) {
  const dispatch = useAppDispatch();
  const pageState = useAppSelector(selectSpeciality);
  const [expandedRowKeys, setExpandedRowKeys] = useState([]);

  const associationStatus = (row) => {
    dispatch(
      addOrUpdateMajorsExam({
        majorsId: visibleProps.majorsId,
        examId: row.examId,
        isRelated: row.isRelated === 1 ? 0 : 1,
      }),
    );
  };

  const { examList, loading } = pageState;

  useEffect(() => {
    dispatch(getMajorsExamList(visibleProps.majorsId));
  }, [dispatch]);

  const associationStatusRow = (row) => {
    const isRelated = statusNameListMap[row === 0 || row === 1 ? row : 'default'];
    return (
      <Tag shape='round' theme={isRelated.theme} variant='light-outline'>
        {isRelated.label}
      </Tag>
    );
  };

  const operationRow = (row) => (
    <>
      <Button theme='primary' variant='text' onClick={() => associationStatus(row)}>
        {row.isRelated === 0 ? '关联类目' : '取消关联'}
      </Button>
    </>
  );

  const columns: () => TableProps['columns'] = () => [
    { colKey: 'examName', title: '考试类目' },
    {
      colKey: 'isRelated',
      title: '关联状态',
      cell: ({ row: { isRelated: association } }) => associationStatusRow(association),
    },
    {
      colKey: 'operation',
      title: '操作',
      width: 240,
      cell: ({ row }) => operationRow(row),
    },
  ];

  const rehandleExpandChange: TableProps['onExpandChange'] = (value) => setExpandedRowKeys(value as any);

  return (
    <Table
      loading={loading}
      bordered
      rowKey='examId'
      columns={columns()}
      data={examList}
      expandedRowKeys={expandedRowKeys}
      expandOnRowClick={false}
      onExpandChange={rehandleExpandChange}
      lazyLoad
      resizable
      expandIcon={false}
    ></Table>
  );
}
