import { PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getMajorsCategoryListApi,
  addOrUpdateMajorsCategoryApi,
  deleteMajorsCategoryApi,
  getMajorsListApi,
  addOrUpdateMajorsApi,
  getMajorsExamListApi,
  addOrUpdateMajorsExamApi,
  deleteMajorsApi,
  getMajorsInfoApi,
} from 'api';

import type {
  IGetMajorsCategoryListApi,
  IGetMajorsListApi,
  IAddOrUpdateMajorsApi,
  IAddOrUpdateMajorsExamApi,
} from 'api';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';

const namespace = 'speciality';

const initialState = {
  loading: true,
  formLoading: false,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  list: [],
  professionalList: [],
  active: 0,
  examList: [],
  majorsInfo: {},
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getMajorsCategoryList = useAsyncThunkWithStatus(`getMajorsCategoryList`, async (params, { getState }) => {
  const {
    data: { rows },
  } = await getMajorsCategoryListApi(params as IGetMajorsCategoryListApi);
  const state = getState() as RootState;
  const currentActive = state.association.active;

  if (rows && rows.length > 0 && !currentActive) {
    return {
      rows,
      active: rows[0].majorsCategoryId,
    };
  }
  return { rows };
});

export const addOrUpdateMajorsCategory = useAsyncThunkWithStatus(
  `addOrUpdateMajorsCategory`,
  async (params, { dispatch }) => {
    await addOrUpdateMajorsCategoryApi(params);
    await dispatch(getMajorsCategoryList({ majorsCategoryName: '' }));
  },
);

export const deleteMajorsCategory = useAsyncThunkWithStatus(`deleteMajorsCategory`, async (id, { dispatch }) => {
  await deleteMajorsCategoryApi(id as number);
  await dispatch(getMajorsCategoryList({ majorsCategoryName: '' }));
});

export const addOrUpdateMajors = useAsyncThunkWithStatus(`addOrUpdateMajors`, async (params) => {
  await addOrUpdateMajorsApi(params as IAddOrUpdateMajorsApi);
});

export const getMajorsList = useAsyncThunkWithStatus(`getMajorsList`, async (params) => {
  const {
    data: { rows, total },
  } = await getMajorsListApi(params as IGetMajorsListApi);
  return {
    rows,
    total,
    pageNum: params.pageNum,
    pageSize: params.pageSize,
  };
});

export const getMajorsExamList = useAsyncThunkWithStatus(`getMajorsExamList`, async (id) => {
  const {
    data: { rows },
  } = await getMajorsExamListApi(id as number);
  return {
    rows,
  };
});

export const addOrUpdateMajorsExam = useAsyncThunkWithStatus(`addOrUpdateMajorsExam`, async (params, { dispatch }) => {
  await addOrUpdateMajorsExamApi(params as IAddOrUpdateMajorsExamApi);
  await dispatch(getMajorsExamList(params.majorsId));
});

export const deleteMajors = useAsyncThunkWithStatus(`deleteMajors`, async (params, { getState, dispatch }) => {
  const {
    speciality: { active, pageSize, pageNum },
  } = getState() as RootState;
  await deleteMajorsApi(params as IAddOrUpdateMajorsExamApi);
  await dispatch(
    getMajorsList({
      categoryId: active,
      pageNum,
      pageSize,
    }),
  );
});

export const getMajorsInfo = useAsyncThunkWithStatus(`getMajorsInfo`, async (id: number) => {
  const { data } = await getMajorsInfoApi(id);

  return data;
});

const specialitySlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setActive: (state, action: PayloadAction<number>) => {
      state.active = action.payload;
    },
  },
  cases: [
    {
      thunk: getMajorsCategoryList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.professionalList = action.payload.rows;
        state.active = action.payload?.active;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: addOrUpdateMajorsCategory,
      pending: (state) => {
        state.formLoading = true;
      },
      fulfilled: (state) => {
        state.formLoading = false;
      },
      rejected: (state) => {
        state.formLoading = false;
      },
    },

    {
      thunk: getMajorsList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.list = action.payload.rows;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.total = action.payload.total;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },

    {
      thunk: addOrUpdateMajors,
      pending: (state) => {
        state.formLoading = true;
      },
      fulfilled: (state) => {
        state.formLoading = false;
      },
      rejected: (state) => {
        state.formLoading = false;
      },
    },
    {
      thunk: getMajorsExamList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.examList = action.payload.rows;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getMajorsInfo,
      pending: (state) => {
        state.formLoading = true;
      },
      fulfilled: (state, action) => {
        state.majorsInfo = action.payload;
        state.formLoading = false;
      },
      rejected: (state) => {
        state.formLoading = false;
      },
    },
  ],
});

export const { clearPageState, setActive } = specialitySlice.actions;

export const selectSpeciality = (state: RootState) => state.speciality;

export default specialitySlice.reducer;
