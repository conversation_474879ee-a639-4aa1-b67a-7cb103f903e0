import React, { useEffect, useState } from 'react';
import { AddIcon } from 'tdesign-icons-react';
import { Input, Space, Tag, type InputProps } from 'tdesign-react';

interface CustomTagProps {
  value: Array<string>;
  onChange: (val: Array<string>) => void;
  tips?: string; // 可选的 tips 提示信息
  status?: InputProps['status']; // 继承 tdesign Input 的 status 类型
  placeholder?: string;
}

const CustomTag: React.FC<CustomTagProps> = ({ value, onChange, tips, status, placeholder }) => {
  const [tagList, setTagList] = useState(value || []);
  const [inputVisible, toggleInputVisible] = useState(false);

  useEffect(() => {
    onChange(tagList);
    toggleInputVisible(false);
  }, [tagList]);

  const deleteTag = (i: number) => {
    tagList.splice(i, 1);
    setTagList([...tagList]);
  };

  const handleInputEnter = (val: string) => {
    toggleInputVisible(false);
    if (val) setTagList((list) => list.concat([val]));
  };

  return (
    <Space direction='vertical' size='small'>
      {tagList.map((name, i) => (
        <Tag
          shape='round'
          size='medium'
          key={i}
          closable
          onClose={() => {
            deleteTag(i);
          }}
        >
          {name}
        </Tag>
      ))}
      <div style={{ display: 'flex', cursor: 'pointer' }}>
        {inputVisible ? (
          <Input
            onBlur={handleInputEnter}
            onEnter={handleInputEnter}
            style={{ width: '94px' }}
            placeholder={placeholder}
            autofocus
            clearable
          />
        ) : (
          <Tag
            shape='round'
            onClick={() => {
              toggleInputVisible(true);
            }}
            icon={<AddIcon />}
          >
            添加
          </Tag>
        )}
      </div>
      {status && <div className={`t-input__tips t-input__tips--${status}`}>{tips}</div>}
    </Space>
  );
};

export default CustomTag;
