interface IOption {
  value: number | string;
  label: string;
}

/**
 * '编辑状态' 枚举
 * @type {{EDITED: number, UNEDITED: number}}
 */
export const EDIT_STATUS = {
  EDITED: 1,
  UNEDITED: 0,
};

/**
 * '适用对象' 枚举
 * @type {{UNIVERSAL: number, THREE_SCHOOL: number, SOCIAL: number}}
 */
export const APPLY_OBJECT = {
  UNIVERSAL: 1,
  THREE_SCHOOL: 2,
  SOCIAL: 3,
};

export const APPLY_OBJECT_LIST: Array<IOption> = [
  {
    value: APPLY_OBJECT.UNIVERSAL,
    label: '普高生',
  },
  {
    value: APPLY_OBJECT.THREE_SCHOOL,
    label: '三校生',
  },
  {
    value: APPLY_OBJECT.SOCIAL,
    label: '社会考生',
  },
];

/**
 * '考试类型' 枚举
 * @type {{EXAM_TYPE_1: number, EXAM_TYPE_2: number, EXAM_TYPE_3: number}}
 */
export const APPLIED_SCHOOLS = {
  VOCATIONAL_COLLEGES: 1,
  UNDERGRAD_COLLEGES: 2,
};

export const APPLIED_SCHOOLS_LIST: Array<IOption> = [
  {
    value: APPLIED_SCHOOLS.VOCATIONAL_COLLEGES,
    label: '高职院校',
  },
  {
    value: APPLIED_SCHOOLS.UNDERGRAD_COLLEGES,
    label: '本科院校',
  },
];

interface IOption {
  value: number | string;
  label: string;
}

// 合同状态枚举
export const CONTRACT_STATUS = {
  FAIL: 0,
  AUDIT_PENDING: 1,
  EXEC_PENDING: 2,
  EXECUTING: 3,
  FINISH: 4,
};

export const CONTRACT_STATUS_OPTIONS: Array<IOption> = [
  { value: CONTRACT_STATUS.FAIL, label: '审核失败' },
  { value: CONTRACT_STATUS.AUDIT_PENDING, label: '待审核' },
  { value: CONTRACT_STATUS.EXEC_PENDING, label: '待履行' },
  { value: CONTRACT_STATUS.EXECUTING, label: '审核成功' },
  { value: CONTRACT_STATUS.FINISH, label: '已完成' },
];

// 合同类型枚举
export const CONTRACT_TYPES = {
  MAIN: 0,
  SUB: 1,
  SUPPLEMENT: 2,
};

export const CONTRACT_TYPE_OPTIONS: Array<IOption> = [
  { value: CONTRACT_TYPES.MAIN, label: '主合同' },
  { value: CONTRACT_TYPES.SUB, label: '子合同' },
  { value: CONTRACT_TYPES.SUPPLEMENT, label: '补充合同' },
];
