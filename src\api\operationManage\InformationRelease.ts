import request from 'utils/request/index';

export interface IGetInformationReleaseListApi {
  pageNum: number;
  pageSize: number;
  title?: string;
  videoNumberId?: string | number;
  videoFeedId?: string | number;
}

/**
 * 分页条件查询资讯列表
 * @returns
 */
export const getInformationReleaseListApi = async (params: IGetInformationReleaseListApi) => {
  const result = await request.get({
    url: '/info/getInfoList',
    params,
  });
  return result;
};

/**
 * 删除资讯信息
 * @returns
 */
export const deletInformationReleaseApi = async (infoId: number) => {
  const result = await request.delete({
    url: `/info/deleteInfo/${infoId}`,
  });
  return result;
};

export interface IAddOrUpdateInformationReleaseApi {
  /** 资讯id(修改传) */
  infoId: number;

  /** 资讯标题 */
  title: string;

  /** 资讯封面(文件id) */
  cover: string;

  /** 视频号id */
  videoNumberId: string;

  /** 视频feedID */
  videoFeedId: string;

  /** 考试id集合 */
  examIdList: number[];
}

/**
 * 添加/修改资讯
 * @returns
 */
export const addOrUpdateInformationReleaseApi = async (params: IAddOrUpdateInformationReleaseApi) => {
  const result = await request.post({
    url: `/info/addOrUpdateInfo`,
    params,
  });
  return result;
};

export interface IOpenOrCloseInfoApi {
  /** 资讯id */
  infoId: number;

  /** 是否开启(1-是，0-否) */
  isOpen: number;
}

/**
 * 开启/关闭资讯信息
 * @returns
 */
export const openOrCloseInfoApi = async (params: IOpenOrCloseInfoApi) => {
  const result = await request.post({
    url: `/info/openOrCloseInfo?infoId=${params.infoId}&isOpen=${params.isOpen}`,
  });
  return result;
};
