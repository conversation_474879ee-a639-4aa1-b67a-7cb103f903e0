import React from 'react';
import PdfPreview from './PdfPreview';
import OfficePreview from './OfficePreview';
import { strIsNull } from 'utils/tool';

const DocumentPreview: React.FC<{ fileUrl: string }> = ({ fileUrl }) => {
  const getFileExtension = (url: string) => url.split('.').pop()?.toLowerCase();

  if (strIsNull(fileUrl)) return <div>文件Url不能为空</div>;

  const fileExtension = getFileExtension(fileUrl);

  if (fileExtension === 'pdf') {
    return <PdfPreview fileUrl={fileUrl} />;
  }

  if (['doc', 'docx', 'ppt', 'pptx'].includes(fileExtension as string)) {
    return <OfficePreview fileUrl={fileUrl} />;
  }

  return <div>不支持的文件类型</div>;
};

export default DocumentPreview;
