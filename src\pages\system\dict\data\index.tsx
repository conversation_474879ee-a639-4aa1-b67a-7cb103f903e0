import React, { Fragment, memo, useEffect, useRef, useState } from 'react';
import { Button, DialogPlugin, Popconfirm, Row, Space, TableProps, Tag } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  selectSystemDict,
  getSystemDictDataList,
  getSystemDictDetailsInfo,
  delSystemDictData,
  getSystemDictAllList,
} from 'modules/system/dict';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IRef } from 'components/Form';
import { IGetDictDataListBo } from 'types/system';
import PermissionButton from 'components/PermissionButton';
import { Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { useParams, useNavigate } from 'react-router-dom';
import { downLoad, strIsNull } from 'utils/tool';

const DictDataTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { dictId } = useParams();
  const { loading, list, pageNum, pageSize, total, delLoading, info, allList } = useAppSelector(selectSystemDict);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetDictDataListBo = { pageNum, pageSize, dictType: '' };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'row-select',
    'dictCode',
    'dictLabel',
    'dictValue',
    'dictSort',
    'status',
    'remark',
    'createBy',
    'createTime',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['remark', 'createTime', 'createBy', 'updateTime', 'updateBy'];

  /**
   * 删除字典
   */
  const handleDelete = async (dictCode: number) => {
    const { type } = await dispatch(delSystemDictData(dictCode));

    if (type.endsWith('fulfilled')) {
      dispatch(getSystemDictDataList(searchParams));
    }
  };

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  /**
   * 批量删除字典
   */
  const handleBatchDelete = async () => {
    const confirmDia = DialogPlugin.confirm({
      header: '批量删除提示',
      body: '确定批量删除该数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        const { type } = await dispatch(delSystemDictData(selectedRowKeys.join(',')));

        if (type.endsWith('fulfilled')) {
          handleResetSelection();
          confirmDia.hide();
          dispatch(getSystemDictDataList(searchParams));
        }
      },
      onClose: () => {
        confirmDia.hide();
      },
    });

    return false;
  };

  /**
   * 导出字典
   */
  const handleExport = async () => {
    try {
      setExportLoading(true);

      const params = searchRef.current?.getFormParams();

      await downLoad(
        '/system/dict/data/export',
        {
          ...params,
        },
        `字典数据_${new Date().getTime()}`,
      );

      setExportLoading(false);
    } catch (error) {
      setExportLoading(false);
    }
  };

  /**
   * 关闭
   */
  const handleClose = () => {
    navigate('/system/dict');
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    {
      colKey: 'dictCode',
      width: 120,
      title: '字典编码',
    },
    {
      colKey: 'dictLabel',
      title: '字典标签',
      cell({ row }) {
        return (
          <Fragment>
            {(strIsNull(row.listClass) || row.listClass === 'default') && strIsNull(row.cssClass) ? (
              <span>{row.dictLabel}</span>
            ) : (
              <Tag
                theme={row.listClass === 'info' ? undefined : row.listClass}
                className={row.cssClass}
                variant='light'
              >
                {row.dictLabel}
              </Tag>
            )}
          </Fragment>
        );
      },
    },
    {
      colKey: 'dictValue',
      title: '字典键值',
    },
    {
      colKey: 'dictSort',
      title: '字典排序',
    },
    {
      colKey: 'status',
      title: '状态',
      cell({ row }) {
        return (
          <Tag theme={row.status === '1' ? 'danger' : 'primary'} variant='light'>
            {row.status === '1' ? '停用' : '正常'}
          </Tag>
        );
      },
    },
    {
      colKey: 'remark',
      title: '备注',
    },
    {
      colKey: 'updateTime',
      title: '修改时间',
    },
    {
      colKey: 'updateBy',
      title: '修改人',
    },
    {
      colKey: 'createTime',
      title: '创建时间',
    },
    {
      colKey: 'createBy',
      title: '创建人',
    },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['system:dict:edit']}
              onClick={() =>
                navigate('/system/dict/data/edit', {
                  state: { dictCode: row.dictCode, dictType: info.dictType, dictId },
                })
              }
            >
              修改
            </PermissionButton>
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleDelete(row.dictCode)}>
              <>
                <PermissionButton permissions={['system:dict:remove']} theme='danger' loading={delLoading}>
                  删除
                </PermissionButton>
              </>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    dispatch(getSystemDictAllList());
  }, []);

  useEffect(() => {
    if (!strIsNull(dictId)) {
      dispatch(getSystemDictDetailsInfo(dictId));
    }
  }, [dictId]);

  useEffect(() => {
    if (!strIsNull(info)) {
      searchRef.current?.setFormParams({ dictType: info.dictType });
      searchParams.dictType = info.dictType;

      dispatch(getSystemDictDataList(searchParams));
    }
  }, [info]);

  return (
    <div className={classnames(CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getSystemDictDataList}
        params={searchParams}
        list={[
          {
            type: 'select',
            label: '字典名称',
            field: 'dictType',
            options: allList,
            nameField: 'dictName',
            valueField: 'dictType',
          },
          {
            type: 'input',
            label: '字典标签',
            field: 'dictLabel',
          },
          {
            type: 'select',
            label: '状态',
            field: 'status',
            options: [
              {
                label: '正常',
                value: '0',
              },
              {
                label: '停用',
                value: '1',
              },
            ],
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            permissions={['system:dict:add']}
            content='添加'
            variant='outline'
            onClick={() => navigate('/system/dict/data/add', { state: { dictType: info.dictType, dictId } })}
          />
          <PermissionButton
            disabled={selectedRowKeys.length === 0}
            permissions={['system:dict:remove']}
            content='批量删除'
            variant='outline'
            theme='danger'
            onClick={() => handleBatchDelete()}
          />
          <PermissionButton
            loading={exportLoading}
            permissions={['system:dict:export']}
            content='导出'
            variant='outline'
            theme='warning'
            onClick={() => handleExport()}
          />
          <Button content='关闭' variant='outline' theme='warning' onClick={() => handleClose()} />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'dictCode',
          selectedRowKeys,
          onSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getSystemDictDataList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(DictDataTable);
