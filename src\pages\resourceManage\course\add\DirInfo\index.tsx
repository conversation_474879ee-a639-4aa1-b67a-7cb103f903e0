import React, { useEffect, useRef, useState } from 'react';
import {
  selectResourceManageCourse,
  getCourseInfo,
  getChapterTreeList,
  deleteResourceCourseChapter,
  editResourceCourseChapter,
} from 'modules/resourceManage/course';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  Button,
  Dialog,
  Empty,
  Loading,
  Popconfirm,
  Space,
  Switch,
  Tree,
  TreeNodeModel,
  TreeProps,
} from 'tdesign-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { strIsNull } from 'utils/tool';
import VideoForm from '../../components/VideoForm';
import EditAddForm, { IEditAddFormRef } from '../../components/EditAddForm';
import { AddCircleIcon, DeleteIcon, EditIcon, Icon } from 'tdesign-icons-react';
import { IEditResourceCourseChapterBo } from 'types/resourceManage';
import TcVideoPlayer, { IRef, TcVideoPlayerProps } from 'components/TcVideoPlayer';

interface AddCourseDirInfoProps {
  courseId: number;
}

const AddCourseDirInfo: React.FC<AddCourseDirInfoProps> = ({ courseId }) => {
  const dispatch = useAppDispatch();
  const {
    courseInfo,
    courseInfoLoading,
    chapterTreeLoading,
    chapterTreeList,
    addChapterLoading,
    editChapterLoading,
    deleteChapterLoading,
  } = useAppSelector(selectResourceManageCourse);
  const [lessonsNum, setLessonsNum] = useState(0);
  const editAddFormRef = useRef<IEditAddFormRef | null>(null);
  const playerRef = useRef<IRef | null>(null);
  const [visible, setVisible] = useState(false);
  const [title, setTitle] = useState('');
  const [chapterId, setChapterId] = useState<number | null>(null);
  const [type, setType] = useState('');
  const [active, setActive] = useState(0);
  const [options, setOptions] = useState({});

  useEffect(() => {
    dispatch(getCourseInfo(courseId));
    dispatch(getChapterTreeList(courseId));
  }, []);

  useEffect(() => {
    if (!strIsNull(courseInfo)) {
      setLessonsNum(courseInfo.hoursNumber || 0);
    }
  }, [courseInfo]);

  const renderActiveContent = () => <Icon name='check' />;
  const renderInactiveContent = () => <Icon name='close' />;

  const handleAddChapter = async (id: number) => {
    setType('add');
    setChapterId(id);
    setTitle('添加章节');
    setVisible(true);
  };

  const handleEditChapter = async (id: number) => {
    setType('edit');
    setChapterId(id);
    setTitle('编辑章节');
    setVisible(true);
  };

  const handleTryAndSeeChange = async (tryAndSee: 0 | 1, id: number) => {
    const bo: IEditResourceCourseChapterBo = {
      id,
      tryAndSee,
    };

    const { type } = await dispatch(editResourceCourseChapter(bo));

    if (type.endsWith('fulfilled')) {
      dispatch(getChapterTreeList(courseId));
    }
  };

  const handleClose = () => {
    editAddFormRef.current?.reset();
    setVisible(false);
  };

  const handleConfirm = async () => {
    try {
      await editAddFormRef.current?.submit();
      if (type === 'add' && chapterId !== 0) {
        setLessonsNum(lessonsNum + 1);
      }
      dispatch(getChapterTreeList(courseId));
      setChapterId(null);
      setVisible(false);
      setType('');
    } catch (e: any) {
      console.log(e);
    }
  };

  const handleDelete = async (id: number) => {
    const { type } = await dispatch(deleteResourceCourseChapter(id));

    if (type.endsWith('fulfilled')) {
      dispatch(getChapterTreeList(courseId));
    }

    playerRef.current?.onDestroy();
    setActive(0);
    setOptions(0);
  };

  const handleTreeClick: TreeProps['onClick'] = ({ node }) => {
    playerRef.current?.onDestroy();

    if (node.data.parentId !== 0) {
      setActive(node.value as number);
    } else {
      setActive(0);
    }

    setOptions({});
  };

  const renderOperations = (node: TreeNodeModel) => (
    <>
      {node.data.parentId === 0 && (
        <Button
          size='small'
          variant='text'
          icon={<AddCircleIcon />}
          ghost
          onClick={() => handleAddChapter(node.value as number)}
        />
      )}
      {node.data.parentId !== 0 && (
        <Switch
          loading={editChapterLoading}
          value={node.data.tryAndSee}
          onChange={(val) => handleTryAndSeeChange(val, node.value as number)}
          customValue={[1, 0]}
          label={[renderActiveContent(), renderInactiveContent()]}
        />
      )}
      <Button
        size='small'
        variant='text'
        icon={<EditIcon />}
        ghost
        onClick={() => handleEditChapter(node.value as number)}
      />
      <Popconfirm content='确定要删除该章节吗？' theme='danger' onConfirm={() => handleDelete(node.value as number)}>
        <>
          <Button loading={deleteChapterLoading} size='small' variant='text' icon={<DeleteIcon />} ghost />
        </>
      </Popconfirm>
    </>
  );

  const handleChangeVideo = (val: any) => {
    playerRef.current?.onDestroy();

    setOptions({
      id: active,
      appID: val.appID,
      fileID: val.fileID,
      psign: val.psign,
    });
  };

  return (
    <Loading loading={courseInfoLoading} showOverlay>
      <div
        className={classnames(CommonStyle.pageWithColor)}
        style={{ paddingLeft: 0, paddingRight: 0, paddingBottom: 0 }}
      >
        <div style={{ display: 'flex', columnGap: '30px', height: '100%' }}>
          <div
            style={{
              width: '350px',
              border: '1px solid var(--td-border-level-1-color)',
              padding: '20px',
              display: 'flex',
              flexDirection: 'column',
              rowGap: '10px',
            }}
          >
            <Button theme='primary' variant='base' onClick={() => handleAddChapter(0)} style={{ width: '100px' }}>
              添加章
            </Button>

            <Loading loading={chapterTreeLoading} size='small' style={{ flex: 'auto' }}>
              <Tree
                data={chapterTreeList}
                keys={{ value: 'id', label: 'chapterName', children: 'children' }}
                icon
                empty={<Empty />}
                operations={renderOperations}
                activable
                hover
                onClick={handleTreeClick}
                expandOnClickNode
              />
            </Loading>
          </div>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              flex: 'auto',
              rowGap: '10px',
            }}
          >
            <Space size={80}>
              <span>
                <span>课程名称：</span>
                <span>{courseInfo.courseName}</span>
              </span>
              <span>
                <span>课时数：</span>
                <span>{lessonsNum}</span>
              </span>
            </Space>
            {active !== 0 && (
              <div
                style={{
                  display: 'flex',
                  columnGap: '40px',
                  flex: 'auto',
                  padding: '10px 20px',
                  border: '1px solid var(--td-border-level-1-color)',
                }}
              >
                <VideoForm chapterId={active} onChangeVideo={handleChangeVideo} />
                {!strIsNull(options) && (
                  <TcVideoPlayer
                    ref={playerRef}
                    options={options as TcVideoPlayerProps['options']}
                    playerTarget={() => { }}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      <Dialog
        width='800px'
        header={title}
        visible={visible}
        closeOnEscKeydown={false}
        closeOnOverlayClick={false}
        confirmOnEnter={true}
        destroyOnClose={true}
        preventScrollThrough={true}
        confirmBtn={{ content: '确认', loading: addChapterLoading || editChapterLoading }}
        cancelBtn={{ content: '取消', onClick: handleClose }}
        onCancel={handleClose}
        onConfirm={handleConfirm}
        onClose={handleClose}
      >
        {visible && <EditAddForm ref={editAddFormRef} type={type} chapterId={chapterId} courseId={courseId} />}
      </Dialog>
    </Loading>
  );
};

export default AddCourseDirInfo;
