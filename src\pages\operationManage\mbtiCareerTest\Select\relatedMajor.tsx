import { useAppDispatch, useAppSelector } from 'modules/store';
import React, { useState, useEffect } from 'react';
import { Transfer, Button, MessagePlugin } from 'tdesign-react';
import Style from './index.module.less';
import { getMbtiExamTypeDetail, selectMbtiAdminExam, mbtiExamTypeRelatedMajor } from 'modules/operationManage/mbti';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface ITableExpandable {
  visibleProps?: object;
  cancellation: () => void;
  examId: Number;
  visible: Boolean
}
export default function TableExpandable({ visibleProps, examId, cancellation, visible }: ITableExpandable) {
  const dispatch = useAppDispatch();
  const [list, setList] = useState([]);
  const [info, setInfo] = useState({});
  const [value, setValue] = useState([]);
  const getList = async () => {

    const {
      payload: { list },
    } = await dispatch(
      getMbtiExamTypeDetail({
        mbtiPersonalityId: visibleProps.id,
        examId,
      }))
    setList(list.majorsVoAllList)
    setValue(list.majorsIdList ? list.majorsIdList : [])
    setInfo(list)
  };
  useEffect(() => {
    getList()
  }, [visible]);
  const onsubmit = () => {
    dispatch(
      mbtiExamTypeRelatedMajor({
        mbtiPersonalityId: visibleProps?.id,
        examId,
        majorIdList: value
      }));
    MessagePlugin.success('修改成功！');
    cancellation()
  }
  return (
    <>
      <div className={Style.headline}>性格特点：</div>
      <div className={Style.text}>{info.traits}</div>
      <div className={Style.headline}>适合专业领域：</div>
      <div className={Style.text}>{info.suitableFields}</div>
      <div className={Style.headline}>适合职业：</div>
      <div className={Style.text}>{info.profession}</div>
      <div className={Style.headline}>关联专业：</div>
      <Transfer
        data={list}
        value={value}
        onChange={(v) => {
          setValue(v);
        }}
      ></Transfer>
      <div className={Style.btn}>
        <Button theme='default' variant='outline' onClick={() => cancellation()}>
          取消
        </Button>
        <Button theme='primary' variant='base' onClick={onsubmit} style={{ marginLeft: 10 }} >
          确定
        </Button>
      </div>

    </>
  );
}
