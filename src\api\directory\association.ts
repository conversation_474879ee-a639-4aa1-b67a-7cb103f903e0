import request from 'utils/request/index';
/**
 * 院校/专业关联
 */

/**
 * 查询所有科目
 * @returns
 */
export const getEducationalInstitutionsNameAndIdApi = async (educationalName: string | null | undefined) => {
  const result = await request.get({
    url: '/educationalInstitutions/getEducationalInstitutionsNameAndId',
    params: { educationalName },
  });
  return result;
};

/**
 * 查询院校专业关联的考试列表
 * @param id
 * @returns
 */
export const getEducationAlMajorsExamListApi = async (id?: number) => {
  const result = await request.get({
    url: `/educationalMajors/getEducationAlMajorsExamList?educationalMajorsId=${id ?? ''}`,
  });
  return result;
};

export interface IGetEducationalMajorsListApi {
  educationalId: number;
  pageSize: number;
  pageNum: number;
  categoryId?: number;
  majorsName?: string;
}

/**
 * 分页条件查询院校关联的专业列表
 * @param params
 * @returns
 */
export const getEducationalMajorsListApi = async (params: IGetEducationalMajorsListApi) => {
  const result = await request.get({
    url: `/educationalMajors/getEducationalMajorsList`,
    params,
  });
  return result;
};

export interface IGetUnrelatedMajorsListApi {
  /** 院校id */
  educationalId: number;
  /** 专业名称 */
  majorsName: string;
  pageNum: number;
  pageSize: number;
}

/**
 * 分页条件查询需要关联的专业列表
 * @param params
 * @returns
 */
export const getUnrelatedMajorsListApi = async (params: IGetUnrelatedMajorsListApi) => {
  const result = await request.get({
    url: `/educationalMajors/getMajorsList`,
    params,
  });
  return result;
};

interface IEducationalMajorsExamInfoBoList {
  /** 考试id */
  examId: number;
  /** 招生人数 */
  enrollmentNumber: number;
  /** 衔接前置学历/专业 */
  connectPreEducationMajor: string;
}

interface IEditEducationalMajorsExamInfoBoList {
  /** 院校专业关联考试id */
  educationalMajorsExamId: number;
  /** 招生人数 */
  enrollmentNumber: number;
  /** 衔接前置学历/专业 */
  connectPreEducationMajor: string;
}
export interface IAddEducationalMajorsApi {
  /** 院校id */
  educationalId: number;
  /** 专业id */
  majorsId?: number;
  /** 学制 */
  schoolSystem: number;
  /** 学费 */
  tuition: number;
  /** 院校专业关联考试信息集合 */
  educationalMajorsExamInfoBoList: IEducationalMajorsExamInfoBoList[];
}
export interface IEditEducationalMajorsApi {
  /** 院校专业id */
  educationalMajorsId: number;
  /** 学制 */
  schoolSystem: number;
  /** 学费 */
  tuition: number;
  /** 院校专业关联考试信息集合 */
  educationalMajorsExamInfoBoList: IEditEducationalMajorsExamInfoBoList[];
}

/**
 * 院校关联专业(院校底下新增专业)
 * @param params
 * @returns
 */
export const addEducationalMajorsApi = async (params: IAddEducationalMajorsApi) => {
  const result = await request.post({
    url: `/educationalMajors/addEducationalMajors`,
    params,
  });
  return result;
};

/**
 * 编辑院校关联的专业
 * @param params
 * @returns
 */
export const updateEducationalMajorsApi = async (params: IEditEducationalMajorsApi) => {
  const result = await request.put({
    url: `/educationalMajors/updateEducationalMajors`,
    params,
  });
  return result;
};

export interface IAddOrDeleteEducationalMajorsExamApi {
  educationalMajorsId: number;
  examId: number;
  isRelated: 1 | 0;
}

/**
 * 院校专业关联/取消关联考试
 * @param params
 * @returns
 */
export const addOrDeleteEducationalMajorsExamApi = async (params: IAddOrDeleteEducationalMajorsExamApi) => {
  const result = await request.post({
    url: `/educationalMajors/addOrDeleteEducationalMajorsExam`,
    params,
  });
  return result;
};

export interface IDeleteEducationalMajorsApi {
  educationalId: number;
  majorsIdList: (string | number)[];
}

/**
 *  院校取消关联/批量取消关联专业
 * @param params
 * @returns
 */
export const deleteEducationalMajorsApi = async (params: IDeleteEducationalMajorsApi) => {
  const result = await request.delete({
    url: `/educationalMajors/deleteEducationalMajors`,
    params,
  });
  return result;
};

/**
 * 获取学生角色下拉框
 * @returns
 */
export const dropDownOptionApi = async () => {
  const result = await request.get({
    url: `/studentRole/dropDownOption`,
  });
  return result;
};

export interface IEducationalMajorsDragSortApi {
  id: number;
  position: number;
  parentId: number;
}

/**
 *  院校关联的专业列表拖动排序
 * @returns
 */
export const educationalMajorsDragSortApi = async (params: IEducationalMajorsDragSortApi) => {
  const result = await request.put({
    url: `/educationalMajors/educationalMajorsDragSort`,
    params,
  });
  return result;
};
