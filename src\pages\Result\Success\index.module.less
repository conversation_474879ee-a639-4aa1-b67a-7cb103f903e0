.Content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 75vh;

  .icon {
    font-size: 72px;
    color: var(--td-success-color);
  }

  .title {
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    margin-top: 28px;
    color: var(--td-text-color-primary);
  }
  .description {
    margin: 8px 0 32px;
    font-size: 14px;
    line-height: 22px;
    color: var(--td-text-color-secondary);
  }

  .rightButton {
    margin-left: 8px;
  }
}
