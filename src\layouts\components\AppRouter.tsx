import React, { ReactNode, Suspense, memo, useEffect, useState } from 'react';
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { Layout, Loading } from 'tdesign-react';
import routers, { IRouter } from 'router';
import { resolve } from 'utils/path';
import Page from './Page';
import Style from './AppRouter.module.less';
import { getRouters } from 'router/getRouter';
import getToken from 'utils/getToken';
import { mergeRoutes } from 'utils/routers';

const { Content } = Layout;

export interface IBreadcrumb {
  title: string;
  path: string;
}

type TRenderRoutes = (routes: IRouter[], parentPath?: string, breadcrumbs?: IBreadcrumb[]) => ReactNode[];

/**
 * 渲染应用路由
 * @param routes
 * @param parentPath
 * @param breadcrumbs
 */
const renderRoutes: TRenderRoutes = (routes, parentPath = '', breadcrumbs = []) =>
  routes &&
  routes.map((route, index: number) => {
    const { Component, children, redirect, meta } = route;

    const currentPath = resolve(parentPath, route.path);
    let currentBreadcrumb = breadcrumbs;

    if (meta?.title) {
      currentBreadcrumb = currentBreadcrumb.concat([
        {
          title: meta?.title,
          path: currentPath,
        },
      ]);
    }

    if (redirect) {
      // 重定向
      return <Route key={index} path={currentPath} element={<Navigate to={redirect} replace />} />;
    }

    if (Component) {
      return (
        <Route
          key={index}
          path={currentPath}
          element={
            <Page isFullPage={route.isFullPage} breadcrumbs={currentBreadcrumb}>
              <Component />
            </Page>
          }
        />
      );
    }
    // 无路由菜单
    return children ? renderRoutes(children, currentPath, currentBreadcrumb) : null;
  });

const AppRouter = () => {
  const navigate = useNavigate();

  const [dynamicRoutes, setDynamicRoutes] = useState<IRouter[]>([]);
  const fetchRouters = async () => {
    const USER_ROUTER = getRouters();
    setDynamicRoutes(USER_ROUTER);
  };

  useEffect(() => {
    if (!getToken()) {
      navigate('/login');
    } else {
      fetchRouters();
    }
  }, []);

  return (
    <Content>
      <Suspense
        fallback={
          <div className={Style.loading}>
            <Loading />
          </div>
        }
      >
        <Routes>{renderRoutes([...routers, ...dynamicRoutes])}</Routes>
      </Suspense>
    </Content>
  );
};

export default memo(AppRouter);
