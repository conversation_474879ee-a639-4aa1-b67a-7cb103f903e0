import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, MessagePlugin, Input, Checkbox, Button, FormInstanceFunctions, SubmitContext } from 'tdesign-react';
import { LockOnIcon, UserIcon, BrowseOffIcon, BrowseIcon } from 'tdesign-icons-react';
import classnames from 'classnames';
import { useAppDispatch } from 'modules/store';
import { login, getUserInfo, getRouters } from 'modules/user';
import { getCode } from 'api';

import Style from './index.module.less';

const { FormItem } = Form;

export type ELoginType = 'password' | 'phone' | 'qrcode';

export default function Login() {
  const [loginType] = useState<ELoginType>('password');
  const [showPsw, toggleShowPsw] = useState(false);
  const formRef = useRef<FormInstanceFunctions>();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [uuid, setUuid] = useState('');
  const [imageSrc, setImageSrc] = useState('');
  interface MessagePlugin {
    captchaEnabled: boolean;
    code: number;
    img: string;
    msg: string;
    uuid: string;
  }

  const fetchData = () => {
    getCode().then((code: MessagePlugin) => {
      setImageSrc(`data:image/gif;base64,${code.img}`);
      setUuid(code.uuid);
    });
  };

  const onSubmit = async (e: SubmitContext) => {
    if (e.validateResult === true) {
      try {
        const formValue = formRef.current?.getFieldsValue?.(true) || {};
        formValue.uuid = uuid;
        await dispatch(login(formValue)).unwrap();
        await dispatch(getUserInfo()).unwrap();
        const routers = await dispatch(getRouters()).unwrap();
        navigate(routers ? routers[0].path : '/');
        MessagePlugin.success('登录成功');
      } catch (e) {
        fetchData();
      }
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      formRef.current?.submit?.();
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div onKeyDown={handleKeyDown}>
      <Form
        ref={formRef}
        className={classnames(Style.itemContainer, `login-${loginType}`)}
        labelWidth={0}
        onSubmit={onSubmit}
      >
        {loginType === 'password' && (
          <>
            <FormItem name='username' rules={[{ required: true, message: '账号必填', type: 'error' }]}>
              <Input size='large' placeholder='请输入账号' prefixIcon={<UserIcon />} onKeydown={handleKeyDown} />
            </FormItem>
            <FormItem name='password' rules={[{ required: true, message: '密码必填', type: 'error' }]}>
              <Input
                size='large'
                type={showPsw ? 'text' : 'password'}
                placeholder='请输入登录密码'
                prefixIcon={<LockOnIcon />}
                suffixIcon={
                  showPsw ? (
                    <BrowseIcon onClick={() => toggleShowPsw((current) => !current)} />
                  ) : (
                    <BrowseOffIcon onClick={() => toggleShowPsw((current) => !current)} />
                  )
                }
                onKeyDown={handleKeyDown}
              />
            </FormItem>
            <FormItem name='code' rules={[{ required: true, message: '验证码必填', type: 'error' }]}>
              <Input size='large' placeholder='请输入验证码' onKeyDown={handleKeyDown} />
              <Button
                style={{ padding: '0px !important' }}
                variant='outline'
                className={Style.verificationBtn}
                onClick={fetchData}
              >
                <img src={imageSrc} alt='' />
              </Button>
            </FormItem>
            <div className={classnames(Style.checkContainer, Style.rememberPwd)}>
              <Checkbox>记住账号</Checkbox>
            </div>
          </>
        )}
        {loginType !== 'qrcode' && (
          <FormItem className={Style.btnContainer}>
            <Button block size='large' type='submit'>
              登录
            </Button>
          </FormItem>
        )}
      </Form>
    </div>
  );
}
