import React, { useState, useEffect } from 'react';
import { Menu, Radio } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { setActive, setType, selectPrivateDomainManage } from 'modules/operationManage/privateDomainManage';
import Style from './index.module.less';

interface IListItemData<T = any> {
  listProps?: object;
  style?: object;
  title?: string;
  description?: string;
  image?: string;
  action?: React.ReactElement;
  listItemProps?: T;
  id: T;
  name: string;
  subjectId: number;
  subjectName: string;
}

interface IListData<T = any> {
  listProps?: object;
  IListItemData: IListItemData[];
  active?: T;
  broadsideList: IListItemData[];
}

interface IData extends React.HTMLAttributes<HTMLElement> {
  broadsideList: IListData;
}

const { MenuItem } = Menu;

const BasicList: React.FC<IData> = ({ broadsideList }) => {
  const dispatch = useAppDispatch();

  const { active } = useAppSelector(selectPrivateDomainManage);
  const [listItemData, setListItemData] = useState(broadsideList);

  useEffect(() => {
    setListItemData(broadsideList);
  }, [broadsideList]);

  return (
    <React.Fragment>
      <Radio.Group
        style={{ width: '100%', textAlign: 'center' }}
        variant='default-filled'
        defaultValue={1}
        onChange={(v) => dispatch(setType(v))}
      >
        <Radio.Button value={1} style={{ width: '50%', textAlign: 'center' }}>
          群聊
        </Radio.Button>
        <Radio.Button value={2} style={{ width: '50%', textAlign: 'center' }}>
          咨询师
        </Radio.Button>
      </Radio.Group>
      <Menu
        value={active}
        onChange={(v) => {
          dispatch(setActive(v as number));
        }}
        style={{ marginRight: 20 }}
      >
        {listItemData.length !== 0 &&
          listItemData.map((item) => (
            <MenuItem style={{ width: '100%' }} value={item.examStudentRoleId} key={item.examStudentRoleId}>
              <div className={Style.content}>
                <span>
                  {item.examName}
                </span>
              </div>
            </MenuItem>
          ))}
        {listItemData.length === 0 && <div style={{ textAlign: 'center' }}>暂无数据</div>}
      </Menu>
    </React.Fragment>
  );
};

export default BasicList;
