import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { getMenuListApi, getMenuInfoApi, addMenuApi, putMenuApi, delMenuApi } from 'api';
import { IEditAddMenuBo, IGetMenuListBo } from 'types/system';
import { handleTree } from 'utils/tool';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'system/menu';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  treeselect: Array<any>;
  menuInfoLoading: boolean;
  menuInfo: any;
  editAddMenuLoading: boolean;
  delMenuLoading: boolean;
}

const initialState: IInitialState = {
  loading: false,
  list: [],
  treeselect: [],
  menuInfoLoading: false,
  menuInfo: {},
  editAddMenuLoading: false,
  delMenuLoading: false,
};

export const getMenuList = createAsyncThunk(`${namespace}/getMenuList`, async (bo: IGetMenuListBo) => {
  const { data } = await getMenuListApi(bo);

  return data;
});

export const getMenuInfo = createAsyncThunk(`${namespace}/getMenuInfo`, async (menuId: number) => {
  const { data } = await getMenuInfoApi(menuId);

  return data;
});

export const getMenuTreeselect = createAsyncThunk(`${namespace}/getMenuTreeselect`, async () => {
  const { data } = await getMenuListApi();

  return data;
});

export const editAddMenu = createAsyncThunk(`${namespace}/editAddMenu`, async (bo: IEditAddMenuBo, { dispatch }) => {
  if (bo.menuId === undefined) {
    await addMenuApi(bo);
    MessagePlugin.success('添加成功！');
  } else {
    await putMenuApi(bo);

    MessagePlugin.success('修改成功！');
  }

  dispatch(getMenuList({}));
});

export const delMenu = createAsyncThunk(`${namespace}/delMenu`, async (menuId: number, { dispatch }) => {
  await delMenuApi(menuId);
  dispatch(getMenuList({}));
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getMenuList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getMenuList.fulfilled, (state, action) => {
        state.loading = false;
        state.list = action.payload;
      })
      .addCase(getMenuList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getMenuInfo.pending, (state) => {
        state.menuInfoLoading = true;
      })
      .addCase(getMenuInfo.fulfilled, (state, action) => {
        state.menuInfoLoading = false;
        state.menuInfo = action.payload;
      })
      .addCase(getMenuInfo.rejected, (state) => {
        state.menuInfoLoading = false;
      })

      .addCase(getMenuTreeselect.fulfilled, (state, action) => {
        state.treeselect = [{ menuId: 0, menuName: '主类目', children: [...handleTree(action.payload, 'menuId')] }];
      })

      .addCase(editAddMenu.pending, (state) => {
        state.editAddMenuLoading = true;
      })
      .addCase(editAddMenu.fulfilled, (state) => {
        state.editAddMenuLoading = false;
      })
      .addCase(editAddMenu.rejected, (state) => {
        state.editAddMenuLoading = false;
      })

      .addCase(delMenu.pending, (state) => {
        state.delMenuLoading = true;
      })
      .addCase(delMenu.fulfilled, (state) => {
        state.delMenuLoading = false;
      })
      .addCase(delMenu.rejected, (state) => {
        state.delMenuLoading = false;
      });
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectSystemMenu = (state: RootState) => state.systemMenu;

export default listBaseSlice.reducer;
