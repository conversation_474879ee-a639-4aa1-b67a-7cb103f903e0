import React, { useState, memo, useEffect, useRef } from 'react';
import {
  <PERSON>alog,
  Button,
  Col,
  Row,
  Popconfirm,
  TableProps,
  MessagePlugin,
  DialogPlugin,
  type DialogProps,
} from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import RelatedMajors from './components/RelatedMajors';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import {
  selectAssociation,
  getEducationalMajorsList,
  deleteEducationalMajors,
  educationalMajorsDragSort,
} from 'modules/directory/association';
import { getMajorsCategoryList, selectSpeciality } from 'modules/directory/speciality';
import Style from './index.module.less';
import { Tables, Search } from 'components';
import { IData } from '../components/BasicList';
import { MoveIcon } from 'tdesign-icons-react';
import { RelatedMajorsHeader } from './components/RelatedMajorsHeader';
import { IRef } from 'components/Form';

export const SelectTable: React.FC<IData> = () => {
  const dispatch = useAppDispatch();
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const [visible, setVisible] = useState(false);
  const [rowData, setRowData] = useState({});
  const [cunt, setCunt] = useState(0);
  const [speciality, setSpeciality] = useState({});
  const [type, setType] = useState('');

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  const { active, activeName, loading, tablist, pageNum, pageSize, total } = useAppSelector(selectAssociation);
  const { professionalList } = useAppSelector(selectSpeciality);
  const searchParams = { pageNum, pageSize, educationalId: active };

  useEffect(() => {
    dispatch(getMajorsCategoryList({}));

    if (active) {
      searchRef.current?.resetForm();
    }
  }, [active]);

  const rehandleClickOp = (record: any) => {
    setType('edit');
    setVisible(true);
    setRowData(record.row);
    setSpeciality({
      eduLevel: record.row.eduLevel,
      majorsName: record.row.majorsName,
    });
  };

  const handleClickDelete = ({ row }: any) => {
    dispatch(
      deleteEducationalMajors({
        educationalId: active,
        majorsIdList: [row.majorsId],
      }),
    );
  };

  const onDragSort = async (val: any) => {
    try {
      await dispatch(
        educationalMajorsDragSort({
          position: pageNum === 1 ? val.targetIndex + 1 : (pageNum - 1) * pageSize + (val.targetIndex + 1),
          id: val.current.educationalMajorsId,
          parentId: active,
        }),
      );
      return true;
    } catch (error) {
      throw new Error('排序失败');
    }
  };

  const batchUnlinking = () => {
    if (selectedRowKeys.length === 0) return MessagePlugin.warning('请先选择要取消关联的数据！');
    const confirmDia = DialogPlugin.confirm({
      header: '批量取消关联提示',
      body: '确定批量取消关联的数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        await dispatch(
          deleteEducationalMajors({
            educationalId: active,
            majorsIdList: selectedRowKeys,
          }),
        );
        handleResetSelection();
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
    return false;
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'drag',
      title: '排序',
      cell: () => <MoveIcon />,
      width: 46,
    },
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    {
      title: '专业名称',
      fixed: 'left',
      align: 'left',
      ellipsis: true,
      colKey: 'majorsName',
    },
    {
      title: '所属大类',
      colKey: 'categoryParentName',
    },
    {
      title: '所属二类',
      ellipsis: true,
      colKey: 'categoryChildrenName',
    },
    {
      title: '学制',
      ellipsis: true,
      colKey: 'schoolSystem',
    },
    {
      title: '学费/年',
      ellipsis: true,
      colKey: 'tuition',
    },
    {
      title: '所属考试',
      ellipsis: true,
      colKey: 'examNameList',
      cell({ row }) {
        if (row.examNameList) return <div>{row.examNameList.length > 0 ? row.examNameList.join(' ') : '暂无数据'}</div>;
        return <div>暂无数据</div>;
      },
    },
    {
      align: 'left',
      fixed: 'right',
      colKey: 'op',
      title: '操作',
      cell(record) {
        return (
          <>
            <Button theme='primary' variant='text' onClick={() => rehandleClickOp(record)}>
              编辑
            </Button>
            <Popconfirm content='确定要取消关联吗？' theme='danger' onConfirm={() => handleClickDelete(record)}>
              <Button theme='danger' variant='text'>
                取消关联
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  const onClickCloseBtn: DialogProps['onCloseBtnClick'] = () => {
    setVisible(false);
  };

  return (
    <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getEducationalMajorsList}
        params={searchParams}
        list={[
          {
            type: 'cascader',
            label: '所属类别',
            field: 'categoryId',
            multiple: false,
            filterable: true,
            clearable: true,
            checkStrictly: true,
            nameField: 'categoryName',
            valueField: 'majorsCategoryId',
            childrenField: 'children',
            options: professionalList,
          },
          {
            type: 'input',
            label: '专业名称',
            field: 'majorsName',
          },
        ]}
      />
      <Row gutter={8} align='middle'>
        <Col>
          <Button
            onClick={() => {
              if (activeName) {
                setVisible(true);
                setType('add');
                setRowData({});
              } else {
                MessagePlugin.warning('请先选择院校', 2 * 1000);
              }
            }}
          >
            关联专业
          </Button>
        </Col>
        <Col>
          <Button theme='default' onClick={batchUnlinking}>
            批量取消关联
          </Button>
        </Col>
      </Row>
      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: tablist,
          loading,
          rowKey: 'majorsId',
          selectedRowKeys,
          onSelectChange,
          dragSort: 'row-handler',
          onDragSort,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getEducationalMajorsList}
        params={searchParams}
      />

      {visible && (
        <Dialog
          className={Style.dialog}
          header={<RelatedMajorsHeader cunt={cunt} type={type} activeName={activeName} speciality={speciality} />}
          visible={visible}
          confirmBtn={null}
          cancelBtn={null}
          onCloseBtnClick={onClickCloseBtn}
          width={800}
        >
          <RelatedMajors
            cancellation={() => setVisible(false)}
            onChangeCunt={setCunt}
            onChangeSpeciality={setSpeciality}
            rowData={rowData}
            type={type}
          ></RelatedMajors>
        </Dialog>
      )}
    </div>
  );
};

export default memo(SelectTable);
