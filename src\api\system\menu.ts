import { IEditAddMenuBo, IGetMenuListBo } from 'types/system';
import request from 'utils/request/index';

/**
 * 获取菜单列表
 * @param params
 * @returns
 */
export const getMenuListApi = async (params: IGetMenuListBo = {}) => {
  const result = await request.get({
    url: '/system/menu/list',
    params,
  });
  return result;
};

/**
 * 获取菜单信息
 * @param params
 * @returns
 */
export const getMenuInfoApi = async (menuId: number) => {
  const result = await request.get({
    url: `/system/menu/${menuId}`,
  });
  return result;
};

/**
 * 添加菜单
 * @param params
 * @returns
 */
export const addMenuApi = async (data: IEditAddMenuBo) => {
  const result = await request.post({
    url: '/system/menu',
    data,
  });
  return result;
};

/**
 * 编辑菜单
 * @param params
 * @returns
 */
export const putMenuApi = async (data: IEditAddMenuBo) => {
  const result = await request.put({
    url: '/system/menu',
    data,
  });
  return result;
};

/**
 * 删除菜单
 * @param params
 * @returns
 */
export const delMenuApi = async (menuId: number) => {
  const result = await request.delete({
    url: `/system/menu/${menuId}`,
  });
  return result;
};
