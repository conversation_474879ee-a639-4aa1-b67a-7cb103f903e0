import React, { memo, useEffect, useRef } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back } from 'components';
import {
  Button,
  Form,
  FormInstanceFunctions,
  FormProps,
  Input,
  Loading,
  Space,
  Radio,
  Textarea,
  Select,
  InputNumber,
} from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectSystemDict, editSystemDictData, getSystemDictDataDetailsInfo } from 'modules/system/dict';
import { useLocation, useNavigate } from 'react-router-dom';
import { IEditDictDataBo } from 'types/system';
import { strIsNull } from 'utils/tool';

const { FormItem } = Form;

const EditDictData: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { dictType, dictId, dictCode } = location.state || {};
  const dispatch = useAppDispatch();
  const { infoLoading, addEditLoading, info } = useAppSelector(selectSystemDict);
  const formRef = useRef<FormInstanceFunctions>();

  const INITIAL_DATA = {
    dictType: '',
    dictLabel: '',
    dictValue: '',
    cssClass: '',
    dictSort: 0,
    listClass: 'default',
    status: '0',
    remark: '',
  };

  const rules: FormProps['rules'] = {
    dictLabel: [
      {
        required: true,
        message: '数据标签不能为空',
        trigger: 'all',
      },
    ],
    dictValue: [
      {
        required: true,
        message: '数据键值不能为空',
        trigger: 'all',
      },
    ],
    dictSort: [
      {
        required: true,
        message: '显示排序不能为空',
        trigger: 'all',
      },
    ],
  };

  const handleSubmit: FormProps['onSubmit'] = async ({ validateResult, fields }) => {
    if (validateResult === true) {
      const bo: IEditDictDataBo = {
        ...fields,
      };

      await dispatch(editSystemDictData(bo));
    }
  };

  const handleReset: FormProps['onReset'] = () => {
    dispatch(getSystemDictDataDetailsInfo(dictCode));
  };

  const handleCancel = () => {
    navigate(`/system/dict/data/${dictId}`);
  };

  useEffect(() => {
    if (!strIsNull(info)) {
      formRef.current?.setFieldsValue(info);
    }
  }, [info]);

  useEffect(() => {
    if (!strIsNull(dictCode)) {
      dispatch(getSystemDictDataDetailsInfo(dictCode));
    }
  }, [dictCode]);

  return (
    <Loading loading={infoLoading} showOverlay style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Back path={`/system/dict/data/${dictId}`} header='字典数据管理' />
      <div className={classnames(CommonStyle.pageWithColor)}>
        <Form
          ref={formRef}
          initialData={INITIAL_DATA}
          resetType='initial'
          rules={rules}
          colon
          className={classnames(CommonStyle.commonSubjectForm)}
          onSubmit={handleSubmit}
          onReset={handleReset}
        >
          <FormItem label='字典类型' name='dictType'>
            <Input disabled placeholder='请输入字典类型' clearable />
          </FormItem>
          <FormItem label='数据标签' name='dictLabel'>
            <Input placeholder='请输入数据标签' clearable />
          </FormItem>
          <FormItem label='数据键值' name='dictValue'>
            <Input placeholder='请输入数据键值' clearable />
          </FormItem>
          <FormItem label='样式属性' name='cssClass'>
            <Input placeholder='请输入样式属性' clearable />
          </FormItem>
          <FormItem label='显示排序' name='dictSort'>
            <InputNumber placeholder='请输入显示排序' theme='column' min={0} style={{ width: '100%' }} />
          </FormItem>
          <FormItem label='回显样式' name='listClass'>
            <Select
              placeholder='请选择回显样式'
              clearable
              options={[
                { label: '默认(default)', value: 'default' },
                { label: '主要(primary)', value: 'primary' },
                { label: '成功(success)', value: 'success' },
                { label: '信息(info)', value: 'info' },
                { label: '警告(warning)', value: 'warning' },
                { label: '危险(danger)', value: 'danger' },
              ]}
            ></Select>
          </FormItem>
          <FormItem label='字典状态' name='status'>
            <Radio.Group>
              <Radio value='0'>正常</Radio>
              <Radio value='1'>停用</Radio>
            </Radio.Group>
          </FormItem>
          <FormItem label='备注' name='remark'>
            <Textarea placeholder='请输入备注' />
          </FormItem>
          <FormItem className={classnames(CommonStyle.commonSubjectFormBtn)}>
            <Space>
              <Button loading={addEditLoading} type='submit' theme='primary'>
                提交
              </Button>
              <Button theme='default' onClick={handleCancel}>
                取消
              </Button>
              <Button type='reset' theme='warning'>
                重置
              </Button>
            </Space>
          </FormItem>
        </Form>
      </div>
    </Loading>
  );
};

export default memo(EditDictData);
