import React, { useState, memo, useEffect, useRef } from 'react';
import {
  But<PERSON>,
  Col,
  Row,
  Popconfirm,
  TableProps,
  Switch,
  Space,
  type TableRowData,
  MessagePlugin,
} from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import {
  getPrivateDomainList,
  selectPrivateDomainManage,
  deletePrivateDomain,
  openOrClosePrivateDomain,
} from 'modules/operationManage/privateDomainManage';
import { ImageViewer, Tables, Search } from 'components';
import { EditOrAddDialog } from './EditOrAddDialog';
import { IRef } from 'components/Form';

export const SelectTable = () => {
  const dispatch = useAppDispatch();
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const [ruleVisible, setRuleVisible] = useState(false);
  const [row, setRow] = useState<TableRowData>();
  const [dataType, setDataType] = useState<string | undefined>('');

  const { active, type, loading, tablist, pageNum, pageSize, total } = useAppSelector(selectPrivateDomainManage);
  const searchParams = { pageNum, pageSize, type, examStudentRoleId: active };

  // const handleResetSelection = () => {
  //   if (tableRef.current) {
  //     // 调用重置选中方法
  //     tableRef.current.resetSelection();
  //   }
  // };

  const getList = () => {
    dispatch(getPrivateDomainList(searchParams));
  };

  const onChange = async (value: boolean, row: any) => {
    await dispatch(
      openOrClosePrivateDomain({
        privateDomainId: row.privateDomainId,
        examStudentRoleId: active,
        isOpen: value ? 1 : 0,
      }),
    );
    MessagePlugin.success(`${value ? '开启' : '关闭'}成功！`);
  };

  const operate = (row: any) => (
    <>
      <Button
        theme='primary'
        variant='text'
        onClick={() => {
          setRow(row);
          setDataType('edit');
          setRuleVisible(true);
        }}
      >
        编辑
      </Button>
      <Popconfirm
        content='确定要删除该数据吗？'
        theme='danger'
        onConfirm={() => dispatch(deletePrivateDomain(row.privateDomainId))}
      >
        <Button theme='danger' variant='text'>
          删除
        </Button>
      </Popconfirm>
    </>
  );

  const state = (row: any) => <Switch size='large' value={row.isOpen === 1} onChange={(val) => onChange(val, row)} />;

  const groupChatColumns: TableProps['columns'] = [
    { colKey: 'serial-number', width: 50, title: '序号' },
    {
      title: '群聊名称',
      fixed: 'left',
      colKey: 'name',
    },
    {
      title: '群聊二维码',
      colKey: 'qrCodeURL',
      cell({ row }) {
        return ImageViewer({
          type: 'item',
          images: [row.qrCodeURL],
        });
      },
    },
    {
      title: '状态',
      colKey: 'isOpen',
      cell({ row }) {
        return state(row);
      },
    },
    {
      align: 'left',
      fixed: 'right',
      colKey: 'op',
      title: '操作',
      cell({ row }) {
        return operate(row);
      },
    },
  ];

  const consultantColumns: TableProps['columns'] = [
    { colKey: 'serial-number', width: 50, title: '序号' },
    {
      title: '咨询师昵称',
      fixed: 'left',
      colKey: 'name',
    },
    {
      title: '咨询师真实姓名',
      colKey: 'actualName',
    },
    {
      title: '咨询师二维码',
      colKey: 'createBy',
      cell({ row }) {
        return ImageViewer({
          type: 'item',
          images: [row.qrCodeURL],
        });
      },
    },
    {
      title: '状态',
      colKey: 'isOpen',
      cell({ row }) {
        return state(row);
      },
    },
    {
      align: 'left',
      fixed: 'right',
      colKey: 'op',
      title: '操作',
      cell({ row }) {
        return operate(row);
      },
    },
  ];

  const [columns, setColumns] = useState([...groupChatColumns]);

  useEffect(() => {
    if (active) {
      searchRef.current?.resetForm();
      getList();
    }
    if (type === 1) {
      setColumns([...groupChatColumns]);
    } else {
      setColumns([...consultantColumns]);
    }
  }, [active, type]);

  return (
    <>
      <Search
        ref={searchRef}
        method={getPrivateDomainList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: `${type === 1 ? '群聊' : '咨询师查询'}`,
            field: 'vague',
          },
        ]}
      />
      <Row gutter={8} align='middle'>
        <Col>
          <Space align='end'>
            <Button
              variant='outline'
              theme='primary'
              onClick={() => {
                setRow({});
                setRuleVisible(true);
                setDataType('add');
              }}
            >
              新增{type === 1 ? '群聊' : '咨询师'}
            </Button>
            {type === 1 && <p>每个分类下仅能同时开启一个群聊</p>}
          </Space>
        </Col>
      </Row>
      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: tablist,
          loading,
          rowKey: 'examQuestionId',
          selectedRowKeys,
          onSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getPrivateDomainList}
        params={searchParams}
      />
      {EditOrAddDialog({
        visibleProp: ruleVisible,
        rowProp: row,
        activeProp: active,
        typeProp: type,
        dataType,
        onCancel: (val) => setRuleVisible(val),
      })}
    </>
  );
};

const selectPage: React.FC = () => (
  <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
    <SelectTable />
  </div>
);

export default memo(selectPage);
