import React, { useState, memo, useEffect, useRef } from 'react';
import { Button, Row, Col, TableProps, type TableRowData } from 'tdesign-react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'modules/store';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { selectPointsManagement, getIntegralList } from 'modules/pointsManagement';
import { Tables, Search } from 'components';
import { ClearDialog, RuleDialog } from './components';
import { IRef } from 'components/Form';

const SelectTable = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const { loading, list, pageNum, pageSize, total } = useAppSelector(selectPointsManagement);
  const [visible, setVisible] = useState(false);
  const [ruleVisible, setRuleVisible] = useState(false);
  const [row, setRow] = useState<TableRowData>();
  const [type, setType] = useState('');

  // const handleResetSelection = () => {
  //   if (tableRef.current) {
  //     // 调用重置选中方法
  //     tableRef.current.resetSelection();
  //   }
  // };

  const searchParams = { pageNum, pageSize };

  useEffect(() => {
    dispatch(getIntegralList(searchParams));
  }, [dispatch]);

  const columns: TableProps['columns'] = [
    { colKey: 'serial-number', width: 80, title: '序号' },
    {
      title: '用户昵称',
      fixed: 'left',
      colKey: 'nickName',
    },
    {
      title: '用户电话',
      colKey: 'phoneNumber',
    },
    {
      title: '积分',
      colKey: 'assembleIntegral',
    },
    {
      fixed: 'right',
      colKey: 'op',
      title: '操作',
      cell({ row }) {
        return (
          <>
            <Button
              theme='primary'
              variant='text'
              onClick={() => navigate('/integralManage/userIntegral/log', { state: row })}
            >
              查看积分日志
            </Button>
            <Button
              theme='danger'
              variant='text'
              onClick={() => {
                setVisible(true);
                setRow(row);
                setType('item');
              }}
            >
              清空用户积分
            </Button>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Search
        ref={searchRef}
        method={getIntegralList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '用户昵称',
            field: 'nickName',
          },
          {
            type: 'input',
            label: '用户电话',
            field: 'phoneNumber',
          },
        ]}
      />
      <div className='tabs-content' style={{ margin: 20 }}>
        <Row gutter={8} align='middle'>
          <Col>
            <Button variant='outline' theme='primary' onClick={() => setRuleVisible(true)}>
              积分规则
            </Button>
          </Col>
          <Col>
            <Button
              variant='outline'
              theme='danger'
              onClick={() => {
                setVisible(true);
                setType('all');
              }}
            >
              清空全部积分
            </Button>
          </Col>
        </Row>
      </div>
      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list,
          loading,
          rowKey: 'index',
          selectedRowKeys,
          onSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getIntegralList}
        params={searchParams}
      />

      {ClearDialog({
        visibleProp: visible,
        rowProp: row,
        typeProp: type,
        onCancel: (val) => setVisible(val),
      })}
      {RuleDialog({
        visibleProp: ruleVisible,
        rowProp: row,
        typeProp: type,
        onCancel: (val) => setRuleVisible(val),
      })}
    </>
  );
};

const selectPage: React.FC = () => (
  <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
    <SelectTable />
  </div>
);

export default memo(selectPage);
