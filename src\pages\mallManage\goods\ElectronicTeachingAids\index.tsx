import React, { memo, useEffect, useRef, useState } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  selectMallManageGoods,
  getGoodsList,
  clearPageState,
  updateGoodsTopped,
  updateGoodsListed,
  deleteGoods,
} from 'modules/mallManage/goods';
import { IRef } from 'components/Form';
import { Tables, Search } from 'components';
import CustomColumns from 'components/CustomColumns';
import PermissionButton from 'components/PermissionButton';
import { Popconfirm, Row, Space, Switch } from 'tdesign-react';
import type { TableProps } from 'tdesign-react';
import { IGetGoodsListBo, IUpdateGoodsToppedBo, IUpdateGoodsListedBo } from 'types/mallManage';
import { Icon } from 'tdesign-icons-react';
import useHasPermission from 'hooks/useHasPermission';

const ElectronicTeachingAids: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const {
    loading,
    list,
    pageNum,
    pageSize,
    total,
    updateGoodsToppedLoading,
    updateGoodsListedLoading,
    deleteGoodsLoading,
  } = useAppSelector(selectMallManageGoods);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetGoodsListBo = { pageNum, pageSize, type: 2 };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'serial-number',
    'topped',
    'goodsName',
    'price',
    'buyNum',
    'isListed',
    'allowExchange',
    'pointsRequired',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = [
    'goodsName',
    'price',
    'buyNum',
    'pointsRequired',
    'createTime',
    'createBy',
    'updateTime',
    'updateBy',
  ];
  const renderActiveContent = () => <Icon name='check' />;
  const renderInactiveContent = () => <Icon name='close' />;
  const hasPermission = useHasPermission(['mall:goods:edit']);

  /**
   * 置顶change
   */
  const handleToppedChange = async (topped: 0 | 1, goodsId: number) => {
    const bo: IUpdateGoodsToppedBo = {
      goodsId,
      topped,
    };

    const { type } = await dispatch(updateGoodsTopped(bo));

    if (type.endsWith('fulfilled')) {
      dispatch(getGoodsList(searchParams));
    }
  };

  /**
   * 上架change
   */
  const handleListedChange = async (isListed: 0 | 1, goodsId: number) => {
    const bo: IUpdateGoodsListedBo = {
      goodsId,
      isListed,
    };

    const { type } = await dispatch(updateGoodsListed(bo));

    if (type.endsWith('fulfilled')) {
      dispatch(getGoodsList(searchParams));
    }
  };

  /**
   * 删除商品
   */
  const handleDelete = async (goodsId: number) => {
    const { type } = await dispatch(deleteGoods(goodsId));

    if (type.endsWith('fulfilled')) {
      dispatch(getGoodsList(searchParams));
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'serial-number',
      width: 80,
      title: '序号',
    },
    {
      colKey: 'goodsName',
      title: '商品名称',
    },
    {
      colKey: 'price',
      title: '单价/￥',
      width: 100,
    },
    {
      colKey: 'buyNum',
      title: '购买人数',
      width: 100,
    },
    {
      colKey: 'topped',
      title: '置顶',
      width: 100,
      cell({ row }) {
        // eslint-disable-next-line no-nested-ternary
        return hasPermission ? (
          <Switch
            customValue={[1, 0]}
            value={row.topped}
            label={[renderActiveContent(), renderInactiveContent()]}
            loading={updateGoodsToppedLoading}
            onChange={(val) => handleToppedChange(val, row.goodsId)}
          />
        ) : row.topped === 1 ? (
          '是'
        ) : (
          '否'
        );
      },
    },
    {
      colKey: 'isListed',
      title: '上架',
      width: 100,
      cell({ row }) {
        // eslint-disable-next-line no-nested-ternary
        return hasPermission ? (
          <Switch
            customValue={[1, 0]}
            value={row.isListed}
            label={[renderActiveContent(), renderInactiveContent()]}
            loading={updateGoodsListedLoading}
            onChange={(val) => handleListedChange(val, row.goodsId)}
          />
        ) : row.isListed === 1 ? (
          '是'
        ) : (
          '否'
        );
      },
    },
    {
      colKey: 'allowExchange',
      title: '兑换',
      width: 100,
      cell({ row }) {
        return row.allowExchange === 1 ? '开' : '关';
      },
    },
    {
      colKey: 'pointsRequired',
      title: '兑换积分',
      width: 100,
    },
    {
      colKey: 'updateTime',
      title: '修改时间',
    },
    {
      colKey: 'updateBy',
      title: '修改人',
    },
    {
      colKey: 'createTime',
      title: '创建时间',
    },
    {
      colKey: 'createBy',
      title: '创建人',
    },
    {
      colKey: 'op',
      title: '操作',
      width: 280,
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['mall:goods:query']}
              onClick={() => navigate('/mall/goods/preview', { state: { goodsId: row.goodsId } })}
            >
              预览
            </PermissionButton>
            <PermissionButton
              permissions={['mall:goods:query']}
              onClick={() => navigate('/mall/goods/details/2', { state: { goodsId: row.goodsId } })}
            >
              详情
            </PermissionButton>
            <PermissionButton
              permissions={['mall:goods:edit']}
              onClick={() => navigate('/mall/goods/edit/2', { state: { goodsId: row.goodsId } })}
            >
              修改
            </PermissionButton>
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleDelete(row.goodsId)}>
              <>
                <PermissionButton permissions={['mall:goods:remove']} theme='danger' loading={deleteGoodsLoading}>
                  删除
                </PermissionButton>
              </>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    dispatch(getGoodsList(searchParams));

    return () => {
      dispatch(clearPageState());
    };
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithPaddingTop)}>
      <Search
        ref={searchRef}
        method={getGoodsList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '商品名称',
            field: 'goodsName',
          },
          {
            type: 'select',
            label: '是否上架',
            field: 'isListed',
            options: [
              {
                label: '否',
                value: 0,
              },
              {
                label: '是',
                value: 1,
              },
            ],
          },
          {
            type: 'select',
            label: '是否兑换',
            field: 'allowExchange',
            options: [
              {
                label: '否',
                value: 0,
              },
              {
                label: '是',
                value: 1,
              },
            ],
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            permissions={['mall:goods:add']}
            content='添加商品'
            variant='outline'
            onClick={() => navigate('/mall/goods/add/2')}
          />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'goodsId',
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getGoodsList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(ElectronicTeachingAids);
