import React from 'react';
import { ChevronLeftIcon } from 'tdesign-icons-react';
import { Card } from 'tdesign-react';
import classnames from 'classnames';
import Style from './index.module.less';
import { useNavigate } from 'react-router-dom';

export interface IBackProps extends React.HTMLAttributes<HTMLElement> {
  header?: string | React.ReactElement;
  Icon?: React.ReactElement;
  border?: boolean;
  path?: string;
  params?: any;
}

const Back = ({ path, header, border = false, params }: IBackProps) => {
  const navigate = useNavigate();
  const backTo = () => {
    navigate(path as string, { state: params });
  };
  return (
    <div className={Style.back}>
      <Card
        className={classnames({
          [Style.boardPanel]: true,
        })}
        bordered={border}
      >
        <div className={Style.boardItem}>
          <span onClick={backTo}>
            <ChevronLeftIcon />
            <span className={Style.boardTitle}>{header}</span>
          </span>
        </div>
      </Card>
    </div>
  );
};

export default React.memo(Back);
