import React, { memo, useRef, useEffect, useState } from 'react';
import {
  Form,
  Row,
  Col,
  Input,
  Button,
  Select,
  MessagePlugin,
  Checkbox,
  Loading,
  Radio,
  Space,
  Tag,
  CheckTag,
} from 'tdesign-react';
import type { SelectProps, SelectValue, SelectOption } from 'tdesign-react';
import { AddIcon } from 'tdesign-icons-react';
import classnames from 'classnames';
import { SubmitContext, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { addOrUpdateTrend, selectDynamicPublishing } from 'modules/operationManage/dynamicPublishing';

import { selectPublic, publicGetExamList, publicAllSchoolList, publicAllLabelList } from 'modules/public';
import { useLocation, useNavigate } from 'react-router-dom';
import { Back, Upload, Editor } from 'components';
import { removeTagsAndSpaces } from 'utils/tool';

interface Tag {
  name: string;
  checked?: boolean;
}

const { FormItem } = Form;

const backPath = '/operationManage/dynamicPublishing';

const STYLE_B_UNCHECKED_PROPS = {
  theme: 'default',
  variant: 'outline',
} as const;

export default memo(() => {
  const navigate = useNavigate();
  const { state } = useLocation();

  const INITIAL_DATA = {
    title: '',
    tags: '',
    cover: undefined,
    level: undefined,
    content: state.type === 'edit' ? state.row.content : undefined,
    examIdList: [],
    educationalIdList: [],
  };
  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>();

  const { formLoading } = useAppSelector(selectDynamicPublishing);
  const { examList, allSchoolList, allLabelList } = useAppSelector(selectPublic);
  const [value, setValue] = useState<SelectValue<SelectOption>>([]);
  const [tagList, setTagList] = useState<Tag[]>([]);
  const [inputVisible, toggleInputVisible] = useState(false);

  const [checkedList, setCheckedList] = useState<Tag[]>(allLabelList);

  const deleteTag = (i: number, tag: Tag) => {
    const newCheckedList = checkedList.map((item) => (item.name === tag.name ? { ...tag, checked: false } : item));
    setCheckedList(newCheckedList);
    const newTagList = [...tagList];
    newTagList.splice(i, 1);
    setTagList(newTagList);
  };

  const handleTagChange = (index: number, checked: boolean, item: Tag) => {
    if (checked) {
      if (tagList.length >= 3) return MessagePlugin.warning('标签最多可添加3个');
      const newTagList = [...tagList, ...[item]];
      setTagList(newTagList);
    } else {
      const newTagList = tagList.filter((tag) => tag.name !== item.name);
      setTagList(newTagList);
    }
    const newCheckedList = checkedList.map((tag, i) => (i === index ? { ...tag, checked } : tag));
    setCheckedList(newCheckedList);
    return false;
  };

  const handleClickAdd = () => {
    toggleInputVisible(true);
  };

  const handleInputEnter = (value: string) => {
    toggleInputVisible(false);
    if (value) setTagList((currentList) => currentList.concat([{ name: value }]));
    const updatedTags = checkedList.map((tag) => (value === tag.name ? { ...tag, checked: true } : tag));
    setCheckedList(updatedTags);
  };
  useEffect(() => {
    if (examList.length === 0) {
      dispatch(publicGetExamList(''));
    }
    if (allSchoolList.length === 0) {
      dispatch(publicAllSchoolList(''));
    }
    if (allLabelList.length === 0) {
      const fetchData = async () => {
        try {
          const response = await dispatch(publicAllLabelList(''));
          let arr = [];
          arr = response.payload.rows.map((item: any) => ({
            name: item,
          }));
          setCheckedList(arr);
        } catch (err) {
          // setError(err);
        } finally {
          // setLoading(false);
        }
      };
      fetchData();
    }
  }, [dispatch]);

  useEffect(() => {
    if (state.type === 'edit') {
      const resultArray = state.row.tags && state.row.tags.split(/[,，]/);
      if (Array.isArray(resultArray) && resultArray.length > 0) {
        const tagsArray: Tag[] = resultArray.map((item: any) => ({ name: item.trim() }));
        const updatedTags = allLabelList.map((tag: any) => {
          const isChecked = tagsArray.some((checkTag) => checkTag.name === tag.name);
          return { ...tag, checked: isChecked };
        });
        setCheckedList(updatedTags);
        setTagList(tagsArray);
      } else {
        setCheckedList(allLabelList);
        setTagList([]);
      }
      // console.log(allLabelList, "allLabelList")
      const formData = state.row;

      if (Array.isArray(formData.educationalIdList) && JSON.stringify(formData.educationalIdList).indexOf(null) > -1) {
        formData.educationalIdList = [];
      }

      formRef.current?.setFieldsValue?.(formData);
    }
  }, [useLocation]);

  const onSuccess1 = ({
    file: {
      response: {
        data: { id },
      },
    },
  }: {
    file: { response: { data: { id: number } } };
  }) => {
    formRef.current?.setFieldsValue?.({ cover: id });
  };

  const onSubmit = async (e: SubmitContext) => {
    formRef.current?.setFieldsValue?.({
      tags: tagList.length > 0 ? tagList.map((i) => i.name).join(',') : '',
    });
    const params = { ...formRef.current?.getFieldsValue?.(true) };
    if (!removeTagsAndSpaces(params.content)) {
      formRef.current?.setFieldsValue?.({
        content: undefined,
      });
    }
    if (e.validateResult === true) {
      const params: object | undefined = {
        trendsId: state.type === 'edit' ? state.row.trendsId : undefined,
        ...formRef.current?.getFieldsValue?.(true),
      };
      if (typeof params.educationalIdList === 'number') {
        params.educationalIdList = [params.educationalIdList];
      }

      await dispatch(addOrUpdateTrend(params));
      MessagePlugin.success(`${state.type === 'edit' ? '编辑' : '新增'}成功`);
      navigate(backPath);
    }
  };
  const handleChange: SelectProps['onChange'] = (value) => {
    setValue(value);
  };
  return (
    <>
      <Back path={backPath} header={`${state.type === 'add' ? '新增' : '编辑'}动态`} />
      <div className={classnames(CommonStyle.pageWithColor)}>
        <div className={Style.formContainer}>
          <Loading loading={formLoading} showOverlay>
            <Form ref={formRef} onSubmit={onSubmit} labelWidth={100} labelAlign='top'>
              <div className={Style.titleBox}></div>
              <Row gutter={[32, 34]}>
                <Col span={12}>
                  <FormItem
                    label='动态标题'
                    name='title'
                    initialData={INITIAL_DATA.title}
                    rules={[{ required: true, message: '动态标题必填', type: 'error' }]}
                  >
                    <Input maxlength={40} showLimitNumber placeholder='请输入动态标题' />
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label='动态重要层级'
                    name='level'
                    initialData={INITIAL_DATA.level}
                    rules={[{ required: true, message: '动态重要层级必选', type: 'error' }]}
                  >
                    <Radio.Group>
                      <Radio value={1}>一级（重要）</Radio>
                      <Radio value={2}>二级（次重要）</Radio>
                    </Radio.Group>
                  </FormItem>
                </Col>
                <Col span={20} className={Style.dateCol}>
                  <FormItem
                    label='可见考试范围'
                    name='examIdList'
                    initialData={INITIAL_DATA.examIdList}
                    rules={[{ required: true, message: '可见考试范围必选', type: 'error' }]}
                  >
                    <Checkbox.Group>
                      {examList &&
                        examList.map((i: any) => (
                          <Checkbox key={i.examId} value={i.examId}>
                            {i.examName}
                          </Checkbox>
                        ))}
                    </Checkbox.Group>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label='动态封面'
                    name='cover'
                    initialData={INITIAL_DATA.cover}
                    rules={[{ required: false, message: '请上传动态封面', type: 'error' }]}
                  >
                    <Upload
                      params={{ businessType: 8 }}
                      theme='image'
                      tips='请上传 .jpg/.png 文件大小3M，建议分辨率为570*1017px'
                      max={1}
                      draggable
                      files={state.row?.coverUrl ? [{ url: state.row?.coverUrl }] : []}
                      maxFileSize={3}
                      accept='.jpg, .png'
                      success={onSuccess1}
                    />
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label='关联院校'
                    name='educationalIdList'
                    initialData={INITIAL_DATA.educationalIdList}
                    rules={[{ required: false, message: '关联院校必选', type: 'error' }]}
                  >
                    <Select
                      value={value}
                      placeholder='请选择院校'
                      onChange={handleChange}
                      filterable
                      clearable
                      keys={{ label: 'educationalName', value: 'educationalId' }}
                      options={[...allSchoolList]}
                    ></Select>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label='动态标签' name='tags' initialData={INITIAL_DATA.tags}>
                    <Space direction='vertical'>
                      <Space>
                        {tagList.map((tag: any, i) => (
                          <Tag key={i} closable={true} onClose={() => deleteTag(i, tag)}>
                            {tag.name}
                          </Tag>
                        ))}
                        {!inputVisible && tagList.length < 3 && (
                          <Tag onClick={handleClickAdd} icon={<AddIcon />}>
                            添加
                          </Tag>
                        )}
                        {inputVisible && (
                          <Input onBlur={handleInputEnter} onEnter={handleInputEnter} style={{ width: '94px' }} />
                        )}
                      </Space>
                      <div style={{ display: 'flex', cursor: 'pointer' }}>
                        {checkedList.map((tag: any, i) => (
                          <CheckTag
                            key={i}
                            checked={tag.checked}
                            onChange={(val) => {
                              handleTagChange(i, val, tag);
                            }}
                            uncheckedProps={STYLE_B_UNCHECKED_PROPS}
                            style={{ marginRight: '12px' }}
                          >
                            {tag.name}
                          </CheckTag>
                        ))}
                      </div>
                    </Space>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label='动态内容'
                    name='content'
                    initialData={INITIAL_DATA.content}
                    rules={[{ required: true, message: '动态内容必填', type: 'error' }]}
                  >
                    <Editor
                      fixedToolbar='hide'
                      border
                      text={INITIAL_DATA.content}
                      editorStyle={{ height: '600px', width: '100%' }}
                    ></Editor>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem>
                    <Button type='submit' theme='primary'>
                      提交
                    </Button>
                    <Button type='reset' style={{ marginLeft: 12 }}>
                      重置
                    </Button>
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </Loading>
        </div>
      </div>
    </>
  );
});
