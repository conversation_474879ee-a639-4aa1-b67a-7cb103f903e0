/* eslint-disable react/react-in-jsx-scope */
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { keyEvent } from './utils.js';
import { strIsNull } from 'utils/tool.js';

export interface TcVideoPlayerProps {
  // 播放器宽
  width?: string;
  // 播放器高
  height?: string;
  // 是否静音播放
  muted?: boolean;
  // 是否自动播放
  autoplay?: boolean;
  // 进度条是否打点
  progressMarker?: boolean;
  // 是否续播
  continuePlay?: boolean;
  // 清晰度示例值
  playbackRates?: Array<number>;
  // 播放器实例化参数
  options: {
    id: number | string;
    appID: string;
    fileID: string;
    psign: string;
  };
  // 是否显示播放、暂停切换按钮
  playToggle?: boolean;
  // 是否显示播放进度条
  progressControl?: boolean;
  // 是否显示播放速率选择按钮
  playbackRateMenuButton?: boolean;
  // 是否显示清晰度切换菜单
  qualitySwitcherMenuButton?: boolean;
  // 是否开启清晰度切换提示
  levelSwitch?: boolean;
  playerTarget: (player: any) => void;
}

export interface IRef {
  onDestroy: () => void;
}

const TcVideoPlayer = forwardRef<IRef, TcVideoPlayerProps>(
  (
    {
      width = '100%',
      height = '100%',
      muted = true,
      autoplay = false,
      progressMarker = false,
      continuePlay = false,
      playbackRates = [0.5, 1.0, 1.2, 1.5, 1.8, 2.0],
      options,
      playToggle = true,
      progressControl = true,
      playbackRateMenuButton = true,
      qualitySwitcherMenuButton = true,
      levelSwitch = true,
      playerTarget,
    },
    ref,
  ) => {
    const [player, setPlayer] = useState<any>({});
    const divRef = useRef<HTMLDivElement | null>(null);
    const videoRef = useRef<HTMLVideoElement | null>(null);

    /**
     * @params 无需传参
     * @description 销毁播放器实例
     */
    const destroy = () => {
      if (!strIsNull(player)) {
        player.dispose();
      }
    };

    /**
     * @params 无需传参
     * @description 开始播放
     */
    const play = () => {
      player.play();
    };

    /**
     * @params 无需传参
     * @description 暂停播放
     */
    const pause = () => {
      player.pause();
    };

    /**
     * @params 无需传参
     * @description 返回视频总时长
     */
    const getDuration = () => (player.duration() / 60).toFixed(2);

    /**
     * @params 无需传参
     * @description 返回当前视频播放时长
     */
    const getCurrentTime = () => (player.currentTime() / 60).toFixed(2);

    /**
     * @params time 指定跳转时间(单位：秒)
     * @description 跳转到指定时间播放
     */
    const setCurrentTime = (time) => {
      const startTime = time || 0;
      player.currentTime(startTime);
      player.play();
    };

    /**
     * @params src 视频封面图片路径
     * @description 设置视频封面图片
     */
    const setPosterImg = (src) => {
      if (!src || src === '') return;
      player.poster(src);
    };

    /**
     * @params 无需传参
     * @description 获取视频封面图片
     */
    const getPosterImg = () => player.poster();

    /**
     * @params 无需传参
     * @description 设置打开全屏
     */
    const openFullScreen = () => {
      player.requestFullscreen();
    };

    /**
     * @params 无需传参
     * @description 退出全屏
     */
    const exitFullScreen = () => {
      player.exitFullscreen();
    };

    /**
     * @params 无需传参
     * @description 是否全屏
     */
    const isFullScreen = () => player.isFullscreen();

    /**
     * @params 无需传参
     * @description 获取视频分辨率尺寸
     */
    const getResolutionSize = () => ({
      width: player.videoWidth(),
      height: player.videoHeight(),
    });

    // 暴露给外部的方法
    useImperativeHandle(ref, () => ({
      onDestroy: () => {
        destroy();
      },
    }));

    const createPlayer = () => {
      const el = divRef.current;

      if (el) {
        const tcViewClientWidth = el.clientWidth;
        const tcplayerId = 'tcvideoplayer';
        // const tcplayerId = 'tcplayer' + Date.now()
        el.style.width = width;
        el.style.height = height;

        // if (videoRef.current) {
        //   videoRef.current.width = tcViewClientWidth;
        //   videoRef.current.id = tcplayerId;
        //   videoRef.current.preload = 'auto';
        //   videoRef.current.playsInline = true;
        //   videoRef.current.setAttribute('webkit-playsinline', 'true');
        // }
        const videoNode = document.createElement('video');
        videoNode.width = tcViewClientWidth;
        videoNode.id = tcplayerId;
        videoNode.preload = 'auto';
        videoNode.playsInline = true;
        videoNode.setAttribute('webkit-playsinline', 'true');
        el.appendChild(videoNode);

        const player = window.TCPlayer(tcplayerId, {
          appID: options.appID,
          fileID: options.fileID,
          psign: options.psign,
          autoplay,
          muted, // 是否静音播放
          playbackRates,
          plugins: {
            ContinuePlay: {
              // 开启续播功能
              auto: continuePlay, // [可选] 是否在视频播放后自动续播
              text: '上次播放至 ', // [可选] 提示文案
              btnText: '恢复播放', // [可选] 按钮文案
            },
            ContextMenu: {
              levelSwitch: {
                open: levelSwitch, // 打开切换提示
                switchingText: '正在为您切换至', // 开始切换时文案
                switchedText: '切换成功', // 切换成功时文案
                switchErrorText: '切换失败', // 切换失败时文案
              },
            },
            ProgressMarker: progressMarker,
          },
          hlsConfig: {
            maxBufferLength: 60,
            maxMaxBufferLength: 60,
          },
          controlBar: {
            playToggle, // 是否显示播放、暂停切换按钮
            progressControl, // 是否显示播放进度条
            playbackRateMenuButton, // 是否显示播放速率选择按钮
            QualitySwitcherMenuButton: qualitySwitcherMenuButton, // 是否显示清晰度切换菜单
          },
        });

        setPlayer(player);

        // 设置键盘控制
        keyEvent(player);

        player.volume(muted ? 0 : 0.5);

        playerTarget(player);
      }
    };

    // useEffect(() => () => {
    //   destroy();
    // });

    useEffect(() => {
      createPlayer();
    }, [options]);

    return (
      <div ref={divRef} className='tc-video-player'>
        {/* <video ref={videoRef}></video> */}
      </div>
    );
  },
);

export default TcVideoPlayer;
