import React, { useState, useEffect, useRef } from 'react';
import { Tooltip } from 'tdesign-react';

// 定义组件的 Props 类型
interface AutoTooltipProps {
  children: React.ReactNode; // 子组件，可以是文本或其他 React 元素
  maxWidth?: number | string; // 最大宽度，默认为 200
  style?: any; // 样式
}

const AutoTooltip: React.FC<AutoTooltipProps> = ({ children, maxWidth = 200, style }) => {
  const [isOverflow, setIsOverflow] = useState(false);
  const textRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const checkOverflow = () => {
      const el = textRef.current;
      if (el) {
        // 检查是否文本内容超出了容器宽度
        setIsOverflow(el.scrollWidth > el.clientWidth);
      }
    };

    checkOverflow();
    window.addEventListener('resize', checkOverflow); // 窗口大小改变时重新检测
    return () => {
      window.removeEventListener('resize', checkOverflow);
    };
  }, [children]);

  return (
    <Tooltip
      content={children}
      disabled={!isOverflow} // 如果文本没有溢出，禁用 Tooltip
      placement='top'
    >
      <div
        ref={textRef}
        style={{
          maxWidth: typeof maxWidth === 'number' ? `${maxWidth}px` : maxWidth,
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          ...style,
        }}
      >
        {children}
      </div>
    </Tooltip>
  );
};

export default AutoTooltip;
