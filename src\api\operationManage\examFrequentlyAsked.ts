import request from 'utils/request/index';

export interface IGetExamQuestionAdminListApi {
  pageSize: number;
  pageNum: number;
  examId: number;
  examQuestionName?: string;
}

/**
 * 分页条件查询考试常见问题列表
 * @param params
 * @returns
 */
export const getExamQuestionAdminListApi = async (params: IGetExamQuestionAdminListApi) => {
  const result = await request.get({
    url: '/examQuestion/getExamQuestionAdminList',
    params,
  });
  return result;
};

export interface IDeleteExamQuestionApi {
  examQuestionIdList: (number | string)[];
  examQuestionContent: string;
  examId: number;
}

/**
 *  删除/批量删除考试常见问题
 * @param params
 * @returns
 */
export const deleteExamQuestionApi = async (params: IDeleteExamQuestionApi) => {
  const result = await request.delete({
    url: `/examQuestion/deleteExamQuestion`,
    params,
  });
  return result;
};

export interface IAddOrUpdateExamQuestionApi {
  examQuestionId: number;
  examId: number;
  questionContent: string;
  answerContent?: string;
}

/**
 * 添加或修改考试常见问题
 * @param params
 * @returns
 */
export const addOrUpdateExamQuestionApi = async (params: IAddOrUpdateExamQuestionApi) => {
  const result = await request.post({
    url: `/examQuestion/addOrUpdateExamQuestion`,
    params,
  });
  return result;
};

export interface IExamQuestionDragSortApi {
  /** 需要排序的常见问题数据id */
  id: number;

  /** 考试id */
  parentId: number;

  /** 排序到的位置从1开始 */
  position: number;
}
export interface IExamQuestionApi {
  /** 问题id */
  examQuestionId: number;

  /** 是否开启(1-是，0-否) */
  isTop: number;
}

/**
 * 常见问题列表拖动排序
 * @param params
 * @returns
 */
export const examQuestionDragSortApi = async (params: IExamQuestionDragSortApi) => {
  const result = await request.put({
    url: `/examQuestion/examQuestionDragSort`,
    params,
  });
  return result;
};
/**
 * 开启/关闭 置顶
 * @returns
 */
export const openOrCloseExamQuestionTopApi = async (params: IExamQuestionApi) => {
  const result = await request.post({
    url: `/examQuestion/examQuestionTop?isTop=${params.isTop}&examQuestionId=${params.examQuestionId}`,
  });
  return result;
};
