import request from 'utils/request/index';

/**
 * 分页条件查询资料列表
 * @returns
 */
export const getSubjectMaterialListApi = async (params: any) => {
  const result = await request.get({
    url: '/subjectMaterial/getSubjectMaterialList',
    params,
  });
  return result;
};

/**
 * 删除题目
 * @returns
 */
export const deleteSubjectMaterialApi = async (params: any) => {
  const result = await request.delete({
    url: '/subjectMaterial/deleteSubjectMaterial',
    params,
  });
  return result;
};

/**
 * 批量添加科目资料
 * @param data
 * @returns
 */
export const addSubjectMaterialApi = async (params: any) => {
  const result = await request.post({
    url: '/subjectMaterial/addSubjectMaterial',
    params,
  });
  return result;
};

export interface IMaterialnDragSortApi {
  id: string;
  position: number;
  parentId: number;
}

/**
 * 资料列表拖动排序
 * @returns
 */
export const MaterialDragSortApi = async (params: IMaterialnDragSortApi) => {
  const result = await request.put({
    url: `subjectMaterial/subjectMaterialDragSort`,
    params,
  });
  return result;
};
export interface IUpdateMaterialTypeApi {
  subjectMaterialId: number;
  materialType: string | '';
}
/**
 * 修改资料类型
 * @returns
 */
export const updateMaterialTypeApi = async (params: IUpdateMaterialTypeApi) => {
  const result = await request.put({
    url: '/subjectMaterial/updateMaterialType',
    params,
  });
  return result;
};
