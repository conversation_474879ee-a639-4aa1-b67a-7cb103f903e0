interface IOption {
  value: number | string;
  label: string;
}

/**
 * '编辑状态' 枚举
 * @type {{EDITED: number, UNEDITED: number}}
 */
export const EDIT_STATUS = {
  EDITED: 1,
  UNEDITED: 0,
};

/**
 * '适用对象' 枚举
 * @type {{UNIVERSAL: number, THREE_SCHOOL: number, SOCIAL: number}}
 */
export const APPLY_OBJECT = {
  UNIVERSAL: 1,
  THREE_SCHOOL: 2,
  SOCIAL: 3,
};

export const APPLY_OBJECT_LIST: Array<IOption> = [
  {
    value: APPLY_OBJECT.UNIVERSAL,
    label: '普高生',
  },
  {
    value: APPLY_OBJECT.THREE_SCHOOL,
    label: '三校生',
  },
  {
    value: APPLY_OBJECT.SOCIAL,
    label: '社会考生',
  },
];

/**
 * '考试类型' 枚举
 * @type {{EXAM_TYPE_1: number, EXAM_TYPE_2: number, EXAM_TYPE_3: number}}
 */
export const APPLIED_SCHOOLS = {
  VOCATIONAL_COLLEGES: 1,
  UNDERGRAD_COLLEGES: 2,
};

export const APPLIED_SCHOOLS_LIST: Array<IOption> = [
  {
    value: APPLIED_SCHOOLS.VOCATIONAL_COLLEGES,
    label: '高职院校',
  },
  {
    value: APPLIED_SCHOOLS.UNDERGRAD_COLLEGES,
    label: '本科院校',
  },
];
