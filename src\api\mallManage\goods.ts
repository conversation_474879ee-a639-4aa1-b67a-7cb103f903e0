import {
  IGetGoodsListBo,
  IGetGoodsInfoBo,
  IUpdateGoodsListedBo,
  IUpdateGoodsToppedBo,
  IPreviewGoodsBo,
  IGetResourceListBo,
  IAddGoodsBo,
  IEditGoodsBo
} from 'types/mallManage';
import request from 'utils/request/index';

/**
 * 获取商品列表
 * @param params
 * @returns
 */
export const getGoodsListApi = (params: IGetGoodsListBo) =>
  request.get({
    url: '/goods/queryGoodsList',
    params,
  });

/**
 * 获取商品信息
 * @param params
 * @returns
 */
export const getGoodsInfoApi = (params: IGetGoodsInfoBo) =>
  request.get({
    url: '/goods/queryGoodsDetail',
    params,
  });

/**
 * 获取商品资源列表
 * @param params
 * @returns
 */
export const getResourceListApi = (params: IGetResourceListBo) =>
  request.get({
    url: '/goods/queryResourceVoList',
    params,
  });

/**
 * 更新商品置顶状态
 * @param data
 * @returns
 */
export const updateGoodsToppedApi = (data: IUpdateGoodsToppedBo) =>
  request.put({
    url: '/goods/updateGoodsTopped',
    data,
  });

/**
 * 更新商品上架状态
 * @param data
 * @returns
 */
export const updateGoodsListedApi = (data: IUpdateGoodsListedBo) =>
  request.put({
    url: '/goods/updateGoodsSalesStatus',
    data,
  });

/**
 * 添加商品
 * @param data
 * @returns
 */
export const addGoodsApi = (data: IAddGoodsBo) =>
  request.post({
    url: '/goods/addGoods',
    data,
  });

/**
 * 编辑商品
 * @param data
 * @returns
 */
export const editGoodsApi = (data: IEditGoodsBo) =>
  request.put({
    url: '/goods/updateGoods',
    data,
  });

/**
 * 删除商品
 * @param goodsId
 * @returns
 */
export const deleteGoodsApi = (goodsId: number) =>
  request.delete({
    url: `/goods/deleteGoods/${goodsId}`,
  });

/**
 * 预览商品
 * @param params
 * @returns
 */
export const previewGoodsApi = (params: IPreviewGoodsBo) =>
  request.get({
    url: '/goods/queryGoodsDetailUrl',
    params,
  });
