import React, { memo, useEffect, useRef, useState } from 'react';
import { Popconfirm, Row, Space, Switch, TableProps } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  selectResourceManageCourse,
  getCourseList,
  editResourceCourse,
  deleteResourceCourse,
} from 'modules/resourceManage/course';
import Style from './index.module.less';
import { IRef } from 'components/Form';
import { IEditResourceCourseBo, IGetResourceCourseListBo } from 'types/resourceManage';
import PermissionButton from 'components/PermissionButton';
import { ImageViewer, Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { useNavigate } from 'react-router-dom';
import { strIsNull } from 'utils/tool';
import { Icon } from 'tdesign-icons-react';
import useHasPermission from 'hooks/useHasPermission';

const CourseTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total, active, addEditCourseLoading, deleteCourseLoading } =
    useAppSelector(selectResourceManageCourse);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetResourceCourseListBo = { pageNum, pageSize, subjectId: active };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'serial-number',
    'courseName',
    'coverImageUrl',
    'hoursNumber',
    'teacherName',
    'isDisable',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['teacherName', 'hoursNumber', 'createTime', 'createBy', 'updateTime', 'updateBy'];
  const renderActiveContent = () => <Icon name='check' />;
  const renderInactiveContent = () => <Icon name='close' />;
  const hasPermission = useHasPermission(['resource:course:edit']);

  /**
   * 更新禁用
   */
  const handleDisableChange = async (isDisable: 0 | 1, id: number) => {
    const bo: IEditResourceCourseBo = {
      id,
      isDisable,
    };

    const { type } = await dispatch(editResourceCourse(bo));

    if (type.endsWith('fulfilled')) {
      dispatch(getCourseList(searchParams));
    }
  };

  /**
   * 删除课程
   */
  const handleDelete = async (id: number) => {
    const { type } = await dispatch(deleteResourceCourse(id));

    if (type.endsWith('fulfilled')) {
      dispatch(getCourseList(searchParams));
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'serial-number',
      width: 80,
      title: '序号',
    },
    {
      colKey: 'courseName',
      title: '课程名称',
    },
    {
      colKey: 'coverImageUrl',
      title: '课程封面',
      width: 200,
      cell({ row }) {
        return ImageViewer({
          type: 'item',
          images: [row.coverImageUrl],
          style: {
            width: 30,
            height: 30,
          },
        });
      },
    },
    {
      colKey: 'hoursNumber',
      title: '课时数',
      width: 120,
    },
    {
      colKey: 'teacherName',
      title: '讲师',
      width: 260,
    },
    {
      colKey: 'isDisable',
      title: '禁用',
      width: 100,
      cell({ row }) {
        // eslint-disable-next-line no-nested-ternary
        return hasPermission ? (
          <Switch
            customValue={[1, 0]}
            value={row.isDisable}
            label={[renderActiveContent(), renderInactiveContent()]}
            loading={addEditCourseLoading}
            onChange={(val) => handleDisableChange(val, row.id)}
          />
        ) : row.isDisable === 1 ? (
          '是'
        ) : (
          '否'
        );
      },
    },
    {
      colKey: 'updateTime',
      title: '修改时间',
    },
    {
      colKey: 'updateBy',
      title: '修改人',
    },
    {
      colKey: 'createTime',
      title: '创建时间',
    },
    {
      colKey: 'createBy',
      title: '创建人',
    },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['resource:course:query']}
              onClick={() => navigate('/resource/course/details', { state: { id: row.id } })}
            >
              详情
            </PermissionButton>
            <PermissionButton
              permissions={['resource:course:edit']}
              onClick={() => navigate('/resource/course/edit', { state: { id: row.id } })}
            >
              修改
            </PermissionButton>
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleDelete(row.id)}>
              <>
                <PermissionButton permissions={['resource:course:remove']} theme='danger' loading={deleteCourseLoading}>
                  删除
                </PermissionButton>
              </>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    if (!strIsNull(active)) {
      dispatch(getCourseList(searchParams));
    }
  }, [active]);

  return (
    <div className={Style.container}>
      <Search
        ref={searchRef}
        method={getCourseList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '视频名称',
            field: 'courseName',
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            permissions={['resource:course:add']}
            content='添加课程'
            variant='outline'
            onClick={() => navigate('/resource/course/add', { state: { subjectId: active } })}
          />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'goodsId',
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getCourseList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(CourseTable);
