import { RootState } from '../store';
import {
  getPrivateDomainExamListApi,
  getPrivateDomainList<PERSON>pi,
  deletePrivateDomainApi,
  addOrUpdatePrivateDomainApi,
  openOrClosePrivateDomainApi,
} from 'api';
import type { IGetPrivateDomainList<PERSON>pi, IOpenOrClosePrivateDomainApi, IAddOrUpdatePrivateDomainApi } from 'api';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'privateDomainManage';

const initialState = {
  loading: true,
  formLoading: false,
  sidebarList: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  active: undefined as number | undefined,
  type: 1,
  broadsideList: [] as Array<any>,
  tablist: [] as Array<any>,
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getPrivateDomainExamList = useAsyncThunkWithStatus(`getPrivateDomainExamList`, async (_, { getState }) => {
  const {
    data: { rows },
  } = await getPrivateDomainExamListApi();
  const state = getState() as RootState;
  const currentActive = state.privateDomainManage.active;
  if (rows && rows.length > 0 && !currentActive)
    return {
      rows,
      active: rows[0].examStudentRoleId,
    };
  return { rows };
});

export const getPrivateDomainList = useAsyncThunkWithStatus('getPrivateDomainList', async (params) => {
  const {
    data: { rows, total },
  } = await getPrivateDomainListApi(params as IGetPrivateDomainListApi);

  return {
    list: rows ?? [],
    total,
  };
});

export const deletePrivateDomain = useAsyncThunkWithStatus(
  'deletePrivateDomain',
  async (id, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { pageNum, pageSize, active, type } = state.privateDomainManage;
    await deletePrivateDomainApi(id as number | string);
    await MessagePlugin.success(`删除成功`);
    await dispatch(
      getPrivateDomainList({
        pageNum,
        pageSize,
        type,
        examStudentRoleId: active,
      }),
    );
  },
);

export const openOrClosePrivateDomain = useAsyncThunkWithStatus(
  'openOrClosePrivateDomain',
  async (params, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { pageNum, pageSize, active, type } = state.privateDomainManage;
    await openOrClosePrivateDomainApi(params as IOpenOrClosePrivateDomainApi);
    await dispatch(
      getPrivateDomainList({
        pageNum,
        pageSize,
        type,
        examStudentRoleId: active,
      }),
    );
  },
);

export const addOrUpdatePrivateDomain = useAsyncThunkWithStatus(
  'addOrUpdatePrivateDomain',
  async (params, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { pageNum, pageSize, active, type } = state.privateDomainManage;
    await addOrUpdatePrivateDomainApi(params as IAddOrUpdatePrivateDomainApi);
    await dispatch(
      getPrivateDomainList({
        pageNum,
        pageSize,
        type,
        examStudentRoleId: active,
      }),
    );
  },
);

const directorySlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setActive: (state, action) => {
      state.active = action.payload;
    },
    setType: (state, action) => {
      state.type = action.payload;
    },
  },
  cases: [
    {
      thunk: getPrivateDomainExamList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.sidebarList = action.payload.rows;
        state.active = action.payload.active;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getPrivateDomainList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.tablist = action.payload?.list;
        state.total = action.payload?.total;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: addOrUpdatePrivateDomain,
      pending: (state) => {
        state.formLoading = true;
      },
      fulfilled: (state) => {
        state.formLoading = false;
      },
      rejected: (state) => {
        state.formLoading = false;
      },
    },
    {
      thunk: openOrClosePrivateDomain,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state) => {
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
  ],
});

export const { clearPageState, setActive, setType } = directorySlice.actions;

export const selectPrivateDomainManage = (state: RootState) => state.privateDomainManage;

export default directorySlice.reducer;
