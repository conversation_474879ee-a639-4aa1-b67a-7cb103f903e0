import React, { useState, memo, useCallback, useEffect } from 'react';
import { Upload, MessagePlugin, UploadProps, type UploadFile } from 'tdesign-react';
import { store } from 'modules/store';
import { selectToken } from 'modules/user';
import { getCurrentDate } from 'utils/tool';
import Style from './index.module.less';
import type { IUploads } from './types';

export const Uploads: React.FC<IUploads> = ({
  url = '/oss/uploadFile',
  params,
  tips,
  success,
  remove,
  files,
  theme,
  max,
  draggable,
  accept,
  maxFileSize,
  fileUnit,
  disabled,
}: IUploads) => {
  const { VITE_API_URL, VITE_API_URL_PREFIX } = import.meta.env;
  const HOST_URL = `${VITE_API_URL.replace(/\/+$/, '')}${VITE_API_URL_PREFIX.replace(/\/+$/, '')}`;

  const state = store.getState();
  const token = selectToken(state);

  const [fileList, setFileList] = useState<UploadFile[]>([]);

  useEffect(() => {
    setFileList(files as UploadFile[]);
  }, [files]);

  const HEADERS = {
    Authorization: `Bearer ${token}`,
  };

  const onSuccess: UploadProps['onSuccess'] = (res) => {
    if (success) {
      success(res);
    }
    MessagePlugin.success('上传成功');
  };

  const onRemove: UploadProps['onRemove'] = ({ file }) => {
    if (remove) {
      remove(file);
    }
  };

  const formatResponse: UploadProps['formatResponse'] = (res) => {
    res.uploadTime = getCurrentDate();
    if (!res) return { status: 'fail', error: '请检查网络，并重新上传' };
    if (res.code !== 200) return { status: 'fail', error: `${res.msg}` };
    return res;
  };

  const formatRequest: UploadProps['formatRequest'] = (requestData: { [key: string]: any }) => {
    const { file } = requestData;
    const data = {
      file,
      ...params,
    };
    return data;
  };

  const beforeAllFilesUpload = () => true;

  const onValidate: UploadProps['onValidate'] = (params) => {
    const { files, type } = params;
    if (type === 'FILE_OVER_SIZE_LIMIT') {
      MessagePlugin.warning(`${files.map((t) => t.name).join('、')} 等文件大小超出限制，已自动过滤`, 5000);
    } else if (type === 'FILES_OVER_LENGTH_LIMIT') {
      MessagePlugin.warning('文件数量超出限制，仅上传未超出数量的文件');
    } else if (type === 'FILTER_FILE_SAME_NAME') {
      MessagePlugin.warning('不允许上传同名文件');
    }
  };

  const handleChange: UploadProps['onChange'] = useCallback((files: UploadFile[]) => {
    setFileList(files);
  }, []);

  const handleFail: UploadProps['onFail'] = useCallback(({ file }: UploadFile) => {
    if (file.status === 'fail') {
      MessagePlugin.error(`文件 【${file.name}】 上传失败\n\n，${file.response?.error}`);
    }
  }, []);

  const beforeUpload: UploadProps['beforeUpload'] = useCallback(
    (file: UploadFile) =>
      new Promise<boolean>((resolve) => {
        // 文件格式限制
        if (accept) {
          const sanitizedAccept = accept?.replace(/\s+/g, '');
          if (
            sanitizedAccept &&
            !new RegExp(`(${sanitizedAccept.split(',').join('|')})$`, 'i').test(file.name as string)
          ) {
            MessagePlugin.warning(`文件 【${file.name}】 格式不正确，请上传 ${accept} 格式的文件`);
            resolve(false);
            return;
          }
        }
        if (fileUnit === 'kb') {
          if (maxFileSize && file.size && file.size / 1024 > maxFileSize) {
            MessagePlugin.warning(`文件 【${file.name}】 超过最大限制 ${maxFileSize}kb`);
            resolve(false);
            return;
          }
        } else {
          // 文件大小限制
          // eslint-disable-next-line no-lonely-if
          if (maxFileSize && file.size && file.size / 1024 / 1024 > maxFileSize) {
            MessagePlugin.warning(`文件 【${file.name}】 超过最大限制 ${maxFileSize}MB`);
            resolve(false);
            return;
          }
        }
        resolve(true);
      }),
    [accept, maxFileSize],
  );

  return (
    <Upload
      className={Style.upload}
      action={`${HOST_URL}${url}`}
      theme={theme ?? 'file'}
      autoUpload={true}
      accept={accept ?? undefined}
      data={params}
      draggable={!draggable}
      files={fileList}
      formatResponse={formatResponse}
      formatRequest={formatRequest}
      onChange={handleChange}
      onFail={handleFail}
      onSuccess={onSuccess}
      onRemove={onRemove}
      tips={tips ?? '如果题干中有图片，请尽量选择添加单个题目，或在导入后进行检查，以免题目样式有误'}
      headers={HEADERS}
      useMockProgress={true}
      beforeAllFilesUpload={beforeAllFilesUpload}
      mockProgressDuration={0}
      beforeUpload={beforeUpload}
      multiple={max !== 1}
      max={max ?? 10}
      onValidate={onValidate}
      disabled={disabled}
    />
  );
};

export default memo(Uploads);
