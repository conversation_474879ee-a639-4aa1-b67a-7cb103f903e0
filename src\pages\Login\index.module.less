@import "./src/styles/font-family.less";

.loginWrapper {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-position: 100%;
  position: relative;
  &.dark {
    background-color: var(--td-bg-color-page);
    // background-image: url('assets/image/assets-login-bg-black.png');
  }
  &.dark::before {
    content: "";
    position: absolute;
    width: 100%;
    background-image: url('assets/image/assets-login-bg-black.png');
    height: 100%;
    top: 0;
    left: 0;
    background-size: 100% 100%;
    transform:scaleX(-1);
  }
  &.light {
    background-color: white;
    // background-image: url('assets/image/assets-login-bg-white.png');
  }
  &.light::before {
    content: "";
    position: absolute;
    width: 100%;
    background-image: url('assets/image/assets-login-bg-white.png');
    height: 100%;
    top: 0;
    left: 0;
    background-size: 100% 100%;
    transform:scaleX(-1);
  }

}


.loginContainer {
  position: absolute;
  top: 22%;
  right: 10%;
  min-height: 500px;
  line-height: 22px;
}

.title {
  font-family: JinBuTi;
  font-size: 46px;
  line-height: 44px;
  color: var(--td-text-color-primary);
  margin-top: 4px;
  margin-bottom: 0;
}

.subTitle {
  margin-top: 16px;
}

.tip {
  display: inline-block;
  margin-right: 8px;
  font-size: 14px;
  margin-top: 0;
  margin-bottom: 0;

  &.registerTip {
    color: var(--td-text-color-secondary);
  }

  &.loginTip {
    color: var(--td-text-color-primary);
    cursor: pointer;
  }
}

.copyright {
  font-size: 14px;
  position: absolute;
  left: 5%;
  bottom: 64px;
  color: var(--td-text-color-secondary);
}

@media screen and (max-height: 762px) {
  .copyright {
    display: none;
  }
}
