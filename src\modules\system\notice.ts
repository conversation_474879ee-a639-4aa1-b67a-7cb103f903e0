import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getSystemNoticeListApi,
  delSystemNoticeApi,
  addSystemNoticeApi,
  editSystemNoticeApi,
  getSystemNoticeDetailsInfoApi,
} from 'api';
import { MessagePlugin } from 'tdesign-react/es/message/Message';
import { IAddNoticeBo, IEditNoticeBo, IGetNoticeListBo } from 'types/system';

const namespace = 'system/notice';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  addEditLoading: boolean;
  delLoading: boolean;
  infoLoading: boolean;
  info: any;
}

const initialState: IInitialState = {
  loading: true,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  addEditLoading: false,
  delLoading: false,
  infoLoading: false,
  info: {},
};

export const getSystemNoticeList = createAsyncThunk(
  `${namespace}/getSystemNoticeList`,
  async (bo: IGetNoticeListBo) => {
    const data = await getSystemNoticeListApi(bo);

    return {
      list: data.rows,
      total: data.total,
      pageNum: bo.pageNum,
      pageSize: bo.pageSize,
    };
  },
);

export const addSystemNotice = createAsyncThunk(`${namespace}/addSystemNotice`, async (bo: IAddNoticeBo) => {
  const { data } = await addSystemNoticeApi(bo);

  return data;
});

export const editSystemNotice = createAsyncThunk(`${namespace}/editSystemNotice`, async (bo: IEditNoticeBo) => {
  await editSystemNoticeApi(bo);
});

export const delSystemNotice = createAsyncThunk(`${namespace}/delSystemNotice`, async (noticeId: number | string) => {
  await delSystemNoticeApi(noticeId);
});

export const getSystemNoticeDetailsInfo = createAsyncThunk(
  `${namespace}/getSystemNoticeDetailsInfo`,
  async (noticeId: number) => {
    const { data } = await getSystemNoticeDetailsInfoApi(noticeId);

    return data;
  },
);

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSystemNoticeList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSystemNoticeList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getSystemNoticeList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(addSystemNotice.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(addSystemNotice.fulfilled, (state) => {
        MessagePlugin.success('添加成功');
        state.addEditLoading = false;
      })
      .addCase(addSystemNotice.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(editSystemNotice.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(editSystemNotice.fulfilled, (state) => {
        MessagePlugin.success('编辑成功');
        state.addEditLoading = false;
      })
      .addCase(editSystemNotice.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(delSystemNotice.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(delSystemNotice.fulfilled, (state) => {
        MessagePlugin.success('删除成功');
        state.delLoading = false;
      })
      .addCase(delSystemNotice.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(getSystemNoticeDetailsInfo.pending, (state) => {
        state.infoLoading = true;
      })
      .addCase(getSystemNoticeDetailsInfo.fulfilled, (state, action) => {
        state.info = action.payload;
        state.infoLoading = false;
      })
      .addCase(getSystemNoticeDetailsInfo.rejected, (state) => {
        state.infoLoading = false;
      });
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectSystemNotice = (state: RootState) => state.systemNotice;

export default listBaseSlice.reducer;
