import request from 'utils/request/index';

export interface IGetBulletinTrendsListApi {
  pageNum: number;
  pageSize: number;
  title?: string;
  examIdList?: (string | number)[];
}

/**
 * 分页条件查询动态列表
 * @returns
 */
export const getBulletinTrendsListApi = async (params: IGetBulletinTrendsListApi) => {
  const result = await request.get({
    url: `/bulletinTrend/getBulletinTrendsList?pageNum=${params.pageNum}&pageSize=${params.pageSize}&examIdList=${params.examIdList ?? ''
      }&title=${params.title ?? ''}`,
  });
  return result;
};

export interface IDeleteTrendsApi {
  title?: string;

  /** 考试id集合 */
  examIdList?: number[];

  /** 动态id集合 */
  trendsIdList: number[];
}

/**
 * 删除资讯信息
 * @returns
 */
export const deleteTrendsApi = async (params: IDeleteTrendsApi) => {
  const result = await request.delete({
    url: `/bulletinTrend/deleteTrends`,
    params,
  });
  return result;
};

export interface IAddOrUpdateTrendApi {
  /** 动态Id(修改传入) */
  trendsId: number;

  /** 标题 */
  title: string;

  /** 标签 */
  tags: string;

  /** 封面(文件id) */
  cover: number;

  /** 层级(1:一级，2:二级) */
  level: string;

  /** 内容 */
  content: string;

  /** 考试id集合 */
  examIdList: number[];

  /** 院校id集合 */
  educationalIdList: number[];
}

/**
 * 新增/修改动态
 * @returns
 */
export const addOrUpdateTrendApi = async (params: IAddOrUpdateTrendApi) => {
  const result = await request.post({
    url: `/bulletinTrend/addOrUpdateTrend`,
    params,
  });
  return result;
};

export interface IOpenOrCloseTrendsApi {
  /** 动态id */
  trendsId: number;

  /** 是否开启(1-是，0-否) */
  isOpen: number;
}
export interface IbulletinTrendApi {
  /** 动态id */
  trendsId: number;

  /** 是否开启(1-是，0-否) */
  isTop: number;
}

/**
 * 开启/关闭动态信息
 * @returns
 */
export const openOrCloseTrendsApi = async (params: IOpenOrCloseTrendsApi) => {
  const result = await request.post({
    url: `/bulletinTrend/openOrCloseTrends?isOpen=${params.isOpen}&trendsId=${params.trendsId}`,
  });
  return result;
};

/**
 * 开启/关闭 置顶
 * @returns
 */
export const openOrCloseBulletinTrendApi = async (params: IbulletinTrendApi) => {
  const result = await request.post({
    url: `/bulletinTrend/upTrends?isTop=${params.isTop}&trendsId=${params.trendsId}`,
  });
  return result;
};
