import React, { useRef, useState } from 'react';
import { Form, Row, Col, Input, Button, MessagePlugin, Loading, Space, Select, type FormProps } from 'tdesign-react';
import { SubmitContext, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { addOrUpdateMajorsCategory, selectSpeciality } from 'modules/directory/speciality';
import Style from './index.module.less';

const { FormItem } = Form;

export type FormValueType = {
  majorsCategoryId: number;
};

export interface FormParams {
  onCancel: () => void;
  success: () => void;
  type: string;
  row: FormValueType;
}

enum EducationalLevel {
  Specialized = 1, // 专科
  Undergraduate = 2, // 本科
}

const educationalLevelOptions = [
  { label: '专科', value: EducationalLevel.Specialized },
  { label: '本科', value: EducationalLevel.Undergraduate },
];
const SearchForm: React.FC<FormParams> = ({ success, onCancel, type, row }) => {
  const append = {
    categoryName: '',
    parentId: row?.majorsCategoryId || null,
  };

  const addForm = {
    categoryName: '',
    eduLevel: '',
    majorsCategoryId: '',
  };

  const editForm = {
    ...row,
  };

  const INITIAL_DATA = (() => {
    if (type === 'add') return addForm;
    if (type === 'append') return append;
    return editForm;
  })();

  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>(null);
  const [value, setValue] = useState('');
  const { formLoading } = useAppSelector(selectSpeciality);

  const onChange = (value: string) => {
    setValue(value);
  };

  const onSubmits = async (e: SubmitContext) => {
    if (e && e.validateResult === true) {
      await dispatch(
        addOrUpdateMajorsCategory({
          ...formRef.current?.getFieldsValue?.(true),
          parentId: type === 'append' ? row.majorsCategoryId : INITIAL_DATA.parentId,
          majorsCategoryId: INITIAL_DATA.majorsCategoryId,
        }),
        console.log(formRef.current, '...formRef.current?.getFieldsValue?.(true)'),
      );
      MessagePlugin.success('修改成功');
      success();
    }
  };

  const onReset: FormProps['onReset'] = () => {
    onCancel();
  };

  return (
    <Loading loading={formLoading} showOverlay>
      <Form className={Style.form} ref={formRef} onSubmit={onSubmits} labelAlign='top' onReset={onReset}>
        <Row gutter={[32, 24]}>
          <Col span={12}>
            <FormItem
              label='大类名称'
              name='categoryName'
              initialData={INITIAL_DATA.categoryName}
              rules={[{ required: true, message: '大类名称必填', type: 'error' }]}
            >
              <Input placeholder='请输入科目名称' maxlength={16} showLimitNumber />
            </FormItem>
          </Col>
          {(type === 'add' || ((row?.parentId === undefined || row?.parentId === null) && type === 'edit')) && (
            <Col span={12}>
              <FormItem
                label='专科/本科'
                name='eduLevel'
                initialData={INITIAL_DATA.eduLevel}
                rules={[{ required: true, message: '专科/本科必选', type: 'error' }]}
              >
                <Select value={value} onChange={onChange} clearable options={educationalLevelOptions} />
              </FormItem>
            </Col>
          )}
          <Col span={12}>
            <FormItem style={{ marginRight: 0 }}>
              <Space>
                <Button type='reset' theme='default'>
                  取消
                </Button>
                <Button type='submit' theme='primary'>
                  提交
                </Button>
              </Space>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Loading>
  );
};

export default SearchForm;
