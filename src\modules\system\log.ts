import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getSystemOperLogListApi,
  delSystemOperLogApi,
  clearSystemOperLogApi,
  getSystemLoginLogListApi,
  delSystemLoginLogApi,
  clearSystemLoginLogApi,
  unlockLogininforApi,
} from 'api';
import { MessagePlugin } from 'tdesign-react/es/message/Message';
import { IGetOperLogListBo } from 'types/system';

const namespace = 'system/log';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  delLoading: boolean;
  unlockLoading: boolean;
}

const initialState: IInitialState = {
  loading: true,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  delLoading: false,
  unlockLoading: false,
};

export const getSystemOperLogList = createAsyncThunk(
  `${namespace}/getSystemOperLogList`,
  async (bo: IGetOperLogListBo) => {
    const data = await getSystemOperLogListApi(bo);

    return {
      list: data.rows,
      total: data.total,
      pageNum: bo.pageNum,
      pageSize: bo.pageSize,
    };
  },
);

export const delSystemOperLog = createAsyncThunk(
  `${namespace}/delSystemOperLog`,
  async (operLogId: number | string) => {
    await delSystemOperLogApi(operLogId);
  },
);

export const clearSystemOperLog = createAsyncThunk(`${namespace}/clearSystemOperLog`, async () => {
  await clearSystemOperLogApi();
});

export const getSystemLoginLogList = createAsyncThunk(
  `${namespace}/getSystemLoginLogList`,
  async (bo: IGetOperLogListBo) => {
    const data = await getSystemLoginLogListApi(bo);

    return {
      list: data.rows,
      total: data.total,
      pageNum: bo.pageNum,
      pageSize: bo.pageSize,
    };
  },
);

export const delSystemLoginLog = createAsyncThunk(
  `${namespace}/delSystemLoginLog`,
  async (loginLogId: number | string) => {
    await delSystemLoginLogApi(loginLogId);
  },
);

export const clearSystemLoginLog = createAsyncThunk(`${namespace}/clearSystemLoginLog`, async () => {
  await clearSystemLoginLogApi();
});

export const unlockLogininfor = createAsyncThunk(`${namespace}/unlockLogininfor`, async (userName: string) => {
  await unlockLogininforApi(userName);
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSystemOperLogList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSystemOperLogList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getSystemOperLogList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(delSystemOperLog.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(delSystemOperLog.fulfilled, (state) => {
        MessagePlugin.success('删除成功');
        state.delLoading = false;
      })
      .addCase(delSystemOperLog.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(clearSystemOperLog.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(clearSystemOperLog.fulfilled, (state) => {
        MessagePlugin.success('清空成功');
        state.delLoading = false;
      })
      .addCase(clearSystemOperLog.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(getSystemLoginLogList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSystemLoginLogList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getSystemLoginLogList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(delSystemLoginLog.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(delSystemLoginLog.fulfilled, (state) => {
        MessagePlugin.success('删除成功');
        state.delLoading = false;
      })
      .addCase(delSystemLoginLog.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(clearSystemLoginLog.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(clearSystemLoginLog.fulfilled, (state) => {
        MessagePlugin.success('清空成功');
        state.delLoading = false;
      })
      .addCase(clearSystemLoginLog.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(unlockLogininfor.pending, (state) => {
        state.unlockLoading = true;
      })
      .addCase(unlockLogininfor.fulfilled, (state) => {
        MessagePlugin.success('解锁成功');
        state.unlockLoading = false;
      })
      .addCase(unlockLogininfor.rejected, (state) => {
        state.unlockLoading = false;
      });
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectSystemLog = (state: RootState) => state.systemLog;

export default listBaseSlice.reducer;
