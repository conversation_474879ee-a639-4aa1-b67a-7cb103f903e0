import React, { memo, useEffect, useRef, useState } from 'react';
import { DialogPlugin, Row, Space, TableProps, Tag } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  selectSystemLog,
  getSystemLoginLogList,
  delSystemLoginLog,
  clearSystemLoginLog,
  unlockLogininfor,
} from 'modules/system/log';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IRef } from 'components/Form';
import { IGetLoginLogListBo } from 'types/system';
import PermissionButton from 'components/PermissionButton';
import { Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { downLoad } from 'utils/tool';

const LoginLogTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const { loading, list, pageNum, pageSize, total, delLoading, unlockLoading } = useAppSelector(selectSystemLog);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetLoginLogListBo = { pageNum, pageSize };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'row-select',
    'infoId',
    'userName',
    'ipaddr',
    'loginLocation',
    'browser',
    'os',
    'status',
    'msg',
    'loginTime',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['msg', 'loginTime'];
  const [userName, setUserName] = useState('');

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  /**
   * 批量删除登录日志
   */
  const handleBatchDelete = async () => {
    const confirmDia = DialogPlugin.confirm({
      header: '批量删除提示',
      body: '确定批量删除该数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      confirmLoading: delLoading,
      onConfirm: async () => {
        const { type } = await dispatch(delSystemLoginLog(selectedRowKeys.join(',')));

        if (type.endsWith('fulfilled')) {
          handleResetSelection();
          confirmDia.hide();
          dispatch(getSystemLoginLogList(searchParams));
        }
      },
      onClose: () => {
        confirmDia.hide();
      },
    });

    return false;
  };

  /**
   * 清空登录日志
   */
  const handleClear = async () => {
    const confirmDia = DialogPlugin.confirm({
      header: '系统提示',
      body: '是否确认清空所有登录日志数据项？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        const { type } = await dispatch(clearSystemLoginLog());

        if (type.endsWith('fulfilled')) {
          confirmDia.hide();
          dispatch(getSystemLoginLogList(searchParams));
        }
      },
      onClose: () => {
        confirmDia.hide();
      },
    });

    return false;
  };

  const handleUnlock = () => {
    const confirmDia = DialogPlugin.confirm({
      header: '系统提示',
      body: `是否确认解锁用户"${userName}"数据项？`,
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        const { type } = await dispatch(unlockLogininfor(userName));

        if (type.endsWith('fulfilled')) {
          handleResetSelection();
          confirmDia.hide();
          dispatch(getSystemLoginLogList(searchParams));
        }
      },
      onClose: () => {
        confirmDia.hide();
      },
    });

    return false;
  };

  /**
   * 导出登录日志
   */
  const handleExport = async () => {
    try {
      setExportLoading(true);

      const params = searchRef.current?.getFormParams();

      await downLoad(
        '/monitor/logininfor/export',
        {
          ...params,
        },
        `登录日志_${new Date().getTime()}`,
      );

      setExportLoading(false);
    } catch (error) {
      setExportLoading(false);
    }
  };

  const handleSelectChange: TableProps['onSelectChange'] = (val, { selectedRowData }) => {
    const list = selectedRowData.map((item) => item.userName);
    setUserName(list ? list[0] : '');

    onSelectChange(val);
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    {
      colKey: 'infoId',
      width: 120,
      title: '访问编号',
    },
    {
      colKey: 'userName',
      title: '用户名称',
    },
    {
      colKey: 'ipaddr',
      title: '登录地址',
    },
    {
      colKey: 'loginLocation',
      title: '登录地点',
    },
    {
      colKey: 'browser',
      title: '浏览器',
    },
    {
      colKey: 'os',
      title: '操作系统',
    },
    {
      colKey: 'status',
      title: '登录状态',
      cell({ row }) {
        return (
          <Tag theme={row.status === '0' ? 'primary' : 'danger'} variant='light'>
            {row.status === '0' ? '成功' : '失败'}
          </Tag>
        );
      },
    },
    {
      colKey: 'msg',
      title: '操作信息',
    },
    {
      colKey: 'loginTime',
      title: '登录时间',
    },
  ];

  useEffect(() => {
    dispatch(getSystemLoginLogList(searchParams));
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getSystemLoginLogList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '登录地址',
            field: 'ipaddr',
          },
          {
            type: 'input',
            label: '用户名称',
            field: 'userName',
          },
          {
            type: 'select',
            label: '状态',
            field: 'status',
            options: [
              {
                label: '成功',
                value: '0',
              },
              {
                label: '失败',
                value: '1',
              },
            ],
          },
          {
            type: 'datePicker',
            label: '登录时间',
            field: 'dateArr',
            isTime: true,
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            disabled={selectedRowKeys.length === 0}
            permissions={['monitor:logininfor:remove']}
            content='批量删除'
            variant='outline'
            theme='danger'
            onClick={() => handleBatchDelete()}
          />
          <PermissionButton
            permissions={['monitor:logininfor:remove']}
            content='清空'
            variant='outline'
            theme='danger'
            onClick={() => handleClear()}
          />
          <PermissionButton
            disabled={selectedRowKeys.length !== 1}
            permissions={['monitor:logininfor:remove']}
            content='解锁'
            variant='outline'
            theme='primary'
            onClick={() => handleUnlock()}
          />
          <PermissionButton
            loading={exportLoading}
            permissions={['monitor:logininfor:export']}
            content='导出'
            variant='outline'
            theme='warning'
            onClick={() => handleExport()}
          />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'infoId',
          selectedRowKeys,
          onSelectChange: handleSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getSystemLoginLogList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(LoginLogTable);
