import {
  IAddResourceCourseBo,
  IAddResourceCourseChapterBo,
  IEditResourceCourseBo,
  IEditResourceCourseChapterBo,
  IGetResourceCourseListBo,
  IPreviewVideoBo,
} from 'types/resourceManage';
import request from 'utils/request/index';

/**
 * 获取课程列表
 * @param params
 * @returns
 */
export const getResourceCourseListApi = (params: IGetResourceCourseListBo) =>
  request.get({
    url: '/course/getList',
    params,
  });

/**
 * 获取课程信息
 * @param id
 * @returns
 */
export const getResourceCourseInfoApi = (id: number) =>
  request.get({
    url: `/course/getById/${id}`,
  });

/**
 * 获取讲师列表
 * @returns
 */
export const getTeacherListApi = () =>
  request.get({
    url: '/teacher/getTeacherBoxList',
  });

/**
 * 添加课程
 * @param data
 * @returns
 */
export const addResourceCourseApi = (data: IAddResourceCourseBo) =>
  request.post({
    url: '/course/save',
    data,
  });

/**
 * 编辑课程
 * @param data
 * @returns
 */
export const editResourceCourseApi = (data: IEditResourceCourseBo) =>
  request.put({
    url: '/course/edit',
    data,
  });

/**
 * 删除课程
 * @param id
 * @returns
 */
export const deleteResourceCourseApi = (id: number) =>
  request.delete({
    url: `/course/delete/${id}`,
  });

/**
 * 获取章节树列表
 * @param courseId
 * @returns
 */
export const getResourceCourseChapterTreeListApi = (courseId: number) =>
  request.get({
    url: `/courseChapter/getTreeByCourseId/${courseId}`,
  });

/**
 * 获取章节信息
 * @param chapterId
 * @returns
 */
export const getResourceCourseChapterInfoApi = (chapterId: number) =>
  request.get({
    url: `/courseChapter/getById/${chapterId}`,
  });

/**
 * 添加章节
 * @param data
 * @returns
 */
export const addResourceCourseChapterApi = (data: IAddResourceCourseChapterBo) =>
  request.post({
    url: '/courseChapter/save',
    data,
  });

/**
 * 编辑章节
 * @param data
 * @returns
 */
export const editResourceCourseChapterApi = (data: IEditResourceCourseChapterBo) =>
  request.put({
    url: '/courseChapter/edit',
    data,
  });

/**
 * 删除章节
 * @param chapterId
 * @returns
 */
export const deleteResourceCourseChapterApi = (chapterId: number) =>
  request.delete({
    url: `/courseChapter/delete/${chapterId}`,
  });

/**
 * 预览视频
 * @param data
 * @returns
 */
export const previewVideoApi = (data: IPreviewVideoBo) =>
  request.post({
    url: '/courseChapter/pvwVideo',
    data,
  });
