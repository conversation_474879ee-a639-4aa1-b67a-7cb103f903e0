import React, { useState, useEffect } from 'react';
import { Menu } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { setActive, setActiveName, selectAssociation } from 'modules/directory/association';
import Style from './index.module.less';
import './index.less';

interface IListItemData<T = any> {
  listProps?: object;
  style?: object;
  title?: string;
  description?: string;
  image?: string;
  action?: React.ReactElement;
  listItemProps?: T;
  id: T;
  name: string;
  subjectId: number;
  subjectName: string;
}

interface IListData<T = any> {
  listProps?: object;
  IListItemData: IListItemData[];
  active?: T;
  broadsideList: IListItemData[];
}

export interface IData extends React.HTMLAttributes<HTMLElement> {
  broadsideList: IListData;
}

const { MenuItem } = Menu;

const BasicList: React.FC<IData> = ({ broadsideList }) => {
  const dispatch = useAppDispatch();

  const { active } = useAppSelector(selectAssociation);
  const [listItemData, setListItemData] = useState(broadsideList);

  useEffect(() => {
    setListItemData(broadsideList);
  }, [broadsideList]);

  return (
    <React.Fragment>
      <Menu
        value={active}
        onChange={(v) => {
          dispatch(setActive(v as number));
          dispatch(setActiveName(listItemData.find((item) => item.educationalId === v)?.educationalName));
        }}
        style={{ marginRight: 20 }}
      >
        {listItemData.length !== 0 &&
          listItemData.map((item) => (
            <MenuItem style={{ width: '100%' }} value={item.educationalId} key={item.educationalId}>
              <div className={Style.content}>
                <span>{item.educationalName}</span>
              </div>
            </MenuItem>
          ))}
        {listItemData.length === 0 && <div style={{ textAlign: 'center' }}>暂无数据</div>}
      </Menu>
    </React.Fragment>
  );
};

export default BasicList;
