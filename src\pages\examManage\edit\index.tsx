import React, { memo, useRef, useEffect } from 'react';
import { Form, Row, Col, Button, DatePicker, MessagePlugin, Checkbox, Loading } from 'tdesign-react';
import classnames from 'classnames';
import { SubmitContext, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { updateExam, getEducationalTypeAll, getCandidateTypeAll, selectExamManage } from 'modules/examManage';
import { useLocation, useNavigate } from 'react-router-dom';
import { Back, Input, Textarea } from 'components';

const { FormItem } = Form;

export default memo(() => {
  const navigate = useNavigate();
  const { state } = useLocation();
  const INITIAL_DATA = {
    fullName: '',
    examTime: '',
    candidateTypeIdList: [],
    educationalTypeIdList: [],
    examIntroduction: '',
  };
  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>();
  const { candidateTypeAll, educationalTypeAll, formLoading } = useAppSelector(selectExamManage);
  const getDataList = async () => {
    await dispatch(getCandidateTypeAll());
    await dispatch(getEducationalTypeAll());
  };
  useEffect(() => {
    getDataList();
  }, [dispatch]);

  useEffect(() => {
    const canIds = () => {
      const ids = state.candidateTypeVoList && state.candidateTypeVoList.map((i: any) => i.candidateTypeId);
      if (!ids || !ids.length) return false;
      return ids;
    };

    const eduIds = () => {
      const ids = state.educationalTypeVoList && state.educationalTypeVoList.map((i: any) => i.educationalTypeId);
      if (!ids || !ids.length) return false;
      return ids;
    };

    const EDIT_DATA = {
      fullName: state.fullName ?? '',
      examTime: state.examTime ?? '',
      candidateTypeIdList: canIds() ?? [],
      educationalTypeIdList: eduIds() ?? [],
      examIntroduction: state.examIntroduction ?? '',
    };
    formRef.current?.setFieldsValue?.(EDIT_DATA);
  }, [useLocation]);

  const onSubmit = async (e: SubmitContext) => {
    if (e.validateResult === true) {
      try {
        const params: object | undefined = {
          examId: state.examId,
          ...formRef.current?.getFieldsValue?.(true),
        };
        await dispatch(updateExam(params)).unwrap();
        MessagePlugin.success('修改成功');
        navigate('/examManage');
      } catch (error) {
        MessagePlugin.error('修改失败');
      }
    }
  };

  return (
    <>
      <Back path='/examManage' header='单招考试'></Back>
      <div className={classnames(CommonStyle.pageWithColor)}>
        <div className={Style.formContainer}>
          <Loading loading={formLoading} showOverlay>
            <Form ref={formRef} onSubmit={onSubmit} labelWidth={100} labelAlign='top'>
              <div className={Style.titleBox}></div>
              <Row gutter={[32, 44]}>
                <Col span={12}>
                  <Input
                    label='考试全称'
                    maxlength={13}
                    initialData={INITIAL_DATA.fullName}
                    rules={[{ required: true, message: '考试全称必填', type: 'error' }]}
                    name='fullName'
                    placeholder='请输入考试全称'
                  />
                  {/* </FormItem> */}
                </Col>
                <Col span={20} className={Style.dateCol}>
                  <FormItem
                    label='考试时间'
                    name='examTime'
                    initialData={INITIAL_DATA.examTime}
                    rules={[{ required: true }]}
                  >
                    <DatePicker mode='date' />
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label='适用对象'
                    name='candidateTypeIdList'
                    initialData={INITIAL_DATA.candidateTypeIdList}
                    rules={[{ required: true, message: '适用对象必选', type: 'error' }]}
                  >
                    <Checkbox.Group>
                      {candidateTypeAll &&
                        candidateTypeAll.map((i: any) => (
                          <Checkbox key={i.candidateTypeId} value={i.candidateTypeId}>
                            {i.candidateTypeName}
                          </Checkbox>
                        ))}
                    </Checkbox.Group>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label='报考院校'
                    name='educationalTypeIdList'
                    initialData={INITIAL_DATA.educationalTypeIdList}
                    rules={[{ required: true, message: '报考院校必选', type: 'error' }]}
                  >
                    <Checkbox.Group>
                      {educationalTypeAll &&
                        educationalTypeAll.map((i: any) => (
                          <Checkbox key={i.educationalTypeId} value={i.educationalTypeId}>
                            {i.educationalTypeName}
                          </Checkbox>
                        ))}
                    </Checkbox.Group>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <Textarea
                    label='考试简介'
                    name='examIntroduction'
                    initialData={INITIAL_DATA.examIntroduction}
                    rules={[{ required: true, message: '考试简介必填', type: 'error' }]}
                    maxlength={72}
                  />
                </Col>
                <Col span={12}>
                  <FormItem>
                    <Button type='submit' theme='primary'>
                      提交
                    </Button>
                    <Button type='reset' style={{ marginLeft: 12 }}>
                      重置
                    </Button>
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </Loading>
        </div>
      </div>
    </>
  );
});
