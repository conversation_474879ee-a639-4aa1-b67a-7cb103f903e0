import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { MessagePlugin } from 'tdesign-react';
import {
  getExamAllListApi,
  updateExamApi,
  getEducationalType<PERSON>ll<PERSON>pi,
  getCandidateType<PERSON>llApi,
  getSubjectListByQueryIdApi,
  addExamSubjectApi,
  examDragSortApi,
  examSubjectDragSortApi,
} from 'api';
import type { ExamAllListType, IExamDragSortApi } from 'api';

const namespace = 'examManage';

interface candidateType {
  candidateTypeId: number;
  candidateTypeName: string;
}

interface educationalType {
  educationalTypeId: number;
  educationalTypeName: string;
}

export interface IExamSubjectDragSortBo {
  id: string;
  position: number;
  parentId: number;
}

interface IInitialState {
  loading: boolean;
  formLoading: boolean;
  pageNum: number;
  pageSize: number;
  total: number;
  contractList: ExamAllListType[];
  candidateTypeAll: candidateType[];
  educationalTypeAll: educationalType[];
  subject: {
    loading: boolean;
    pageNum: number;
    pageSize: number;
    total: number;
    contractList: ExamAllListType[];
    dragSortLoading: boolean;
  };
}

const initialState: IInitialState = {
  loading: true,
  formLoading: false,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  contractList: [],
  candidateTypeAll: [],
  educationalTypeAll: [],
  subject: {
    loading: true,
    pageNum: 1,
    pageSize: 10,
    total: 0,
    contractList: [],
    dragSortLoading: false,
  },
};

export const updateExam = createAsyncThunk(`${namespace}/updateExam`, async (params: object | undefined) => {
  await updateExamApi(params);
});

export const getList = createAsyncThunk(
  `${namespace}/getList`,
  async (params: { pageSize: number; pageNum: number }) => {
    const {
      data: { rows, total },
    } = await getExamAllListApi();
    return {
      list: rows,
      total,
      pageSize: params.pageSize,
      current: params.pageNum,
    };
  },
);

export const examDragSort = createAsyncThunk(
  `${namespace}/examDragSort`,
  async (params: IExamDragSortApi, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { pageNum, pageSize } = state.examManage;
    await examDragSortApi(params);
    await dispatch(
      getList({
        pageNum,
        pageSize,
      }),
    );
  },
);

/**
 * 适用对象枚举查询
 */
export const getCandidateTypeAll = createAsyncThunk(`${namespace}/getCandidateTypeAll`, async () => {
  const {
    data: { rows },
  } = await getCandidateTypeAllApi();
  return rows;
});

/**
 * 报考院校枚举查询
 */
export const getEducationalTypeAll = createAsyncThunk(`${namespace}/getEducationalTypeAll`, async () => {
  const {
    data: { rows },
  } = await getEducationalTypeAllApi();
  return rows;
});

/**
 * 根据考试id，或科目id集合查询考试关联科目列表
 */
export const getSubjectListByQueryId = createAsyncThunk(
  `${namespace}/getSubjectListByQueryId`,
  async (params: { pageSize: number; pageNum: number; examId: number; subjectName: any }) => {
    const {
      data: { rows, total },
    } = await getSubjectListByQueryIdApi(params);
    return {
      list: rows,
      total,
      pageSize: params.pageSize,
      pageNum: params.pageNum,
    };
  },
);

/**
 * 考试关联科目
 */
export const addExamSubject = createAsyncThunk(
  `${namespace}/addExamSubject`,
  async (data: { examId: number; subjectId: number; relation: number }) => {
    try {
      await addExamSubjectApi(data);
      MessagePlugin.success('操作成功');
    } catch (error) {
      MessagePlugin.error(error?.message);
    }
  },
);

/**
 * 关联科目排序
 */
export const examSubjectDragSort = createAsyncThunk(
  `${namespace}/examSubjectDragSort`,
  async (bo: IExamSubjectDragSortBo, { dispatch }) => {
    await examSubjectDragSortApi(bo);

    dispatch(getSubjectListByQueryId({ pageNum: 1, pageSize: 10, examId: bo.parentId, subjectName: '' }));
  },
);

const ExamManageSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getList.fulfilled, (state, action) => {
        state.loading = false;
        state.contractList = action.payload?.list;
        state.total = action.payload?.total;
        state.pageSize = action.payload?.pageSize;
        state.pageNum = action.payload?.current;
      })
      .addCase(getList.rejected, (state) => {
        state.loading = false;
      })
      .addCase(updateExam.pending, (state) => {
        state.formLoading = true;
      })
      .addCase(updateExam.fulfilled, (state) => {
        state.formLoading = false;
      })
      .addCase(updateExam.rejected, (state) => {
        state.formLoading = false;
      })
      .addCase(getCandidateTypeAll.fulfilled, (state, action) => {
        state.candidateTypeAll = action.payload;
      })
      .addCase(getEducationalTypeAll.fulfilled, (state, action) => {
        state.educationalTypeAll = action.payload;
      })

      .addCase(getSubjectListByQueryId.pending, (state) => {
        state.subject.loading = true;
      })
      .addCase(getSubjectListByQueryId.fulfilled, (state, action) => {
        state.subject.loading = false;
        state.subject.contractList = action.payload?.list;
        state.subject.total = action.payload?.total;
        state.subject.pageSize = action.payload?.pageSize;
        state.subject.pageNum = action.payload?.pageNum;
      })
      .addCase(getSubjectListByQueryId.rejected, (state) => {
        state.subject.loading = false;
      })

      .addCase(examSubjectDragSort.pending, (state) => {
        state.subject.dragSortLoading = true;
      })
      .addCase(examSubjectDragSort.fulfilled, (state) => {
        state.subject.dragSortLoading = false;
      })
      .addCase(examSubjectDragSort.rejected, (state) => {
        state.subject.dragSortLoading = false;
      });
  },
});

export const { clearPageState } = ExamManageSlice.actions;

export const selectExamManage = (state: RootState) => state.examManage;

export default ExamManageSlice.reducer;
