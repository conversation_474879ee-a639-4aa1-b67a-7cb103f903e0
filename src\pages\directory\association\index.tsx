import React, { useEffect, useRef } from 'react';
import { Input, type InputValue } from 'tdesign-react';
import { useLocation } from 'react-router-dom';
import { SearchIcon } from 'tdesign-icons-react';
import classnames from 'classnames';
import { SelectTable } from './Select';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import BasicList from './components/BasicList';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { throttle } from 'lodash';
import { selectAssociation, getEducationalInstitutionsNameAndId, clearPageState } from 'modules/directory/association';

const TreeTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const pageState = useAppSelector(selectAssociation);
  const { broadsideList } = pageState;
  const location = useLocation();
  const prevLocationRef = useRef(location);

  useEffect(() => {
    dispatch(
      getEducationalInstitutionsNameAndId({
        educationalName: location?.state?.educationalName,
        educationalId: location?.state?.educationalId,
      }),
    );
    return () => {
      dispatch(clearPageState());
    };
  }, []);

  useEffect(() => {
    // 监听路由变化
    if (prevLocationRef.current.pathname === '/directory/association') {
      dispatch(clearPageState());
    }

    // 更新上一个路由
    prevLocationRef.current = location;
  }, [location, dispatch]);

  const throttledDispatch = throttle((educationalName) => {
    dispatch(getEducationalInstitutionsNameAndId({ educationalName }));
  }, 1500);

  const onChange = (value: InputValue) => {
    throttledDispatch(value);
  };

  return (
    <div className={classnames(CommonStyle.pageWithColor, Style.content)}>
      <div className={Style.treeContent}>
        <Input onChange={onChange} className={Style.search} suffixIcon={<SearchIcon />} placeholder='请输入关键词' />
        <BasicList broadsideList={broadsideList}></BasicList>
      </div>
      <div className={Style.tableContent}>
        <SelectTable broadsideList={broadsideList} />
      </div>
    </div>
  );
};

export default TreeTable;
