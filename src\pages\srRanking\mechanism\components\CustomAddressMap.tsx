import React, { memo, useMemo } from 'react';
import AddressMap from 'components/AddressMap';
import { type InputProps } from 'tdesign-react';

interface CustomAddressMapProps {
  value: any;
  onChange: (val: any) => void;
  tips?: string; // 可选的 tips 提示信息
  status?: InputProps['status']; // 继承 tdesign Input 的 status 类型
  id: number;
}

const CustomAddressMap: React.FC<CustomAddressMapProps> = ({ value, onChange, tips, id }) => {
  const initData = useMemo(() => value, [JSON.stringify(value)]);

  // 确保 domId 和 tipinput 只在 id 变化时更新
  const domId = useMemo(() => `domId-${id}`, [id]);
  const tipinput = useMemo(() => `tipinput-${id}`, [id]);

  const handleAddressInfo = (info: any, l: any) => {
    const { formattedAddress, addressComponent } = info || {};
    const { name: addressName, location } = l;

    let longitude = '';
    let latitude = '';

    if (Array.isArray(location)) {
      [longitude, latitude] = location;
    } else {
      longitude = location.lng;
      latitude = location.lat;
    }

    onChange({
      province: addressComponent.province,
      city: addressComponent.city,
      area: addressComponent.district,
      country: addressComponent.township,
      village: '',
      addressDetails: formattedAddress,
      longitude: longitude.toString(),
      latitude: latitude.toString(),
      addressName,
    });
  };

  return (
    <div style={{ width: '100%', height: '200px' }}>
      <AddressMap
        initData={initData}
        domId={domId}
        tipinput={tipinput}
        placeholder='请输入校区地址'
        onAddressInfo={handleAddressInfo}
      />
      {tips && <div className='t-input__tips t-input__tips--error'>{tips}</div>}
    </div>
  );
};

export default memo(CustomAddressMap);
