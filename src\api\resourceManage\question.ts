import request from 'utils/request/index';

export interface IGetSubjectListApi {
  subjectName?: string;
}

/**
 * 查询所有科目
 * @returns
 */
export const getSubjectListApi = async (subjectName: string | null | undefined) => {
  const result = await request.get({
    url: '/exam/getSubjectList',
    params: { subjectName },
  });
  return result;
};

/**
 * 条件分页查询题目列表
 * @returns
 */
export const getQuestionListApi = async (params: any) => {
  const result = await request.get({
    url: '/question/questionList',
    params,
  });
  return result;
};

/**
 * 删除题目
 * @returns
 */
export const deleteQuestionApi = async (params: any) => {
  const result = await request.delete({
    url: '/question/deleteQuestion',
    data: params,
  });
  return result;
};

/**
 * 添加/编辑题目
 * @param data
 * @returns
 */
export const addOrUpdateQuestionApi = async (data: any) => {
  const result = await request.post({
    url: '/question/addOrUpdateQuestion',
    data,
  });
  return result;
};

/**
 * 删除科目
 * @returns
 */
export const deleteSubjectApi = async (id: number) => {
  const result = await request.delete({
    url: `/question/deleteSubject/${id}`,
  });
  return result;
};
export interface IAddOrUpdateSubjectApi {
  subjectId: number;
  subjectName: string;
  freeQuestionCount: number;
  mockExamQuestionCount: number;
  mockExamDuration: number;
  notes: string;
}

/**
 * 添加/编辑科目
 * @param data
 * @returns
 */
export const addOrUpdateSubjectApi = async (data: IAddOrUpdateSubjectApi) => {
  const result = await request.post({
    url: `/question/addOrUpdateSubject`,
    data,
  });
  return result;
};

export interface IQuestionDragSortApi {
  strId: string;
  position: number;
  parentId: number;
}

/**
 * 题库列表拖动排序
 * @returns
 */
export const questionDragSortApi = async (params: IQuestionDragSortApi) => {
  const result = await request.put({
    url: `/question/questionDragSort`,
    params,
  });
  return result;
};
