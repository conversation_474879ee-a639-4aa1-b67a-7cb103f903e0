import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { login as loginApi, getUserInfo as getUserInfoApi, getRouters as getRouters<PERSON><PERSON>, getProfile<PERSON><PERSON> } from 'api';
import { IRouter } from 'router';
import { formatRoute } from 'utils/routers';

const namespace = 'user';
const TOKEN_NAME = 'token';
const USER_INFO = 'userInfo';
const PERMISSIONS = 'permissions';
const ROLES = 'roles';
const USER_ROUTER = 'user_router';

// 定义用户状态类型
interface UserState {
  token: string | null;
  userInfo: Record<string, number>;
  permissions: string[];
  roles: string[];
  userDetailsInfo: Record<string, number>;
  routers: IRouter[];
}

const initialState: UserState = {
  token: localStorage.getItem(TOKEN_NAME),
  userInfo: {},
  permissions: [],
  roles: [],
  userDetailsInfo: {},
  routers: [],
};

/**
 * 登录
 */
export const login = createAsyncThunk(
  `${namespace}/login`,
  async (userInfo: Record<string, unknown>, { rejectWithValue }) => {
    try {
      const res = await loginApi(userInfo);
      return res;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }

      return rejectWithValue('未知错误');
    }
  },
);

/**
 * 获取用户信息
 */
export const getUserInfo = createAsyncThunk(`${namespace}/getUserInfo`, async () => {
  const res = await getUserInfoApi();

  return {
    user: res.user,
    permissions: res.permissions,
    roles: res.roles,
  };
});

/**
 * 获取权限
 */
export const getRouters = createAsyncThunk(`${namespace}/getRouters`, async () => {
  const { data } = await getRoutersApi();
  const ROUTER = JSON.parse(JSON.stringify(data));
  const FORMAT_ROUTER = formatRoute(ROUTER);
  return FORMAT_ROUTER;
});

/**
 * 获取用户详细信息
 */
export const getUserDetailsInfo = createAsyncThunk(`${namespace}/getUserDetailsInfo`, async () => {
  const user = await getProfileApi();
  return user;
});

const userSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    logout: (state) => {
      localStorage.removeItem(TOKEN_NAME);
      localStorage.removeItem(USER_INFO);
      localStorage.removeItem(PERMISSIONS);
      localStorage.removeItem(ROLES);
      localStorage.removeItem(USER_ROUTER);
      state.token = '';
      state.userInfo = {};
      state.permissions = [];
      state.roles = [];
    },
    remove: (state) => {
      state.token = '';
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(login.fulfilled, (state, action) => {
        localStorage.setItem(TOKEN_NAME, action.payload.token);
        state.token = action.payload.token;
      })
      .addCase(getUserInfo.fulfilled, (state, action) => {
        state.userInfo = action.payload.user;

        if (action.payload.roles && action.payload.roles.length > 0) {
          state.permissions = action.payload.permissions;
          state.roles = action.payload.roles;
        } else {
          state.roles = ['ROLE_DEFAULT'];
        }

        localStorage.setItem(USER_INFO, JSON.stringify(action.payload.user));
        localStorage.setItem(PERMISSIONS, JSON.stringify(action.payload.permissions));
        localStorage.setItem(ROLES, JSON.stringify(action.payload.roles));
      })
      .addCase(getRouters.fulfilled, (state, action) => {
        localStorage.setItem(USER_ROUTER, JSON.stringify(action.payload));
        state.routers = action.payload;
      })
      .addCase(getUserDetailsInfo.fulfilled, (state, action) => {
        state.userDetailsInfo = action.payload;
      });
  },
});

export const selectListBase = (state: RootState) => state.listBase;
export const selectToken = (state: RootState) => state.user.token;
export const selectUserInfo = (state: RootState) => state.user.userInfo;
export const selectPermissions = (state: RootState) => state.user.permissions;
export const selectRoles = (state: RootState) => state.user.roles;
export const selectUserDetailsInfo = (state: RootState) => state.user.userDetailsInfo;
export const selectRouters = (state: RootState) => state.user.routers;

export const { logout, remove } = userSlice.actions;

export default userSlice.reducer;
