.checkContainer {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: var(--td-text-color-secondary);

  &.rememberPwd {
    margin-bottom: 16px;
    justify-content: space-between;
  }

  :global {
    .t-checkbox__label {
      color: var(--td-text-color-secondary);
    }

    span {
      color: var(--td-brand-color);

      &:hover {
        cursor: pointer;
      }
    }
  }
}

.verificationBtn {
  flex-shrink: 0;
  width: 102px;
  height: 40px;
  margin-left: 11px;
}

.btnContainer {
  margin-top: 48px;
}

.spaces {
  gap: 10px 65px !important;
}
