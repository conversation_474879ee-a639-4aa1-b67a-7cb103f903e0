import request from 'utils/request/index';

interface Params {
  pageNum: number;
  pageSize: number;
}

/**
 * 查询部门下拉树结构
 * @returns
 */
export const getDeptTreeApi = async () => {
  const result = await request.get({
    url: '/system/user/deptTree',
  });
  return result;
};

export interface IGetSystemUserListApi extends Params {
  deptId: number | undefined;
  userName?: string;
  phonenumber?: number;
  /** 状态： 0: 正常 1: 停用 */
  status?: 0 | 1;
}

/**
 * 查询用户列表
 * @returns
 */
export const getSystemUserListApi = async (params: IGetSystemUserListApi) => {
  const result = await request.get({
    url: '/system/user/list',
    params,
  });
  return result;
};

/**
 * 删除用户
 * @param userId
 * @returns
 */
export const delSystemUserApi = async (userId: number | string) => {
  const result = await request.delete({
    url: `/system/user/${userId}`,
  });
  return result;
};

/**
 * 查询角色和岗位
 * @returns
 */
export const getRolePostApi = async (id?: number) => {
  const result = await request.get({
    url: `/system/user/${id}`,
  });
  return result;
};

export interface IAddUserApi {
  /** 用户昵称 */
  nickName: string;

  /** 用户账号 */
  userName: string;

  /** 密码 */
  password: string | number;

  /** 手机号 */
  phonenumber: string;

  /** 部门 ID */
  deptId: number | undefined;
  /** 岗位 ID */
  postIds: number[];

  /** 角色 ID */
  roleIds: number[];

  /** 状态 0--开启 1--关闭 */
  status: '0' | '1';

  /** 性别 0--男 1--女 */
  sex: '0' | '1' | undefined;

  /** 邮箱地址 */
  email: string | undefined;

  /** 备注 */
  remark: string | undefined;

  /** 用户头像 */
  avatar: string | undefined;
}

export const addUserApi = async (params: IAddUserApi) => {
  const result = await request.post({
    url: `/system/user`,
    params,
  });
  return result;
};

export interface IPutUserApi extends IAddUserApi {
  useId: number;
}
export const putUserApi = async (params: IPutUserApi) => {
  const result = await request.put({
    url: `/system/user`,
    params,
  });
  return result;
};

export interface IResetPwdApi {
  password: string;
  userId: number;
}

/**
 * 重置密码
 * @param params
 * @returns
 */
export const resetPwdApi = async (params: IResetPwdApi) => {
  const result = await request.put({
    url: `/system/user/resetPwd`,
    params,
  });
  return result;
};

/**
 * 获取用户详细信息
 * @param params
 * @returns
 */
export const getUserDetailsInfoApi = async (params: IResetPwdApi) => {
  const result = await request.put({
    url: `/system/user/resetPwd`,
    params,
  });
  return result;
};
