import { IGetOperLogListBo, IGetLoginLogListBo } from 'types/system';
import request from 'utils/request/index';

/**
 * 查询操作日志列表
 * @returns
 */
export const getSystemOperLogListApi = async (params: IGetOperLogListBo) => {
  const result = await request.get({
    url: '/monitor/operlog/list',
    params,
  });
  return result;
};

/**
 * 删除操作日志
 * @param operLogId
 * @returns
 */
export const delSystemOperLogApi = async (operLogId: number | string) => {
  const result = await request.delete({
    url: `/monitor/operlog/${operLogId}`,
  });
  return result;
};

/**
 * 清空操作日志
 * @returns
 */
export const clearSystemOperLogApi = async () => {
  const result = await request.delete({
    url: `/monitor/operlog/clean`,
  });
  return result;
};

/**
 * 查询登录日志列表
 * @returns
 */
export const getSystemLoginLogListApi = async (params: IGetLoginLogListBo) => {
  const result = await request.get({
    url: '/monitor/logininfor/list',
    params,
  });
  return result;
};

/**
 * 删除操作日志
 * @param loginLogId
 * @returns
 */
export const delSystemLoginLogApi = async (loginLogId: number | string) => {
  const result = await request.delete({
    url: `/monitor/logininfor/${loginLogId}`,
  });
  return result;
};

/**
 * 清空操作日志
 * @returns
 */
export const clearSystemLoginLogApi = async () => {
  const result = await request.delete({
    url: `/monitor/logininfor/clean`,
  });
  return result;
};

/**
 * 解锁用户
 * @returns
 */
export const unlockLogininforApi = async (userName: string) => {
  const result = await request.get({
    url: `/monitor/logininfor/unlock/${userName}`,
  });
  return result;
};
