import React, { memo, useState, useEffect, useRef } from 'react';
import { Tag, Button, TableProps } from 'tdesign-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectExamManage, getList, examDragSort, clearPageState } from 'modules/examManage';
import { Tables } from 'components';
import { MoveIcon } from 'tdesign-icons-react';
import { strIsNull } from 'utils/tool';

type themeType = 'warning' | 'default' | 'primary' | 'danger' | 'success' | undefined;

const Tags = (theme: themeType, name: string) => (
  <Tag theme={theme} variant='light'>
    {name}
  </Tag>
);

export const StatusMap: {
  [key: number]: React.ReactElement;
} = {
  0: Tags('warning', '未编辑'),
  1: Tags('success', '已编辑'),
  2: Tags('danger', '未知'),
};

export default memo(() => {
  const dispatch = useAppDispatch();
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);

  const pageState = useAppSelector(selectExamManage);
  const { loading, contractList, pageNum, pageSize, total } = pageState;
  const navigate = useNavigate();

  // const handleResetSelection = () => {
  //   if (tableRef.current) {
  //     // 调用重置选中方法
  //     tableRef.current.resetSelection();
  //   }
  // };

  useEffect(() => {
    dispatch(
      getList({
        pageSize: pageState.pageSize,
        pageNum: pageState.pageNum,
      }),
    );
    return () => {
      dispatch(clearPageState());
    };
  }, []);

  const onDragSort = async (val: any) => {
    try {
      await dispatch(
        examDragSort({
          position: pageNum === 1 ? val.targetIndex + 1 : (pageNum - 1) * pageSize + (val.targetIndex + 1),
          id: val.current.examId,
        }),
      );
      return true;
    } catch (error) {
      throw new Error('排序失败');
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'drag',
      title: '排序',
      cell: () => <MoveIcon />,
      width: 46,
    },
    {
      colKey: 'examName',
      title: '考试名称',
    },
    {
      colKey: 'editingStatus',
      title: '编辑状态',
      cell({ row }) {
        return StatusMap[row.editingStatus] ?? StatusMap[2];
      },
    },
    {
      colKey: 'subjectVoList',
      title: '关联科目',
      cell({ row: { subjectVoList } }) {
        const list = subjectVoList && subjectVoList.map((i: any) => i.subjectName).filter((i: any) => !strIsNull(i));
        return list ? list.join(',') : '暂无数据';
      },
    },
    {
      fixed: 'right',
      width: 180,
      colKey: 'op',
      title: '操作',
      cell({ row }) {
        return (
          <>
            <Button theme='primary' variant='text' onClick={() => navigate(`/examManage/edit`, { state: row })}>
              编辑
            </Button>
            <Button
              theme='primary'
              variant='text'
              onClick={() => navigate('/examManage/relatedSubjects', { state: row.examId })}
            >
              科目
            </Button>
          </>
        );
      },
    },
  ];
  return (
    <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
      <Tables
        ref={tableRef}
        tabletData={{
          columns,
          list: contractList,
          loading,
          rowKey: 'examId',
          dragSort: 'row-handler',
          onDragSort,
          selectedRowKeys,
          onSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getList}
        params={{}}
      />
    </div>
  );
});
