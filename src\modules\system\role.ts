import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getRoleListApi,
  getRoleInfoApi,
  getDeptTreeSelectApi,
  roleChangeStatusApi,
  getTreeselect<PERSON>pi,
  getRoleMenuTreeselectApi,
  addRole<PERSON><PERSON>,
  putRole<PERSON><PERSON>,
  delRole<PERSON><PERSON>,
  editDataScopeApi,
} from 'api';
import type { IRoleChangeStatusApi, IAddRoleApi } from 'api';
import { MessagePlugin } from 'tdesign-react';
import { IEditDataScopeBo } from 'types/system';

const namespace = 'system/role';

interface IInitialState {
  loading: boolean;
  pageNum: number;
  pageSize: number;
  total: number;
  contractList: Array<any>;
  switchLoading: boolean;
  treeselectList: Array<any>;
  formLoading: boolean;
  delLoading?: boolean;
  roleMenuTreeselectList: Array<number>;
  roleInfo: any;
  roleInfoLoading: boolean;
  deptTree: any;
  editDataScopeLoading: boolean;
}

const initialState: IInitialState = {
  loading: true,
  switchLoading: false,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  contractList: [],
  treeselectList: [],
  formLoading: false,
  delLoading: false,
  roleMenuTreeselectList: [],
  roleInfo: {},
  roleInfoLoading: false,
  deptTree: {},
  editDataScopeLoading: false,
};

export const getRoleList = createAsyncThunk(
  `${namespace}/getRoleList`,
  async (params: { pageSize: number; pageNum: number }) => {
    const { rows, total } = await getRoleListApi(params);
    return {
      list: rows,
      total,
      pageSize: params.pageSize,
      pageNum: params.pageNum,
    };
  },
);

export const getRoleInfo = createAsyncThunk(`${namespace}/getRoleInfo`, async (roleId: number) => {
  const { data } = await getRoleInfoApi(roleId);
  const { checkedKeys, depts } = await getDeptTreeSelectApi(roleId);

  data.deptIds = checkedKeys;
  data.depts = depts;

  return data;
});

export const editDataScope = createAsyncThunk(`${namespace}/editDataScope`, async (bo: IEditDataScopeBo) => {
  await editDataScopeApi(bo);
});

export const getTreeselect = createAsyncThunk(`${namespace}/getTreeselect`, async () => {
  const { data } = await getTreeselectApi();
  return {
    treeselectList: data,
  };
});

export const getRoleMenuTreeselect = createAsyncThunk(`${namespace}/getRoleMenuTreeselect`, async (roleId: number) => {
  const { checkedKeys } = await getRoleMenuTreeselectApi(roleId);
  return {
    roleMenuTreeselectList: checkedKeys,
  };
});

export const roleChangeStatus = createAsyncThunk(
  `${namespace}/roleChangeStatus`,
  async (params: IRoleChangeStatusApi) => {
    await roleChangeStatusApi(params);
  },
);

export const addRole = createAsyncThunk(`${namespace}/addRole`, async (params: IAddRoleApi) => {
  await addRoleApi(params);
});

export const putRole = createAsyncThunk(`${namespace}/putRole`, async (params: IAddRoleApi) => {
  await putRoleApi(params);
});

export const delSystemRole = createAsyncThunk(`${namespace}/delSystemRole`, async (params: any, { dispatch }) => {
  const { roleId, ...rest } = params;
  await delRoleApi(roleId);
  await dispatch(getRoleList({ ...rest }));
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getRoleList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getRoleList.fulfilled, (state, action) => {
        state.loading = false;
        state.contractList = action.payload?.list;
        state.total = action.payload?.total;
        state.pageSize = action.payload?.pageSize;
        state.pageNum = action.payload?.pageNum;
      })
      .addCase(getRoleList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getRoleInfo.pending, (state) => {
        state.roleInfoLoading = true;
      })
      .addCase(getRoleInfo.fulfilled, (state, action) => {
        state.roleInfoLoading = false;
        state.roleInfo = action.payload;
      })
      .addCase(getRoleInfo.rejected, (state) => {
        state.roleInfoLoading = false;
      })

      .addCase(getTreeselect.fulfilled, (state, action) => {
        state.treeselectList = action.payload?.treeselectList;
      })

      .addCase(delSystemRole.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(delSystemRole.fulfilled, (state) => {
        state.delLoading = false;
        MessagePlugin.success('删除成功！');
      })
      .addCase(delSystemRole.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(getRoleMenuTreeselect.fulfilled, (state, action) => {
        state.roleMenuTreeselectList = action.payload?.roleMenuTreeselectList;
      })

      .addCase(editDataScope.pending, (state) => {
        state.editDataScopeLoading = true;
      })
      .addCase(editDataScope.fulfilled, (state) => {
        state.editDataScopeLoading = false;
        MessagePlugin.success('保存成功！');
      })
      .addCase(editDataScope.rejected, (state) => {
        state.editDataScopeLoading = false;
      });
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectSystemRole = (state: RootState) => state.systemRole;

export default listBaseSlice.reducer;
