import {
  IAddDictBo,
  IEditDictBo,
  IGetDictDataListBo,
  IGetDictListBo,
  IAddDictDataBo,
  IEditDictDataBo,
} from 'types/system';
import request from 'utils/request/index';

/**
 * 查询字典列表
 * @returns
 */
export const getSystemDictListApi = async (params: IGetDictListBo) => {
  const result = await request.get({
    url: '/system/dict/type/list',
    params,
  });
  return result;
};

/**
 * 删除字典
 * @param dictId
 * @returns
 */
export const delSystemDictApi = async (dictId: number | string) => {
  const result = await request.delete({
    url: `/system/dict/type/${dictId}`,
  });
  return result;
};

export const addSystemDictApi = async (params: IAddDictBo) => {
  const result = await request.post({
    url: `/system/dict/type`,
    params,
  });
  return result;
};

export const editSystemDictApi = async (params: IEditDictBo) => {
  const result = await request.put({
    url: `/system/dict/type`,
    params,
  });
  return result;
};

/**
 * 刷新字典缓存
 */
export const refreshSystemDictCacheApi = async () => {
  const result = await request.delete({
    url: '/system/dict/type/refreshCache',
  });
  return result;
};

/**
 * 获取字典详细信息
 * @param dictId
 * @returns
 */
export const getSystemDictDetailsInfoApi = async (dictId: number) => {
  const result = await request.get({
    url: `/system/dict/type/${dictId}`,
  });
  return result;
};

/**
 * 查询字典数据列表
 * @returns
 */
export const getSystemDictDataListApi = async (params: IGetDictDataListBo) => {
  const result = await request.get({
    url: '/system/dict/data/list',
    params,
  });
  return result;
};

/**
 * 删除字典数据
 * @param dictCode
 * @returns
 */
export const delSystemDictDataApi = async (dictCode: number | string) => {
  const result = await request.delete({
    url: `/system/dict/data/${dictCode}`,
  });
  return result;
};

export const addSystemDictDataApi = async (params: IAddDictDataBo) => {
  const result = await request.post({
    url: `/system/dict/data`,
    params,
  });
  return result;
};

export const editSystemDictDataApi = async (params: IEditDictDataBo) => {
  const result = await request.put({
    url: `/system/dict/data`,
    params,
  });
  return result;
};

/**
 * 获取字典数据详细信息
 * @param dictCode
 * @returns
 */
export const getSystemDictDataDetailsInfoApi = async (dictCode: number) => {
  const result = await request.get({
    url: `/system/dict/data/${dictCode}`,
  });
  return result;
};

/**
 * 获取所有字典列表
 * @returns
 */
export const getSystemDictAllListApi = async () => {
  const result = await request.get({
    url: '/system/dict/type/optionselect',
  });
  return result;
};
