import React, { useState, useEffect, ReactElement, forwardRef, useImperativeHandle } from 'react';
import { Table, TableProps } from 'tdesign-react';
import { usePagingV2 } from 'hooks/usePagingV2';
import { TableRowData, DragSortContext, TableColumnController } from 'tdesign-react/es/table/type';
import { strIsNull } from 'utils/tool';

export interface PagingProps {
  /** 当前页数 */
  pageNum: number;

  /** 每页大小数 */
  pageSize: number;

  /** 数据总数 */
  total: number;
}

export interface TableDataProps {
  /** 表头 */
  columns: TableProps['columns'];

  /** 表格数据 */
  list: TableRowData[];

  /** 请求等待时的加载遮罩 */
  loading: boolean;

  /** 唯一标识一行数据的字段名，来源于 data 中的字段。如果是字段嵌套多层，可以设置形如 item.a.id 的方法 */
  rowKey: string;

  /** 选中行，控制属性。半选状态行请更为使用 indeterminateSelectedRowKeys 控制 */
  selectedRowKeys?: (string | number)[];

  /** 点击多选框或单选框的回调函数 */
  onSelectChange?: (value: (string | number)[]) => void;

  /** 开启拖拽 */
  dragSort?: 'row' | 'row-handler' | 'col' | 'row-handler-col' | 'drag-col' | undefined;

  /** 拖拽回调 */
  onDragSort?: (value: DragSortContext<TableRowData> | Promise<boolean>) => boolean | Promise<boolean>;
}

interface ITableData extends TableDataProps {
  displayColumns?: Array<string>;
  onDisplayColumnsChange?: (displayColumns: Array<string>) => void;
  optionalColumns?: Array<string>;
  columnController?: TableColumnController;
  columnControllerVisible?: boolean;
  onColumnControllerVisibleChange?: (visible: boolean) => void;
}

export interface IPackageTable {
  /** 表头数据 */
  tabletData: ITableData;

  /** 分页参数 */
  paging?: PagingProps;

  /** 请求数据方法的其他参数 */
  params?: Record<string, any>;

  /** 请求数据的方法 */
  method: (params: any) => unknown;

  /** 表单参数Ref */
  formRef?: any;
}

const PackageTable = forwardRef(({ tabletData, paging, params, method, formRef }: IPackageTable, ref): ReactElement => {
  if (!strIsNull(formRef) && formRef?.current) {
    const formParams = formRef.current.getFormParams();

    const fParams = Object.fromEntries(
      Object.entries(formParams).filter(([_, value]) => value !== undefined),
    ) as Object;

    Object.assign(params, fParams);
  }

  let pagination;

  if (paging) {
    pagination = usePagingV2(method, params, paging || { pageNum: 1, pageSize: 10, total: 0 });
  }

  const handleColumns: TableProps['columns'] =
    tabletData.columns &&
    tabletData.columns.map((col: TableProps['columns'], index: number) => {
      let newCol = {
        ...col,
        ellipsis: true,
        align: 'left',
      } as TableProps['columns'];

      if (col?.colKey === 'row-select') {
        newCol = {
          ...newCol,
          title: '多选框',
        };
      } else if (col?.colKey === 'op') {
        newCol = {
          ...newCol,
          fixed: 'right',
        };
      }

      return newCol;
    });

  tabletData.columnController = {
    dialogProps: { preventScrollThrough: true },
    hideTriggerButton: true,
    fields: tabletData.optionalColumns,
  };

  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(tabletData.selectedRowKeys ?? []);
  const [data, setData] = useState<TableRowData[]>([...tabletData.list]);

  // useEffect(() => {
  //   if (tabletData.onSelectChange) {
  //     tabletData.onSelectChange(selectedRowKeys);
  //   }
  // }, [selectedRowKeys, tabletData]);

  useEffect(() => {
    setData([...tabletData.list]);
  }, [tabletData.list]);

  const onDragSortChange: TableProps['onDragSort'] = (params) => {
    try {
      if (tabletData.onDragSort) {
        const result = tabletData.onDragSort(params);
        setData(params.newData);
        if (!result) {
          setData([...tabletData.list]);
        }
      }
    } catch (error) {
      setData([...tabletData.list]);
    }
  };

  const onSelectionchange: TableDataProps['onSelectChange'] = (val, options) => {
    setSelectedRowKeys(val);
    if (tabletData.onSelectChange) {
      tabletData.onSelectChange(val, options);
    }
  };

  // 重置选中的方法
  const resetSelection = () => {
    setSelectedRowKeys([]); // 重置选中状态
    if (tabletData.onSelectChange) {
      tabletData.onSelectChange([], { selectedRowData: [] });
    }
  };

  // 暴露给外部的方法
  useImperativeHandle(ref, () => ({
    resetSelection,
  }));

  return (
    <Table
      loading={tabletData.loading}
      data={tabletData.dragSort ? data : tabletData.list}
      columns={handleColumns}
      rowKey={tabletData.rowKey}
      selectedRowKeys={selectedRowKeys}
      hover
      dragSort={tabletData.dragSort ?? undefined}
      onDragSort={onDragSortChange}
      onSelectChange={onSelectionchange} // 处理行选中的变化
      pagination={paging ? pagination : undefined}
      headerAffixedTop={{ offsetTop: 0 }}
      lazyLoad
      displayColumns={tabletData.displayColumns}
      onDisplayColumnsChange={tabletData.onDisplayColumnsChange}
      columnController={tabletData.columnController}
      columnControllerVisible={tabletData.columnControllerVisible}
      onColumnControllerVisibleChange={tabletData.onColumnControllerVisibleChange}
    />
  );
});

export default PackageTable;
