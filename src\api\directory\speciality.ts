import request from 'utils/request/index';
/**
 * 专业目录
 */

export interface IGetMajorsCategoryListApi {
  majorsCategoryName?: string;
}

/**
 * 查询专业类目树列表
 * @returns
 */
export const getMajorsCategoryListApi = async (params?: IGetMajorsCategoryListApi) => {
  const result = await request.get({
    url: '/majors/getMajorsCategoryList',
    params,
  });
  return result;
};

/**
 * 新增/编辑类目
 * @param params
 * @returns
 */
export const addOrUpdateMajorsCategoryApi = async (params: IGetMajorsCategoryListApi) => {
  const result = await request.post({
    url: '/majors/addOrUpdateMajorsCategory',
    params,
  });
  return result;
};

/**
 * 删除类目
 * @param id
 * @returns
 */
export const deleteMajorsCategoryApi = async (id: number) => {
  const result = await request.delete({
    url: `/majors/deleteMajorsCategory/${id}`,
  });
  return result;
};

export interface IGetMajorsListApi {
  pageNum: number;
  pageSize: number;
  categoryId: number;
  majorsName?: string;
}

/**
 * 条件分页查询专业列表
 * @param id
 * @returns
 */
export const getMajorsListApi = async (params: IGetMajorsListApi) => {
  const result = await request.get({
    url: `/majors/getMajorsList`,
    params,
  });
  return result;
};

export interface IEmploymentDirectionBoList {
  employmentDirectionId: undefined;
  jobName: string;
  jobDesc: string;
  minAvgMoney: string;
  maxAvgMoney: string;
}
export interface IAddOrUpdateMajorsApi {
  categoryId: number;
  majorsId: number;
  majorsName: string;
  majorsDesc: string;
  majorsIntroduce: string;
  majorsBackgroundImgId: string;
  employmentDirectionBoList: IEmploymentDirectionBoList[];
  majorUndergraduate: string;
}

export const addOrUpdateMajorsApi = async (params: IAddOrUpdateMajorsApi) => {
  const result = await request.post({
    url: `/majors/addOrUpdateMajors`,
    params,
  });
  return result;
};

/**
 * 查询专业关联考试列表
 * @param id
 * @returns
 */
export const getMajorsExamListApi = async (id: number) => {
  const result = await request.get({
    url: `/majors/getMajorsExamList/${id}`,
  });
  return result;
};

export interface IAddOrUpdateMajorsExamApi {
  examId: number;
  majorsId: number;
  isRelated: number;
}

/**
 * 专业关联/取消关联考试信息
 * @param params
 * @returns
 */
export const addOrUpdateMajorsExamApi = async (params: IAddOrUpdateMajorsExamApi) => {
  const result = await request.post({
    url: `/majors/addOrUpdateMajorsExam`,
    params,
  });
  return result;
};

/**
 * 删除/批量删除专业
 * @param params
 * @returns
 */
export const deleteMajorsApi = async (params: IAddOrUpdateMajorsExamApi) => {
  const result = await request.delete({
    url: `/majors/deleteMajors`,
    params,
  });
  return result;
};

/**
 * 删除/批量删除专业
 * @param id
 * @returns
 */
export const getMajorsInfoApi = async (id: number) =>
  request.get({
    url: `/majors/getDetailById/${id}`,
  });
