import React from 'react';
import { ImageViewer, Image, Space } from 'tdesign-react';
import { BrowseIcon } from 'tdesign-icons-react';
import type { ImageViewerProps } from 'tdesign-react';

interface BasicImageViewerProps extends ImageViewerProps {
  /** 预览图片类型：item--单图预览 array--多图预览 */
  type: 'item' | 'array';
  /** 图片地址 */
  images: string[] | undefined;
}

let imageStyle = {
  width: 65,
  height: 65,
  border: '4px solid var(--td-bg-color-secondarycontainer)',
  borderRadius: 'var(--td-radius-medium)',
  backgroundColor: '#fff',
};

const BasicImageViewer = ({ type = 'item', images, style }: BasicImageViewerProps) => {
  const trigger = (open: () => void, imgSrc: string | undefined) => {
    const mask = (
      <div
        style={{
          background: 'rgba(0,0,0,.6)',
          color: '#fff',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onClick={open}
      >
        <span>
          <BrowseIcon size='16px' name={'browse'} />
        </span>
      </div>
    );

    if (style) {
      if (style.width) {
        imageStyle = {
          ...imageStyle,
          width: style.width,
        };
      }

      if (style.height) {
        imageStyle = {
          ...imageStyle,
          height: style.height,
        };
      }
    }

    return <Image src={imgSrc} overlayContent={mask} overlayTrigger='hover' fit='contain' style={imageStyle} />;
  };
  return (
    <Space breakLine size={16}>
      {type === 'item' ? (
        <ImageViewer trigger={({ open }) => trigger(open, images?.[0])} images={images} />
      ) : (
        images &&
        images.map((imgSrc, index) => (
          <ImageViewer key={index} trigger={({ open }) => trigger(open, imgSrc)} images={images} defaultIndex={index} />
        ))
      )}
    </Space>
  );
};

export default BasicImageViewer;
