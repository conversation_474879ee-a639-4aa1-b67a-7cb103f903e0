import React from 'react';
import { Input, type InputValue } from 'tdesign-react';
import { SearchIcon } from 'tdesign-icons-react';
import classnames from 'classnames';
import { SelectTable } from './Select';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import BasicList from './components/SideList';
import { useAppDispatch } from 'modules/store';
import { throttle } from 'lodash';
import { getMajorsCategoryList } from 'modules/directory/speciality';

const TreeTable: React.FC = () => {
  const dispatch = useAppDispatch();

  const throttledDispatch = throttle((value) => {
    dispatch(getMajorsCategoryList({ majorsCategoryName: value }));
  }, 0);

  const onChange = (value: InputValue) => {
    throttledDispatch(value);
  };

  return (
    <div className={classnames(CommonStyle.pageWithColor, Style.content)}>
      <div className={Style.treeContent}>
        <Input onChange={onChange} className={Style.search} suffixIcon={<SearchIcon />} placeholder='请输入关键词' />
        <BasicList></BasicList>
      </div>
      <div className={Style.tableContent}>
        <SelectTable />
      </div>
    </div>
  );
};

export default TreeTable;
