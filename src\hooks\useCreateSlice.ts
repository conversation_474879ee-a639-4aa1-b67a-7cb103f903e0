import {
  createSlice,
  SliceCaseReducers,
  CreateSliceOptions,
  createAsyncThunk,
  AsyncThunk,
  ActionReducerMapBuilder,
  PayloadAction,
  Draft,
} from '@reduxjs/toolkit';

import { RootState } from 'modules/store';

interface CreateSliceWithCasesOptions<State, CaseReducers extends SliceCaseReducers<State>>
  extends CreateSliceOptions<State, CaseReducers> {
  cases?: Array<{
    thunk: AsyncThunk<any, any, {}>;
    pending: (state: Draft<State>) => void;
    fulfilled: (state: Draft<State>, action: PayloadAction<any>) => void;
    rejected: (state: Draft<State>) => void;
  }>;
}

type AsyncThunkConfig = {
  state: RootState;
};

type ThunkAPI = {
  getState: () => RootState;
  dispatch: any;
  extra: unknown;
  requestId: string;
  signal: AbortSignal;
  rejectWithValue: any;
};

type PendingCallback<S> = (state: Draft<S>) => void;
type FulfilledCallback<S, P> = (state: Draft<S>, action: PayloadAction<P>) => void;
type RejectedCallback<S> = (state: Draft<S>) => void;

/**
 * 二次封装 createAsyncThunk
 * @param {string} typePrefix 模块名称
 * @param {Function} payloadCreator 请求回调
 * @returns {Function} AsyncThunk
 */
export const useAsyncThunkWithStatus = <Returned, ThunkArg>(
  typePrefix: string,
  payloadCreator: (arg: ThunkArg, thunkAPI: ThunkAPI) => Promise<Returned>,
): AsyncThunk<Returned, ThunkArg, AsyncThunkConfig> =>
  createAsyncThunk<Returned, ThunkArg, AsyncThunkConfig>(typePrefix, async (arg, thunkAPI) => {
    try {
      return await payloadCreator(arg, thunkAPI);
    } catch (error) {
      return thunkAPI.rejectWithValue(error);
    }
  });

/**
 * 辅助函数，用于添加异步 thunk 的 pending、fulfilled、rejected case
 * @param builder
 * @param thunk
 * @param pendingCallback
 * @param fulfilledCallback
 * @param rejectedCallback
 */
export const addCaseHelper = <S, P>(
  builder: ActionReducerMapBuilder<S>,
  thunk: AsyncThunk<P, any, {}>,
  pendingCallback: PendingCallback<S>,
  fulfilledCallback: FulfilledCallback<S, P>,
  rejectedCallback: RejectedCallback<S>,
) => {
  builder
    .addCase(thunk.pending, (state) => pendingCallback(state as Draft<S>))
    .addCase(thunk.fulfilled, (state, action) => fulfilledCallback(state as Draft<S>, action as PayloadAction<P>))
    .addCase(thunk.rejected, (state) => rejectedCallback(state as Draft<S>));
};

/**
 * 创建 slice，并添加异步 thunk 的 pending、fulfilled、rejected case
 * @param options
 * @returns
 */
export const useCreateSlice = <State, CaseReducers extends SliceCaseReducers<State>>(
  options: CreateSliceWithCasesOptions<State, CaseReducers>,
) => {
  const { cases, extraReducers, ...sliceOptions } = options;
  const slice = createSlice({
    ...sliceOptions,
    extraReducers: (builder) => {
      cases?.forEach(({ thunk, pending, fulfilled, rejected }) => {
        addCaseHelper(builder, thunk, pending, fulfilled, rejected);
      });
      if (extraReducers) {
        if (typeof extraReducers === 'function') {
          extraReducers(builder);
        } else {
          Object.entries(extraReducers).forEach(([key, reducer]) => {
            builder.addCase(key as any, reducer as any);
          });
        }
      }
    },
  });

  return slice;
};

/**
 * 合并 useAsyncThunkWithStatus 和 useCreateSlice
 */
// export default <Returned, ThunkArg, State>(namespace: string, initialState: State) => {
export default <Returned, ThunkArg>(namespace: string) => {
  const moudels = {
    useAsyncThunkWithStatus: (
      method: string,
      payloadCreator: (arg: ThunkArg, thunkAPI: ThunkAPI) => Promise<Returned>,
    ) => useAsyncThunkWithStatus<Returned, ThunkArg>(`${namespace}/${method}`, payloadCreator),

    // useCreateSlice: (createOptions: CreateSliceWithCasesOptions<State, SliceCaseReducers<State>>) => {
    //   const options = {
    //     name: namespace,
    //     initialState,
    //     ...createOptions,
    //   };
    //   return useCreateSlice(options);
    // },
  };
  return { ...moudels };
};
