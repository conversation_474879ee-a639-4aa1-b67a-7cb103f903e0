import React, { memo, useEffect } from 'react';
import { Loading, Menu, Empty } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectSRRankingEducational, getTreeList, setActive } from 'modules/srRanking/educational';
import Style from './index.module.less';
import './index.less';

const { MenuItem } = Menu;

const Header = () => <h3 className={Style.header}>所属考试</h3>;

const SRRankingEducationalTree: React.FC = () => {
  const dispatch = useAppDispatch();
  const { treeLoading, treeList, active } = useAppSelector(selectSRRankingEducational);

  useEffect(() => {
    dispatch(getTreeList());
  }, []);

  return (
    <div className={Style.container}>
      <Loading size='small' loading={treeLoading}>
        <Menu
          className='srRankingEducationalTree'
          value={active}
          onChange={(v) => dispatch(setActive(v))}
          logo={<Header />}
        >
          {treeList &&
            treeList.map(({ examId, examName }) => (
              <MenuItem value={examId} key={examId}>
                <span>{examName}</span>
              </MenuItem>
            ))}
          {treeList.length === 0 && <Empty />}
        </Menu>
      </Loading>
    </div>
  );
};

export default memo(SRRankingEducationalTree);
