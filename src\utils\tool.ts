const { VITE_API_URL_PREFIX, VITE_API_URL } = import.meta.env;

/**
 * 获取当前日期
 * @param needTime 是否需要时间
 * @returns
 */
export function getCurrentDate(needTime = false) {
  const d = new Date();
  const month = d.getMonth() + 1;
  const monthText = month < 10 ? `0${month}` : month;
  const date = `${d.getFullYear()}-${monthText}-${d.getDate()}`;
  const time = `${d.getHours()}:${d.getMinutes()}:${d.getSeconds()}`;
  if (needTime) return [date, time].join(' ');
  return date;
}

/**
 * 获取文件后缀名
 * @param file File对象
 * @returns 文件后缀名，如果无法获取后缀名则返回空字符串
 */
export function getFileExtension(file: File): string {
  const fileName = file.name;
  if (!fileName || fileName.indexOf('.') === -1) {
    return '';
  }
  const extension = fileName.substring(fileName.lastIndexOf('.') + 1);
  return extension ? extension.toLowerCase() : '';
}

/**
 * 判断文件后缀是否在允许的后缀数组中
 * @param file File对象
 * @param allowedExtensions 允许的后缀数组（不带点）
 * @returns 如果文件后缀名在允许的数组中则返回 true，否则返回 false
 */
export function isFileExtensionAllowed(file: File, allowedExtensions: string[]): boolean {
  const extension = getFileExtension(file);

  // 使用 includes 判断后缀名是否在 allowedExtensions 数组中
  return allowedExtensions.includes(extension);
}

/**
 * 判断变量是否为对象且不是空对象
 * @param value 要检查的变量
 * @returns 如果是对象且不是空对象则返回 true，否则返回 false
 */
export function isNonEmptyObject(value: any): boolean {
  return value !== null && typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length > 0;
}

/**
 * 合并两个数组且覆盖
 * @param array1
 * @param array2
 * @returns
 */
export function mergeArrays(array1: any[], array2: any[]) {
  const containsAll = array2.every((item2) => array1.some((item1) => item1.name === item2.name));
  if (containsAll) return array1;
  const mergedArray = [...array1];
  array2.forEach((item2) => {
    if (!mergedArray.some((item1) => item1.name === item2.name)) {
      mergedArray.push({ name: item2.name, showClose: true });
    }
  });
  return mergedArray;
}

/**
 * 去除所有的HTML标签和空格
 * @param {string} input
 * @returns
 */
export function removeTagsAndSpaces(input: string, p0: { integralType: number }, p1: string): string {
  if (input) {
    const withoutTags = input.replace(/<\/?[^>]+(>|$)/g, '');
    const withoutEntities = withoutTags.replace(/&nbsp;/g, '');
    const withoutSpaces = withoutEntities.replace(/\s+/g, '');
    return withoutSpaces;
  }
  return input;
}

/**
 * 日期字符串，格式为 'YYYY-MM-DD'
 * @param dateString
 * @returns
 */
export function formatDate(dateString: string): string {
  if (dateString) {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  }
  return '';
}
/**
 *params 后台接口请求参数
 *url 后台接口地址
 *name 导出文件名称
 * */
export async function downLoad(url: string, params: object | {}, name: string): Promise<any> {
  try {
    const response = await fetch(VITE_API_URL.replace(/\/+$/, '') + VITE_API_URL_PREFIX.replace(/\/+$/, '') + url, {
      method: 'POST',
      body: window.JSON.stringify(params),
      credentials: 'include',
      headers: new Headers({
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      }),
    });

    const blob = await response.blob();

    const newUrl = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = newUrl;
    const fileName = `${name}.xlsx`;
    a.download = decodeURIComponent(fileName);
    a.click();
  } catch (e) {
    console.error('---文件下载异常----', e);
  }
}

/**
 * 下载图片文件
 * @param imageUrl 图片URL
 * @param fileName 文件名称
 */
export async function downloadImage(imageUrl: string, fileName: string): Promise<void> {
  try {
    const response = await fetch(imageUrl, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('下载失败');
    }

    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(url);
  } catch (e) {
    console.error('---图片下载异常----', e);
    throw e;
  }
}

/** 判断字符串是否为空 */
export function strIsNull(str: any): boolean {
  return (
    str !== 0 &&
    (str === undefined ||
      str === null ||
      str === '' ||
      (Object.prototype.toString.call(str) === '[object Object]' && Object.keys(str).length === 0) ||
      (Array.isArray(str) && (str.length === 0 || str.every((s) => strIsNull(s)))))
  );
}

/**
 * 构造树型结构数据
 * @param data 数据源
 * @param id id字段 默认 'id'
 * @param parentId 父节点字段 默认 'parentId'
 * @param children 孩子节点字段 默认 'children'
 */
export function handleTree<T extends Record<string, any>>(
  data: T[],
  id: string = 'id',
  parentId: string = 'parentId',
  children: string = 'children',
): T[] {
  const config = {
    id,
    parentId,
    childrenList: children,
  };

  const childrenListMap: Record<number | string, T[]> = {};
  const nodeIds: Record<number | string, T> = {};
  const tree: T[] = [];

  // 构造映射关系
  for (const item of data) {
    const parent = item[config.parentId];
    const itemId = item[config.id];
    if (!childrenListMap[parent]) {
      childrenListMap[parent] = [];
    }
    nodeIds[itemId] = item;
    childrenListMap[parent].push(item);
  }

  // 构造根节点
  for (const item of data) {
    const parent = item[config.parentId];
    if (parent == null || !(parent in nodeIds)) {
      // 处理根节点
      tree.push({ ...item });
    }
  }

  // 递归填充子节点
  const fillChildren = (node: T) => {
    const itemId = node[config.id];
    const children = childrenListMap[itemId] || [];
    node[config.childrenList] = children.map((child) => {
      const childCopy = { ...child };
      fillChildren(childCopy);
      return childCopy;
    });
  };

  tree.forEach((root) => fillChildren(root));
  return tree;
}
