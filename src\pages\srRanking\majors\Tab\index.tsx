import React, { memo, useState } from 'react';
import { Tabs, TabValue } from 'tdesign-react';
import { TAB_LIST } from './consts';
import { useLocation, useNavigate } from 'react-router-dom';

const SRRankingMajorsTab: React.FC = () => {
  const location = useLocation();
  const { type } = location.state || {};
  const [tabValue, setTabValue] = useState<TabValue>(type || 1);
  const navigate = useNavigate();

  const handleChange = (val: TabValue) => {
    navigate('/srRanking/majors', {
      state: {
        type: val,
      },
    });
    setTabValue(val);
  };

  return (
    <div>
      <Tabs value={tabValue} onChange={handleChange} list={TAB_LIST} />
    </div>
  );
};

export default memo(SRRankingMajorsTab);
