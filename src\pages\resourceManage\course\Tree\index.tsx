import React, { memo, useEffect } from 'react';
import { Loading, Menu, Empty } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectResourceManageCourse, getSubjectList, setActive } from 'modules/resourceManage/course';
import Style from './index.module.less';

const { MenuItem } = Menu;

const Header = () => <h3 className={Style.header}>科目名称</h3>;

const CourseTree: React.FC = () => {
  const dispatch = useAppDispatch();
  const { subjectLoading, subjectList, active } = useAppSelector(selectResourceManageCourse);

  useEffect(() => {
    dispatch(getSubjectList());
  }, []);

  return (
    <div className={Style.container}>
      <Loading size='small' loading={subjectLoading}>
        <Menu value={active} onChange={(v) => dispatch(setActive(v))} logo={<Header />}>
          {subjectList &&
            subjectList.map(({ subjectId, subjectName }) => (
              <MenuItem value={subjectId} key={subjectId}>
                <span>{subjectName}</span>
              </MenuItem>
            ))}
          {subjectList.length === 0 && <Empty />}
        </Menu>
      </Loading>
    </div>
  );
};

export default memo(CourseTree);
