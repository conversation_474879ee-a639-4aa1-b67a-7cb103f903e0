import React, { memo, useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Form, Row, Col, Input, Button, Textarea, Space, InputNumber, Loading } from 'tdesign-react';
import classnames from 'classnames';
import { FormInstanceFunctions } from 'tdesign-react/es/form/type';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import { Upload, Back } from 'components';
import { selectSpeciality, getMajorsInfo } from 'modules/directory/speciality';
import { useAppDispatch, useAppSelector } from 'modules/store';

const { FormItem } = Form;

export default memo(() => {
  const location = useLocation();
  const { majorsId, type, path, header } = location.state || {};
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>();
  const { majorsInfo, formLoading } = useAppSelector(selectSpeciality);
  const [formItemList, setFormItemList] = useState([]);
  const [fileUrl, setFileUrl] = useState('');

  useEffect(() => {
    if (majorsId) {
      dispatch(getMajorsInfo(majorsId));
    }
  }, [majorsId]);

  useEffect(() => {
    if (majorsInfo) {
      formRef.current?.setFieldsValue(majorsInfo);

      setFormItemList(majorsInfo.employmentDirectionVoList);
      setFileUrl(majorsInfo.majorsBackgroundImgUrl);
    }
  }, [majorsInfo]);

  const onSuccess = ({
    file: {
      response: {
        data: { id },
      },
    },
  }: {
    file: {
      response: {
        data: { id: number };
      };
    };
  }) => {
    formRef.current?.setFieldsValue?.({ majorsBackgroundImgId: id });
  };

  const EmploymentDirection = () =>
    formItemList &&
    formItemList.map((t, i) => (
      <Col key={i} span={12} className={Style.dateCol}>
        <Space>
          <Row gutter={[42, 20]}>
            <Col span={5}>
              <FormItem
                label={`就业方向${i + 1}`}
                initialData={t?.jobName}
                rules={[{ required: i === 0, message: '就业方向必填', type: 'error' }]}
              >
                <Input placeholder='请输入内容' />
              </FormItem>
            </Col>
            <Col span={7} className={Style.dateCol}>
              <FormItem label='岗位平均月薪' requiredMark={true}>
                <Space style={{ alignItems: 'center' }}>
                  <FormItem
                    style={{ marginRight: 0 }}
                    initialData={t?.minAvgMoney}
                    rules={[{ required: true, message: '最小平均月薪必填', type: 'error' }]}
                  >
                    <InputNumber theme='normal' allowInputOverLimit={false} min={0} placeholder='请输入最小平均月薪' />
                  </FormItem>
                  <span>—</span>
                  <FormItem
                    initialData={t?.maxAvgMoney}
                    rules={[{ required: true, message: '最大平均月薪必填', type: 'error' }]}
                  >
                    <InputNumber theme='normal' allowInputOverLimit={false} min={0} placeholder='请输入最大平均月薪' />
                  </FormItem>
                  <span style={{ marginRight: 20 }}>(k)</span>
                </Space>
              </FormItem>
            </Col>
            <Col span={12} className={Style.dateCol}>
              <FormItem initialData={t?.jobDesc}>
                <Textarea placeholder='请输入备注' autosize />
              </FormItem>
            </Col>
          </Row>
        </Space>
      </Col>
    ));

  const handleCancel = () => {
    navigate(path, {
      state: {
        type,
      },
    });
  };

  const handleReset = () => {
    setFormItemList([]);
    dispatch(getMajorsInfo(majorsId));
  };

  return (
    <>
      <Loading loading={formLoading} showOverlay>
        <Back path={path} header={header} params={{ type }} />
        <div className={classnames(CommonStyle.pageWithColor)}>
          <div className={Style.formContainer}>
            <Form ref={formRef} labelWidth={100} labelAlign='top' disabled onReset={handleReset}>
              <Row gutter={[42, 34]}>
                <Col span={12}>
                  <FormItem
                    label='专业名称'
                    name='majorsName'
                    rules={[{ required: true, message: '专业名称必填', type: 'error' }]}
                  >
                    <Input maxlength={30} clearable showLimitNumber placeholder='请输入专业名称' />
                  </FormItem>
                </Col>
                <Col span={12} className={Style.update}>
                  <FormItem label='详情背景' name='majorsBackgroundImgId' rules={[{ required: true }]}>
                    <Upload
                      url='/oss/uploadFile'
                      params={{ businessType: 4 }}
                      theme='image'
                      tips='请上传 .jpg/.png，文件大小在 512kb 以内，建议宽高比例为 75:56'
                      max={1}
                      draggable
                      success={onSuccess}
                      maxFileSize={512}
                      fileUnit={'kb'}
                      accept='.jpg, .png'
                      files={fileUrl ? [{ url: fileUrl }] : []}
                    />
                  </FormItem>
                </Col>

                <Col span={6} className={Style.dateCol}>
                  <FormItem label='专业简介' name='majorsDesc' rules={[{ required: true }]}>
                    <Textarea placeholder='专业简介必填' autosize />
                  </FormItem>
                </Col>
                <Col span={6} className={Style.dateCol}>
                  <FormItem label='专业介绍' name='majorsIntroduce' rules={[{ required: true }]}>
                    <Textarea placeholder='专业介绍必填' autosize />
                  </FormItem>
                </Col>
                {EmploymentDirection()}
                <Col span={12} className={Style.dateCol}>
                  <FormItem label='衔接本科专业' name='majorUndergraduate' rules={[{ required: true }]}>
                    <Textarea placeholder='请输入此专业可衔接的本科专业' autosize />
                  </FormItem>
                </Col>
              </Row>
              <div className={Style.titleBox}></div>
              <FormItem>
                <Space>
                  <Button type='reset'>刷新</Button>
                  <Button theme='default' onClick={handleCancel}>
                    返回
                  </Button>
                </Space>
              </FormItem>
            </Form>
          </div>
        </div>
      </Loading>
    </>
  );
});
