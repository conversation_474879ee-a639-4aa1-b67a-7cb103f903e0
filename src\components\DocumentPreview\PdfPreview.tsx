import React, { useState } from 'react';
import { Document, Page } from 'react-pdf';

interface PdfPreviewProps {
  fileUrl: string; // 作为入参传入的 PDF 文件 URL 或 Blob 对象
}

const PdfPreview: React.FC<PdfPreviewProps> = ({ fileUrl }) => {
  const [numPages, setNumPages] = useState<number>(0); // 总页数
  const [pageNumber, setPageNumber] = useState<number>(1); // 当前页数
  const [scale, setScale] = useState<number>(1); // 缩放比例

  // PDF 加载完成后的回调函数
  const onLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  // 文件格式处理（支持 URL 和 File/Blob 类型）
  const getFileSource = () => {
    if (typeof fileUrl === 'string') {
      // 如果是 URL 字符串
      return fileUrl;
    }
    // 如果是 File 或 Blob 对象
    return URL.createObjectURL(fileUrl);
  };

  return (
    <div>
      <Document
        file={getFileSource()} // 加载 PDF 文件
        onLoadSuccess={onLoadSuccess} // 加载成功回调
        loading='Loading...' // 加载时显示的文本
      >
        <Page pageNumber={pageNumber} scale={scale} />
      </Document>

      {/* 分页控制 */}
      <div>
        <button disabled={pageNumber <= 1} onClick={() => setPageNumber(pageNumber - 1)}>
          上一页
        </button>
        <span>
          当前页数 {pageNumber} 总页数 {numPages}
        </span>
        <button disabled={pageNumber >= numPages} onClick={() => setPageNumber(pageNumber + 1)}>
          下一页
        </button>
      </div>

      {/* 缩放控制 */}
      <div>
        <button onClick={() => setScale(scale - 0.1)} disabled={scale <= 0.5}>
          缩小
        </button>
        <span>Scale: {scale}</span>
        <button onClick={() => setScale(scale + 0.1)} disabled={scale >= 2}>
          放大
        </button>
      </div>
    </div>
  );
};

export default PdfPreview;
