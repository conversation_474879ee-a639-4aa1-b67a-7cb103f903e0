{"name": "brush-question-adm", "version": "0.3.1", "private": true, "scripts": {"start": "npm run dev", "dev": "vite --open --mode development", "dev:linux": "vite --mode development", "build:dzst": "vite build --mode release", "build:cedzst": "vite build --mode test", "preview:cedzst": "npm run build:cedzst && cp -r dist _test && vite preview --mode test", "preview:dzst": "npm run build:dzst && cp -r dist _release && vite preview --mode release", "preview": "vite preview --mode test", "test": "echo \"no test specified,work in process\"", "test:coverage": "echo \"no test specified,work in process\"", "lint": "eslint ./src --ext ts,tsx", "lint:fix": "eslint ./src --ext ts,tsx --fix", "stylelint": "stylelint src/**/*.{html,css,sass,less}", "stylelint:fix": "stylelint --fix src/**/*.{html,css,sass,less}", "prepare-beifen": "node -e \"if(require('fs').existsSync('.git')){process.exit(1)}\" || is-ci || husky install", "nvm": "nvm install $(cat .nvmrc) && nvm use $(cat .nvmrc)", "prepare": "husky install", "gh": "bash deploy-gh.sh"}, "devDependencies": {"@commitlint/cli": "^18.4.1", "@commitlint/config-conventional": "^18.4.0", "@honkhonk/vite-plugin-svgr": "^1.1.0", "@types/echarts": "^4.9.13", "@types/lodash": "^4.14.178", "@types/node": "^20.2.5", "@types/nprogress": "^0.2.3", "@types/qrcode.react": "^1.0.2", "@types/qs": "^6.9.10", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-react": "^1.3.2", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.3.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.4.0", "eslint-config-react-app": "^7.0.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.28.0", "eslint-plugin-simple-import-sort": "^10.0.0", "husky": "^8.0.1", "less": "^4.1.3", "lint-staged": "^15.1.0", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "prettier": "^2.5.1", "rollup-plugin-visualizer": "^5.12.0", "stylelint": "~15.11.0", "stylelint-config-standard": "^34.0.0", "stylelint-order": "~6.0.3", "typescript": "^4.8.4", "vite": "^2.9.15", "vite-svg-loader": "^4.0.0"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@amap/amap-react": "^0.1.5", "@lglab/react-qr-code": "^1.4.5", "@react-pdf-viewer/core": "^3.12.0", "@reduxjs/toolkit": "^1.8.5", "@types/qs": "^6.9.10", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "@wangeditor/plugin-formula": "^1.0.11", "axios": "^1.2.0", "axios-jsonp": "^1.0.4", "classnames": "^2.3.1", "dayjs": "^1.10.7", "echarts": "^5.3.0", "echarts-for-react": "^3.0.2", "history": "^5.3.0", "katex": "^0.15.2", "lodash": "^4.17.21", "qrcode.react": "^3.1.0", "qs": "^6.11.2", "react": "^18.2.0", "react-amap": "^1.2.8", "react-dom": "^18.2.0", "react-file-viewer": "^1.2.1", "react-katex": "^3.0.1", "react-pdf": "^9.2.1", "react-redux": "^7.2.4", "react-router-dom": "^6.3.0", "tdesign-icons-react": "0.3.4", "tdesign-react": "^1.10.5", "tvision-color": "^1.5.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "description": "题库", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "npm run lint:fix"], "*.{html,css,sass,less}": ["npm run stylelint:fix"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}