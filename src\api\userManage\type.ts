/**
 * 基础分页接口
 */
interface IPagination {
  /** 页码 */
  pageNum: number;
  /** 每页大小 */
  pageSize: number;
}

/**
 * 公共属性接口
 */
interface ICommonAttributes {
  /** 学生ID */
  studentId?: number;
  /** 考试ID */
  examId?: number;
  /** 科目ID */
  subjectId?: number;
}

/**
 * 学生考试详情接口
 */
export interface IGetStudentExamDetailApi extends IPagination, ICommonAttributes {}

/**
 * 学生信息列表接口
 */
export type IGetStudentInfoListApi = IPagination;

/**
 * 学生练习列表接口
 */
export interface IGetStudentPracticeList extends IPagination, ICommonAttributes {
  /** 是否正确，0: 错误，1: 正确 */
  isCorrect?: 0 | 1;
}

/**
 * 学生考试列表接口
 */
export interface IGetStudentExamListApi extends IPagination, ICommonAttributes {}

/**
 * 学生收藏或错题列表接口
 */
interface ICollectionOrWrongListApi extends IPagination, ICommonAttributes {
  /** 题干 */
  stem?: string;
}

/**
 * 学生收藏列表接口
 */
export type IGetStudentCollectionListApi = ICollectionOrWrongListApi;

/**
 * 学生错题列表接口
 */
export type IGetStudentWrongListApi = ICollectionOrWrongListApi;

/**
 * 学生关注列表接口
 */
interface IGetStudentFollowListApi extends IPagination {
  /** 学生ID */
  studentId?: number;
  /** 考试ID */
  examId?: number;
  /** 院校ID */
  educationalId?: number;
}

/**
 * 学生关注专业列表接口
 */
export interface IGetStudentFollowMajorsListApi extends IGetStudentFollowListApi {
  /** 专业ID */
  majorId?: number;
}

/**
 * 学生关注院校列表接口
 */
export type IGetStudentFollowEducationalListApi = IGetStudentFollowListApi;

/**
 * 学生看课记录列表接口
 */
export interface IGetStudentWatchCourseProgressListApi extends IPagination, ICommonAttributes {
  courseName?: string;
  studyTime?: {
    min: string;
    max: string;
  };
}

/**
 * 学生登录日志列表接口
 */
export interface IGetStudentLoginLogListApi extends IPagination, ICommonAttributes {
  status?: 0 | 1;
  params?: {
    beginTime: string;
    endTime: string;
  };
}

/**
 * 统一接口类型定义
 */
export type IApiParams =
  | IGetStudentExamDetailApi
  | IGetStudentInfoListApi
  | IGetStudentPracticeList
  | IGetStudentExamListApi
  | IGetStudentCollectionListApi
  | IGetStudentWrongListApi
  | IGetStudentFollowMajorsListApi
  | IGetStudentFollowEducationalListApi;
