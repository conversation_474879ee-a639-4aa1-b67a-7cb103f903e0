import React, { useState, useEffect, useRef } from 'react';
import {
  Radio,
  Dialog,
  Form,
  MessagePlugin,
  Loading,
  InputNumber,
  type FormInstanceFunctions,
  SubmitContext,
  TableRowData,
  Row,
  Col,
  Switch,
  Space,
} from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import classnames from 'classnames';
import {
  selectPointsManagement,
  getIntegralList,
  getIntegralRule,
  addOrUpdateIntegralRule, updateContinuousSignIn
} from 'modules/pointsManagement';
import Style from '../index.module.less';

const { FormItem } = Form;

interface IRuleDialogProps {
  typeProp: string;
  visibleProp: boolean;
  rowProp?: TableRowData;
  onCancel?: (val: boolean) => void;
  onSuccess?: (val: boolean) => void;
}

export const RuleDialog = ({ visibleProp, rowProp, typeProp, onCancel }: IRuleDialogProps) => {
  const dispatch = useAppDispatch();

  const { pageNum, pageSize, formData, formLoading, formDataLoading } = useAppSelector(selectPointsManagement);
  const formRef = useRef<FormInstanceFunctions>();
  const [visible, setVisible] = useState<boolean>(false);
  const INTIN_DATA = formData ?? {
    successAddIntegral: 1,
    failAddIntegral: 0,
    theSameObtainIntegral: 0,
    failAddIntegralLimit: 30,
    pointsLimit: 10,
    pointsNewMember: 20,
    pointsReg: 10,
    isContinuousSignIn: 1,
    continuousSignInDayList: [],

  };
  const [checked, setChecked] = useState('');
  useEffect(() => {
    dispatch(getIntegralRule(''));
    setVisible(visibleProp);
    setChecked(formData?.isContinuousSignIn)
  }, [visibleProp, rowProp, typeProp]);

  const cancelBtn = () => {
    setVisible(false);
    if (onCancel) onCancel(false);
  };

  const onSubmit = async (e: SubmitContext) => {
    if (e.validateResult === true) {
      try {
        const formValue = formRef.current?.getFieldsValue?.(true) || {};
        const continuousSignInDayList = new Array<object>();
        // eslint-disable-next-line no-param-reassign
        formData.continuousSignInDayList.forEach((item: object, index: number) => {
          continuousSignInDayList.push({
            ...item,
            signInPoints: formValue?.[`dayIndex${index + 1}`],
          });
        });
        await dispatch(
          addOrUpdateIntegralRule({
            ...formValue,
            continuousSignInDayList,
            ruleId: formData?.ruleId,
          }),
        ).unwrap();
        cancelBtn();
        await dispatch(
          getIntegralList({
            pageSize,
            pageNum,
          }),
        );
        MessagePlugin.success(`${formData ? '更新' : '添加'}成功！`);
      } catch (e) {
        MessagePlugin.warning(`${formData ? '更新' : '添加'}失败！`);
      }
    }
  };

  return (
    <>
      {visible && (
        <Dialog
          confirmLoading={formLoading}
          header='积分规则'
          visible={visible}
          confirmBtn={'确定'}
          onConfirm={() => formRef.current?.submit?.()}
          cancelBtn={'取消'}
          onCancel={cancelBtn}
          onCloseBtnClick={cancelBtn}
          width={900}
        >
          <Loading loading={formDataLoading}>
            <Form ref={formRef} className={classnames(Style.itemContainer)} labelWidth={0} onSubmit={onSubmit}>
              <Row>
                <Col span={3}>
                  <FormItem
                    name='successAddIntegral'
                    initialData={INTIN_DATA.successAddIntegral}
                    label='答对题目增加积分'
                    rules={[{ required: true, message: '答对题目增加积分必填', type: 'error' }]}
                  >
                    <InputNumber allowInputOverLimit={false} min={0} max={10} placeholder='请输入答对题目增加积分' />
                  </FormItem>
                </Col>
                <Col span={3}>
                  <FormItem
                    name='failAddIntegral'
                    label='答错题目增加积分'
                    initialData={INTIN_DATA.failAddIntegral}
                    rules={[{ required: true, message: '答错题目增加积分必填', type: 'error' }]}
                  >
                    <InputNumber allowInputOverLimit={false} min={0} max={10} placeholder='请输入答错题目增加积分' />
                  </FormItem>
                </Col>
                <Col span={3}>
                  <FormItem
                    name='theSameObtainIntegral'
                    initialData={INTIN_DATA.theSameObtainIntegral}
                    label='相同题目是否重复获取积分'
                    rules={[{ required: true, message: '相同题目是否重复获取积分必选', type: 'error' }]}
                  >
                    <Radio.Group>
                      <Radio value={0}>否</Radio>
                      <Radio value={1}>是</Radio>
                    </Radio.Group>
                  </FormItem>
                </Col>
                <Col span={3}>
                  <FormItem
                    name='pointsLimit'
                    label='每日刷题增加积分上限'
                    initialData={INTIN_DATA.pointsLimit}
                    rules={[{ required: true, message: '每日刷题增加积分上限必填', type: 'error' }]}
                  >
                    <InputNumber
                      allowInputOverLimit={false}
                      min={0}
                      max={100}
                      placeholder='请输入每日刷题增加积分上限'
                    />
                  </FormItem>
                </Col>
              </Row>
              <Row style={{ marginTop: 20 }}>
                <Col span={3}>
                  <FormItem
                    name='pointsReg'
                    label='首次注册增加积分'
                    initialData={INTIN_DATA.pointsReg}
                    rules={[{ required: true, message: '首次注册增加积分必填', type: 'error' }]}
                  >
                    <InputNumber allowInputOverLimit={false} min={0} max={50} placeholder='请输入首次注册增加积分' />
                  </FormItem>
                </Col>
                <Col span={3}>
                  <FormItem
                    name='pointsNewMember'
                    label='邀请新人注册增加积分'
                    initialData={INTIN_DATA.pointsNewMember}
                    rules={[{ required: true, message: '邀请新人注册增加积分必填', type: 'error' }]}
                  >
                    <InputNumber
                      allowInputOverLimit={false}
                      min={0}
                      max={50}
                      placeholder='请输入邀请新人注册增加积分'
                    />
                  </FormItem>
                </Col>
              </Row>
              <Row>
                <Col style={{ marginTop: 30 }}>
                  <span>连续5天签到增加积分（中断则重新计算）：</span>
                  <Switch
                    size='large'
                    value={checked == 1}
                    onChange={async (val) => {
                      await dispatch(
                        updateContinuousSignIn({
                          ruleId: formData.ruleId,
                          isOpen: val ? 1 : 0,
                        }),
                      );
                      setChecked(val);
                      // eslint-disable-next-line eqeqeq
                      MessagePlugin.success(`${val == 0 ? '关闭' : '开启'}成功！`);
                    }} />
                </Col>
              </Row>
              <Row style={{ marginTop: 10 }}>
                <Space breakLine={true} className={Style.spaces}>
                  {INTIN_DATA.continuousSignInDayList.map((item, index) => (
                    <FormItem
                      key={item.id}
                      label={item.dayName}
                      name={`dayIndex${index + 1}`}
                      initialData={item.signInPoints}
                      rules={[{ required: true, message: `${item.dayName}积分数量必填`, type: 'error' }]}
                    >
                      <InputNumber
                        allowInputOverLimit={false}
                        min={0}
                        max={100}
                        placeholder={`请输入${item.dayName}积分数量`}
                      />
                    </FormItem>
                  ))}
                </Space>
              </Row>
            </Form>
          </Loading>
        </Dialog>
      )}
    </>
  );
};
