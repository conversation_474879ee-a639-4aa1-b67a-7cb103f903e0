import { Upload } from 'components';
import { themeType } from 'components/Upload/types';
import React, { Fragment, memo, useEffect, useState } from 'react';
import { UploadFile, type InputProps } from 'tdesign-react';

interface IImg {
  url?: string;
  id?: number;
}

interface CustomUploadProps {
  value: IImg;
  onChange: (val: IImg) => void;
  tips?: string; // 可选的 tips 提示信息
  status?: InputProps['status']; // 继承 tdesign Input 的 status 类型
  url: string;
  theme: themeType;
  title: string;
  accept: string;
  maxFileSize: number;
  max: number;
  draggable: boolean;
  params: any;
}

const CustomUpload: React.FC<CustomUploadProps> = ({
  value,
  onChange,
  tips,
  status,
  url,
  theme,
  title,
  accept,
  maxFileSize,
  max,
  draggable,
  params,
}) => {
  const handleUploadSuccess = ({
    file: {
      response: {
        data: { id, fileUrl },
      },
    },
  }: {
    file: { response: { data: { id: number; fileUrl: string } } };
  }) => {
    const obj = {
      url: fileUrl,
      id,
    };

    onChange(obj);
  };

  const handleRemove = () => {
    onChange({});
  };

  return (
    <Fragment>
      <Upload
        url={url}
        theme={theme}
        tips={title}
        accept={accept}
        maxFileSize={maxFileSize}
        max={max}
        draggable={draggable}
        params={params}
        files={value?.url ? [value] : []}
        success={handleUploadSuccess}
        remove={handleRemove}
      />

      {status && <div className={`t-input__tips t-input__tips--${status}`}>{tips}</div>}
    </Fragment>
  );
};

export default memo(CustomUpload);
