import React, { memo, useEffect, useRef } from 'react';
import { Space, Button, Loading, Pagination, MessagePlugin, type PageInfo } from 'tdesign-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'modules/store';
import {
  getInformationReleaseList,
  openOrCloseInfo,
  clearPageState,
  selectInformationRelease,
  deletInformationRelease,
} from 'modules/operationManage/InformationRelease';
import { CardImage, Search, Empty } from 'components';
import Style from './index.module.less';
import { formatDate, strIsNull } from 'utils/tool';
import { IRef } from 'components/Form';

export default memo(() => {
  const dispatch = useAppDispatch();
  const { list, total, pageSize, pageNum, loading } = useAppSelector(selectInformationRelease);
  const navigate = useNavigate();
  const searchRef = useRef<IRef | null>(null);

  const searchParams = { pageNum, pageSize };

  useEffect(() => {
    dispatch(getInformationReleaseList(searchParams));

    return () => {
      dispatch(clearPageState());
    };
  }, []);

  const onChange = async ({ current, pageSize }: PageInfo) => {
    const params = JSON.parse(JSON.stringify(searchParams));

    if (!strIsNull(searchRef) && searchRef?.current) {
      const formParams = searchRef.current.getFormParams();

      const fParams = Object.fromEntries(
        Object.entries(formParams).filter(([_, value]) => value !== undefined),
      ) as Object;

      Object.assign(params, fParams);
    }

    await dispatch(
      getInformationReleaseList({
        ...params,
        pageSize,
        pageNum: current,
      }),
    );
  };

  const handlePageSizeChange = async (pageSize: number, { current }: PageInfo) => {
    const params = JSON.parse(JSON.stringify(searchParams));

    if (!strIsNull(searchRef) && searchRef?.current) {
      const formParams = searchRef.current.getFormParams();

      const fParams = Object.fromEntries(
        Object.entries(formParams).filter(([_, value]) => value !== undefined),
      ) as Object;

      Object.assign(params, fParams);
    }

    await dispatch(
      getInformationReleaseList({
        ...params,
        pageSize,
        pageNum: current,
      }),
    );
  };

  return (
    <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getInformationReleaseList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '资讯标题',
            field: 'title',
          },
          {
            type: 'input',
            label: '视频号ID',
            field: 'videoNumberId',
          },
          {
            type: 'input',
            label: '视频feedID',
            field: 'videoFeedId',
          },
        ]}
      />
      <Button
        style={{ marginBottom: '20px' }}
        onClick={() => navigate('/operationManage/InformationRelease/add', { state: { type: 'add' } })}
      >
        新增资讯
      </Button>
      <Loading loading={loading}>
        <Space breakLine size={[38, 38]} align='center'>
          {list.map((item: any) =>
            CardImage({
              key: item.infoId,
              status: item.isOpen === 1,
              cover: item.coverUrl,
              title: item.title,
              width: '280px',
              imgHeight: '300px',
              imgWidth: '280px',
              content: [formatDate(item.createTime)],
              onDelete: () => dispatch(deletInformationRelease(item.infoId)),
              onEdit: () =>
                navigate('/operationManage/InformationRelease/edit', { state: { row: item, type: 'edit' } }),
              onChange: async (val) => {
                await dispatch(
                  openOrCloseInfo({
                    infoId: item.infoId,
                    isOpen: val ? 1 : 0,
                  }),
                );
                MessagePlugin.success(`${val ? '开启' : '关闭'}成功！`);
              },
            }),
          )}
        </Space>
        {list && list.length === 0 && <Empty />}
        <Pagination
          className={Style.pagination}
          total={total}
          pageSizeOptions={[12, 24, 36]}
          pageSize={pageSize}
          onChange={onChange}
          onPageSizeChange={handlePageSizeChange}
        />
      </Loading>
    </div>
  );
});
