import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getDeptTreeApi,
  getSystemUserListApi,
  delSystemUserApi,
  getRolePostApi,
  addUserApi,
  putUserApi,
  resetPwdApi,
} from 'api';
import type { IGetSystemUserListApi, IAddUserApi, IPutUserApi, IResetPwdApi } from 'api';
import { MessagePlugin } from 'tdesign-react/es/message/Message';

const namespace = 'system/user';

interface IInitialState {
  loading: boolean;
  formLoading: boolean;
  pageNum: number;
  pageSize: number;
  total: number;
  contractList: any[];
  treeList: any[];
  treeLoading: boolean;
  active: number | undefined;
  delLoading: boolean;
  data: null | object;
  userName?: string;
  phonenumber?: number | string;
  /** 状态： 0: 正常 1: 停用 */
  status?: 0 | 1 | undefined;
}

const initialState: IInitialState = {
  loading: true,
  formLoading: false,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  contractList: [],
  treeList: [],
  treeLoading: true,
  active: undefined,
  delLoading: false,
  data: null,
  userName: '',
  phonenumber: '',
  status: undefined,
};

export const getSystemUserList = createAsyncThunk(
  `${namespace}/getSystemUserList`,
  async (params: IGetSystemUserListApi) => {
    const { rows, total } = await getSystemUserListApi(params);
    return {
      list: rows,
      total,
      pageSize: params.pageSize,
      pageNum: params.pageNum,
    };
  },
);

export const getRolePost = createAsyncThunk(`${namespace}/getRolePost`, async (id: number) => {
  const data = await getRolePostApi(id);
  return data;
});

export const getDeptTree = createAsyncThunk(`${namespace}/getDeptTree`, async (_, { getState }) => {
  const state = getState() as RootState;
  const { active } = state.systemUser;
  const { data } = await getDeptTreeApi();
  if (Array.isArray(data) && data.length > 0 && !active) {
    return {
      list: data,
      active: data[0].id,
    };
  }
  return {
    list: data,
    active,
  };
});

export const addUser = createAsyncThunk(`${namespace}/addUser`, async (params: IAddUserApi) => {
  await addUserApi(params);
});

export const putUser = createAsyncThunk(`${namespace}/putUser`, async (params: IPutUserApi) => {
  await putUserApi(params);
});

export const resetPwd = createAsyncThunk(`${namespace}/resetPwd`, async (params: IResetPwdApi) => {
  await resetPwdApi(params);
});

export const delSystemUser = createAsyncThunk(`${namespace}/delSystemUser`, async (params: any, { dispatch }) => {
  const { userId, ...rest } = params;
  await delSystemUserApi(userId);
  await dispatch(getSystemUserList({ ...rest }));
  MessagePlugin.success('删除成功！');
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setActive: (state, action) => {
      state.active = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSystemUserList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSystemUserList.fulfilled, (state, action) => {
        state.loading = false;
        state.contractList = action.payload?.list;
        state.total = action.payload?.total;
        state.pageSize = action.payload?.pageSize;
        state.pageNum = action.payload?.pageNum;
      })
      .addCase(getSystemUserList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getDeptTree.pending, (state) => {
        state.treeLoading = true;
      })
      .addCase(getDeptTree.fulfilled, (state, action) => {
        state.treeLoading = false;
        state.treeList = action.payload.list;
        state.active = action.payload.active;
      })
      .addCase(getDeptTree.rejected, (state) => {
        state.treeLoading = false;
      })

      .addCase(getRolePost.fulfilled, (state, action) => {
        state.data = action.payload;
      })

      .addCase(addUser.pending, (state) => {
        state.formLoading = true;
      })
      .addCase(addUser.fulfilled, (state) => {
        state.formLoading = false;
      })
      .addCase(addUser.rejected, (state) => {
        state.formLoading = false;
      })

      .addCase(putUser.pending, (state) => {
        state.formLoading = true;
      })
      .addCase(putUser.fulfilled, (state) => {
        state.formLoading = false;
      })
      .addCase(putUser.rejected, (state) => {
        state.formLoading = false;
      })

      .addCase(delSystemUser.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(delSystemUser.fulfilled, (state) => {
        state.delLoading = false;
      })
      .addCase(delSystemUser.rejected, (state) => {
        state.delLoading = false;
      });
  },
});

export const { clearPageState, setActive } = listBaseSlice.actions;

export const selectSystemUser = (state: RootState) => state.systemUser;

export default listBaseSlice.reducer;
