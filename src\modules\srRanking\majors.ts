import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  addHotApi,
  deleteHotApi,
  dragSortHotApi,
  editHotApi,
  getHotInfoApi,
  getMajorsListByExamIdApi,
  getEnrollmentListApi,
  getSalaryListApi,
  getHotListApi,
  publicGetExamListApi,
  getInstitutionListApi,
} from 'api';
import {
  IAddHotBo,
  IEditHotBo,
  IGetEnrollmentListBo,
  IGetSalaryListBo,
  IGetHotListBo,
  IGetInstitutionListBo,
} from 'types/srRanking';
import { IDragSort } from 'types';
import { MenuValue, MessagePlugin } from 'tdesign-react';
import { strIsNull } from 'utils/tool';

const namespace = 'srRanking/majors';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  treeLoading: boolean;
  treeList: Array<any>;
  active: MenuValue;
  hotInfoLoading: boolean;
  hotInfo: any;
  deleteHotLoading: boolean;
  addEditHotLoading: boolean;
  majorsList: Array<any>;
  institutionListLoading: boolean;
  institutionList: Array<any>;
}

const initialState: IInitialState = {
  loading: false,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  treeLoading: false,
  treeList: [],
  active: '',
  hotInfoLoading: false,
  hotInfo: {},
  deleteHotLoading: false,
  addEditHotLoading: false,
  majorsList: [],
  institutionListLoading: false,
  institutionList: [],
};

export const getTreeList = createAsyncThunk(`${namespace}/getTreeList`, async () => {
  const { data } = await publicGetExamListApi();

  return data;
});

export const getEnrollmentList = createAsyncThunk(
  `${namespace}/getEnrollmentList`,
  async (bo: IGetEnrollmentListBo) => {
    const { data } = await getEnrollmentListApi(bo);

    return {
      list: data.rows,
      total: data.total,
      pageNum: bo.pageNum,
      pageSize: bo.pageSize,
    };
  },
);

export const getSalaryList = createAsyncThunk(`${namespace}/getSalaryList`, async (bo: IGetSalaryListBo) => {
  const { data } = await getSalaryListApi(bo);

  return {
    list: data.rows,
    total: data.total,
    pageNum: bo.pageNum,
    pageSize: bo.pageSize,
  };
});

export const getHotList = createAsyncThunk(`${namespace}/getHotList`, async (bo: IGetHotListBo) => {
  const { data } = await getHotListApi(bo);

  return {
    list: data.rows,
    total: data.total,
    pageNum: bo.pageNum,
    pageSize: bo.pageSize,
  };
});

export const getHotInfo = createAsyncThunk(`${namespace}/getHotInfo`, async (id: number) => {
  const { data } = await getHotInfoApi(id);

  return data;
});

export const getMajorsList = createAsyncThunk(`${namespace}/getMajorsList`, async (id: number) => {
  const { data } = await getMajorsListByExamIdApi(id);

  return data;
});

export const getInstitutionList = createAsyncThunk(
  `${namespace}/getInstitutionList`,
  async (bo: IGetInstitutionListBo) => {
    const { data } = await getInstitutionListApi(bo);

    return data;
  },
);

export const dragSortHot = createAsyncThunk(`${namespace}/dragSortHot`, async (bo: IDragSort) => {
  try {
    const { code, data } = await dragSortHotApi(bo);

    if (code === 200) {
      return data;
    }
  } catch (_) {
    return false;
  }
});

export const addHot = createAsyncThunk(`${namespace}/addHot`, async (bo: IAddHotBo) => {
  try {
    const { data } = await addHotApi(bo);

    return data;
  } catch (error) {
    return false;
  }
});

export const editHot = createAsyncThunk(`${namespace}/editHot`, async (bo: IEditHotBo) => {
  try {
    const { data } = await editHotApi(bo);

    return data;
  } catch (error) {
    return false;
  }
});

export const deleteHot = createAsyncThunk(`${namespace}/deleteHot`, async (id: number) => {
  await deleteHotApi(id);
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setActive: (state, { payload }) => {
      state.active = payload;
    },
    clearInstitutionList: (state) => {
      state.institutionList = [];
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getTreeList.pending, (state) => {
        state.treeLoading = true;
      })
      .addCase(getTreeList.fulfilled, (state, action) => {
        state.treeList = action.payload.rows;

        if (!strIsNull(action.payload.rows)) {
          state.active = action.payload.rows[0].examId;
        }

        state.treeLoading = false;
      })
      .addCase(getTreeList.rejected, (state) => {
        state.treeLoading = false;
      })

      .addCase(getEnrollmentList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getEnrollmentList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getEnrollmentList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getSalaryList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSalaryList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getSalaryList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getHotList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getHotList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getHotList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getHotInfo.pending, (state) => {
        state.hotInfoLoading = true;
      })
      .addCase(getHotInfo.fulfilled, (state, action) => {
        state.hotInfoLoading = false;
        state.hotInfo = action.payload;
      })
      .addCase(getHotInfo.rejected, (state) => {
        state.hotInfoLoading = false;
      })

      .addCase(getMajorsList.fulfilled, (state, action) => {
        state.majorsList = action.payload;
      })

      .addCase(getInstitutionList.pending, (state) => {
        state.institutionListLoading = true;
      })
      .addCase(getInstitutionList.fulfilled, (state, action) => {
        state.institutionList = action.payload.rows;
        state.institutionListLoading = false;
      })
      .addCase(getInstitutionList.rejected, (state) => {
        state.institutionListLoading = false;
      })

      .addCase(dragSortHot.pending, (state) => {
        state.loading = true;
      })
      .addCase(dragSortHot.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(dragSortHot.rejected, (state) => {
        state.loading = false;
      })

      .addCase(addHot.pending, (state) => {
        state.addEditHotLoading = true;
      })
      .addCase(addHot.fulfilled, (state) => {
        state.addEditHotLoading = false;

        MessagePlugin.success('添加热门推荐磅成功');
      })
      .addCase(addHot.rejected, (state) => {
        state.addEditHotLoading = false;
      })

      .addCase(editHot.pending, (state) => {
        state.addEditHotLoading = true;
      })
      .addCase(editHot.fulfilled, (state) => {
        state.addEditHotLoading = false;

        MessagePlugin.success('编辑热门推荐磅成功');
      })
      .addCase(editHot.rejected, (state) => {
        state.addEditHotLoading = false;
      })

      .addCase(deleteHot.pending, (state) => {
        state.deleteHotLoading = true;
      })
      .addCase(deleteHot.fulfilled, (state) => {
        state.deleteHotLoading = false;
      })
      .addCase(deleteHot.rejected, (state) => {
        state.deleteHotLoading = false;
      });
  },
});

export const { clearPageState, setActive, clearInstitutionList } = listBaseSlice.actions;

export const selectSRRankingMajors = (state: RootState) => state.srRankingMajors;

export default listBaseSlice.reducer;
