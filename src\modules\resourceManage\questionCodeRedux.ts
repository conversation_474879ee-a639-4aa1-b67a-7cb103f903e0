import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  quesCodeGetList,
  quesCodeCrateQrCode,
  uploadQrCode,
  quesCodeSave,
  quesCodeEdit,
  quesCodeDelete,
  quesCodeGetById,
  quesCodePvwVideo,
  quesCodeDeleteQrCode,
} from 'api'; // Import the API functions

import { IQuesCodeEntity, IQuesCodeQueryBo, IPreviewVideoBo, IQrCode } from 'types/resourceManage';
import { previewVideoApi } from 'api/resourceManage/course';

const namespace = 'resourceManage/quesCode';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  qrCodeInfoLoading: boolean;
  error: string | null;
  deleteLoading: boolean;
  // Add properties for video functionality
  chapterInfoLoading: boolean;
  chapterInfo: any;
  previewVideoLoading: boolean;
  previewVideoInfo: any;
}

const initialState: IInitialState = {
  loading: false,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  qrCodeInfoLoading: false,
  error: null,
  deleteLoading: false,
  // Initialize video-related properties
  chapterInfoLoading: false,
  chapterInfo: {},
  previewVideoLoading: false,
  previewVideoInfo: {},
};

// Async Thunks
export const fetchQuesCodeList = createAsyncThunk(
  `${namespace}/fetchQuesCodeList`,
  async (params: IQuesCodeQueryBo, { rejectWithValue }) => {
    try {
      const { data } = await quesCodeGetList(params);
      return {
        list: data.rows,
        total: data.total,
        pageNum: params.pageNum,
        pageSize: params.pageSize,
      };
    } catch (error) {
      return rejectWithValue('Failed to fetch QR codes');
    }
  },
);

export const createQuesCode = createAsyncThunk(
  `${namespace}/createQuesCode`,
  async (params: IQuesCodeEntity, { rejectWithValue }) => {
    try {
      await quesCodeCrateQrCode(params);
      return { success: true };
    } catch (error) {
      return rejectWithValue('Failed to create QR code');
    }
  },
);

export const fetchUploadQrCode = createAsyncThunk(
  `${namespace}/createQuesCode`,
  async (params: IQrCode, { rejectWithValue }) => {
    try {
      await uploadQrCode(params);
      return { success: true };
    } catch (error) {
      return rejectWithValue('Failed to create QR code');
    }
  },
);

export const generateQrCode = createAsyncThunk(
  `${namespace}/generateQrCode`,
  async (id: number, { rejectWithValue }) => {
    try {
      await quesCodeCrateQrCode({ id } as IQuesCodeEntity);
      return { success: true };
    } catch (error) {
      return rejectWithValue('Failed to generate QR code');
    }
  },
);

export const saveQuesCode = createAsyncThunk(
  `${namespace}/saveQuesCode`,
  async (params: IQuesCodeEntity, { rejectWithValue }) => {
    try {
      await quesCodeSave(params);
      return { success: true };
    } catch (error) {
      return rejectWithValue('Failed to save QR code');
    }
  },
);

export const editQuesCode = createAsyncThunk(
  `${namespace}/editQuesCode`,
  async (params: IQuesCodeEntity, { rejectWithValue }) => {
    try {
      await quesCodeEdit(params);
      return { success: true };
    } catch (error) {
      return rejectWithValue('Failed to edit QR code');
    }
  },
);

export const deleteQuesCode = createAsyncThunk(
  `${namespace}/deleteQuesCode`,
  async (qrCodeId: number, { rejectWithValue }) => {
    try {
      await quesCodeDelete(qrCodeId);
      return { success: true };
    } catch (error) {
      return rejectWithValue('Failed to delete QR code');
    }
  },
);

export const fetchQuesCodeById = createAsyncThunk(
  `${namespace}/fetchQuesCodeById`,
  async (qrCodeId: number, { rejectWithValue }) => {
    try {
      const { data } = await quesCodeGetById(qrCodeId.toString());
      return data;
    } catch (error) {
      return rejectWithValue('Failed to fetch QR code details');
    }
  },
);

export const previewQuesCodeVideo = createAsyncThunk(
  `${namespace}/previewQuesCodeVideo`,
  async (params: string, { rejectWithValue }) => {
    try {
      await quesCodePvwVideo(params);
      return { success: true };
    } catch (error) {
      return rejectWithValue('Failed to preview video');
    }
  },
);

export const deleteQrCode = createAsyncThunk(
  `${namespace}/deleteQrCode`,
  async (qrCodeId: string, { rejectWithValue }) => {
    try {
      await quesCodeDeleteQrCode(qrCodeId);
      return { success: true };
    } catch (error) {
      return rejectWithValue('Failed to delete QR code');
    }
  },
);

export const previewVideo = createAsyncThunk(
  `${namespace}/previewVideo`,
  async (bo: IPreviewVideoBo, { rejectWithValue }) => {
    try {
      const { data } = await previewVideoApi(bo);
      return data;
    } catch (error) {
      return rejectWithValue('Failed to preview video');
    }
  },
);

// Slice
const quesCodeSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      // Fetch QR codes list
      .addCase(fetchQuesCodeList.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchQuesCodeList.fulfilled, (state, action) => {
        state.loading = false;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(fetchQuesCodeList.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Create QR code
      .addCase(createQuesCode.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createQuesCode.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(createQuesCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Generate QR code
      .addCase(generateQrCode.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(generateQrCode.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(generateQrCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Save QR code
      .addCase(saveQuesCode.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(saveQuesCode.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(saveQuesCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Edit QR code
      .addCase(editQuesCode.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(editQuesCode.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(editQuesCode.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Delete QR code
      .addCase(deleteQuesCode.pending, (state) => {
        state.deleteLoading = true;
        state.error = null;
      })
      .addCase(deleteQuesCode.fulfilled, (state) => {
        state.deleteLoading = false;
      })
      .addCase(deleteQuesCode.rejected, (state, action) => {
        state.deleteLoading = false;
        state.error = action.payload as string;
      })

      // Fetch QR code details by ID
      .addCase(fetchQuesCodeById.pending, (state) => {
        state.qrCodeInfoLoading = true;
        state.chapterInfoLoading = true;
        state.error = null;
      })
      .addCase(fetchQuesCodeById.fulfilled, (state, action) => {
        state.qrCodeInfoLoading = false;
        // Also set chapterInfo for VideoForm compatibility
        state.chapterInfo = action.payload;
        state.chapterInfoLoading = false;
      })
      .addCase(fetchQuesCodeById.rejected, (state, action) => {
        state.qrCodeInfoLoading = false;
        state.chapterInfoLoading = false;
        state.error = action.payload as string;
      })

      // Preview video
      .addCase(previewQuesCodeVideo.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(previewQuesCodeVideo.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(previewQuesCodeVideo.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })

      // Delete QR code directly
      .addCase(deleteQrCode.pending, (state) => {
        state.deleteLoading = true;
        state.error = null;
      })
      .addCase(deleteQrCode.fulfilled, (state) => {
        state.deleteLoading = false;
      })
      .addCase(deleteQrCode.rejected, (state, action) => {
        state.deleteLoading = false;
        state.error = action.payload as string;
      })
      // Preview video cases
      .addCase(previewVideo.pending, (state) => {
        state.previewVideoLoading = true;
        state.error = null;
      })
      .addCase(previewVideo.fulfilled, (state, action) => {
        state.previewVideoLoading = false;
        state.previewVideoInfo = action.payload;
      })
      .addCase(previewVideo.rejected, (state, action) => {
        state.previewVideoLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearState } = quesCodeSlice.actions;

export const selectQuesCodeState = (state: RootState) => state.resourceManageQuesCode;

export default quesCodeSlice.reducer;
