import { IAddPostBo, IEditPostBo, IGetPostListBo } from 'types/system';
import request from 'utils/request/index';

/**
 * 查询岗位列表
 * @returns
 */
export const getSystemPostListApi = async (params: IGetPostListBo) => {
  const result = await request.get({
    url: '/system/post/list',
    params,
  });
  return result;
};

/**
 * 删除岗位
 * @param postId
 * @returns
 */
export const delSystemPostApi = async (postId: number | string) => {
  const result = await request.delete({
    url: `/system/post/${postId}`,
  });
  return result;
};

export const addSystemPostApi = async (params: IAddPostBo) => {
  const result = await request.post({
    url: `/system/post`,
    params,
  });
  return result;
};

export const editSystemPostApi = async (params: IEditPostBo) => {
  const result = await request.put({
    url: `/system/post`,
    params,
  });
  return result;
};

/**
 * 获取岗位详细信息
 * @param postId
 * @returns
 */
export const getSystemPostDetailsInfoApi = async (postId: number) => {
  const result = await request.get({
    url: `/system/post/${postId}`,
  });
  return result;
};
