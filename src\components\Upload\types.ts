export type themeType = 'custom' | 'file-input' | 'file-flow' | 'image' | 'image-flow' | undefined;

export interface IUploads {
  /** 上传地址 */
  url?: string;

  /** 请求其他参数 */
  params?: object;

  /** 组件下方文本提示，可以使用 status 定义文本 */
  tips?: string;

  /** 已上传文件列表，同 value。TS 类型：UploadFile */
  files?: object[];

  /** 上传成功回调 Function(res: UploadFile) */
  success?: Function;

  /** 删除文件回调 */
  remove?: Function;

  /** 组件风格。custom 表示完全自定义风格；file 表示默认文件上传风格；file-input 表示输入框形式的文件上传；file-flow 表示文件批量上传；image 表示默认图片上传风格；image-flow 表示图片批量上传 */
  theme?: themeType;

  /** 最大上传数量 */
  max?: number;

  /** 是否关闭拖拽上传 */
  draggable?: boolean;

  /** 文件大小限制 */
  maxFileSize?: number;

  /** 文件格式限制  (单位：MB) */
  accept?: string | undefined;
  /** 文件单位 （kb） */
  fileUnit?: string | undefined;

  disabled?: boolean;
}
