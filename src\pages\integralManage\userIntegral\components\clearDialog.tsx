import React, { useState, useEffect, useRef } from 'react';
import {
  Button,
  Dialog,
  Form,
  Input,
  MessagePlugin,
  type FormInstanceFunctions,
  SubmitContext,
  TableRowData,
} from 'tdesign-react';
import { LockOnIcon, MailIcon, BrowseOffIcon, BrowseIcon } from 'tdesign-icons-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import classnames from 'classnames';
import {
  selectPointsManagement,
  getIntegralList,
  clearIntegralCaptchaImage,
  clearIntegral,
} from 'modules/pointsManagement';
import Style from '../index.module.less';

const { FormItem } = Form;

interface IClearDialogProps {
  typeProp: string;
  visibleProp: boolean;
  rowProp?: TableRowData;
  onCancel?: (val: boolean) => void;
  onSuccess?: (val: boolean) => void;
}

export const ClearDialog = ({ visibleProp, rowProp, typeProp, onCancel }: IClearDialogProps) => {
  const dispatch = useAppDispatch();

  const { pageNum, pageSize, imgLoading, formLoading } = useAppSelector(selectPointsManagement);
  const formRef = useRef<FormInstanceFunctions>();
  const [visible, setVisible] = useState<boolean>(false);
  const [showPsw, toggleShowPsw] = useState(false);
  const [imageSrc, setImageSrc] = useState('');
  const [uuid, setUuid] = useState('');
  const [row, setRow] = useState<TableRowData>();
  const [type, setType] = useState('');

  const getImgCode = async () => {
    setVisible(true);
    const imgCode = await dispatch(clearIntegralCaptchaImage(''));
    setImageSrc(`data:image/gif;base64,${imgCode.payload.img}`);
    setUuid(imgCode.payload.uuid);
  };

  useEffect(() => {
    getImgCode();
    setVisible(visibleProp);
    setRow(rowProp);
    setType(typeProp);
  }, [visibleProp, rowProp, typeProp]);

  const cancelBtn = () => {
    setVisible(false);
    if (onCancel) onCancel(false);
  };

  const onSubmit = async (e: SubmitContext) => {
    if (e.validateResult === true) {
      try {
        const formValue = formRef.current?.getFieldsValue?.(true) || {};
        formValue.uuid = uuid;
        await dispatch(
          clearIntegral({
            ...formValue,
            uuid,
            studentIdList: type === 'all' ? undefined : [row?.studentId],
            integralIdList: type === 'all' ? undefined : [row?.integralId],
          }),
        ).unwrap();
        cancelBtn();
        await dispatch(
          getIntegralList({
            pageSize,
            pageNum,
          }),
        );
        MessagePlugin.success(`清空成功！`);
      } catch (e) {
        getImgCode();
      }
    }
  };

  return (
    <>
      {visible && (
        <Dialog
          confirmLoading={formLoading}
          header='是否确认清空积分？请谨慎操作'
          visible={visible}
          confirmBtn={'确定'}
          onConfirm={() => formRef.current?.submit?.()}
          cancelBtn={'取消'}
          onCancel={cancelBtn}
          onCloseBtnClick={cancelBtn}
          width={500}
        >
          <Form ref={formRef} className={classnames(Style.itemContainer)} labelWidth={0} onSubmit={onSubmit}>
            <FormItem name='password' label='密码' rules={[{ required: true, message: '密码必填', type: 'error' }]}>
              <Input
                size='large'
                type={showPsw ? 'text' : 'password'}
                placeholder='请输入登录密码'
                prefixIcon={<LockOnIcon />}
                suffixIcon={
                  showPsw ? (
                    <BrowseIcon onClick={() => toggleShowPsw((current) => !current)} />
                  ) : (
                    <BrowseOffIcon onClick={() => toggleShowPsw((current) => !current)} />
                  )
                }
              />
            </FormItem>
            <FormItem
              name='codeValue'
              label='验证码'
              rules={[{ required: true, message: '验证码必填', type: 'error' }]}
            >
              <Input size='large' prefixIcon={<MailIcon />} placeholder='请输入验证码' />
              <Button
                loading={imgLoading}
                style={{ padding: '0px !important' }}
                variant='outline'
                className={Style.verificationBtn}
                onClick={getImgCode}
              >
                <img src={imageSrc} alt='' />
              </Button>
            </FormItem>
          </Form>
        </Dialog>
      )}
    </>
  );
};
