import React, { memo, useEffect, useRef, useState } from 'react';
import { Button, Dialog, Loading, Popconfirm, Popup, PopupProps, Row, Space, TableProps } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  selectSRRankingMajors,
  getHotList,
  deleteHot,
  dragSortHot,
  getInstitutionList,
  clearInstitutionList,
} from 'modules/srRanking/majors';
import { IRef } from 'components/Form';
import { IGetHotListBo, IGetInstitutionListBo } from 'types/srRanking';
import PermissionButton from 'components/PermissionButton';
import { Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { strIsNull } from 'utils/tool';
import { MoveIcon } from 'tdesign-icons-react';
import { IDragSort } from 'types';
import EditAddForm, { IEditAddFormRef } from './EditAddForm';

const SRRankingMajorsHotTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const {
    loading,
    list,
    pageNum,
    pageSize,
    total,
    active,
    deleteHotLoading,
    addEditHotLoading,
    institutionListLoading,
    institutionList,
  } = useAppSelector(selectSRRankingMajors);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetHotListBo = { pageNum, pageSize, examId: active };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'drag',
    'sort',
    'majorsName',
    'enrollmentNumber',
    'institutionNumber',
    'avgSalary',
    'levelName',
    'introduce',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['createTime', 'createBy', 'updateTime', 'updateBy'];
  const [title, setTitle] = useState('');
  const [visible, setVisible] = useState(false);
  const [hotId, setHotId] = useState<number | null>(null);
  const editAddFormRef = useRef<IEditAddFormRef | null>(null);

  const renderInstitutionContent = () => (
    <Loading loading={institutionListLoading}>
      {institutionList && (
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)',
            gap: '10px',
            maxWidth: 'calc(4 * (200px + 10px))',
            width: '100%',
            margin: '0 auto',
          }}
        >
          {institutionList.map((item) => (
            <div key={item.id}>{item.educationalName}</div>
          ))}
        </div>
      )}
    </Loading>
  );

  /**
   * 删除热门推荐
   */
  const handleDelete = async (id: number) => {
    const { type } = await dispatch(deleteHot(id));

    if (type.endsWith('fulfilled')) {
      dispatch(getHotList(searchParams));
    }
  };

  const handleConfirm = async () => {
    try {
      const data = await editAddFormRef.current?.submit();

      if (data) {
        dispatch(getHotList(searchParams));
      }

      setHotId(null);
      setVisible(false);
    } catch (e: any) {
      console.log(e);
    }
  };

  const handleClose = () => {
    editAddFormRef.current?.reset();
    setVisible(false);
  };

  const handleAdd = () => {
    setHotId(null);
    setTitle('添加热门专业');
    setVisible(true);
  };

  const handleEdit = (id: number) => {
    setHotId(id);
    setTitle('编辑热门专业');
    setVisible(true);
  };

  const handleInstitutionNumberClick = (majorsId: number) => {
    const bo: IGetInstitutionListBo = {
      examId: active,
      majorsId,
    };

    dispatch(getInstitutionList(bo));
  };

  const handleVisibleChange: PopupProps['onVisibleChange'] = (visible) => {
    if (!visible) {
      clearInstitutionList();
    }
  };

  const onDragSort: TableProps['onDragSort'] = async ({ current, targetIndex }) => {
    try {
      const bo: IDragSort = {
        id: current.id,
        parentId: active,
        position: pageNum === 1 ? targetIndex + 1 : (pageNum - 1) * pageSize + (targetIndex + 1),
      };

      const data = await dispatch(dragSortHot(bo)).unwrap();

      if (data) {
        dispatch(getHotList(searchParams));
      }
    } catch (error) {
      throw new Error('排序失败');
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'drag',
      title: '排序',
      cell: () => <MoveIcon />,
      width: 50,
    },
    {
      colKey: 'sort',
      title: '排名',
      width: 120,
    },
    {
      colKey: 'majorsName',
      title: '专业名称',
      width: 260,
    },
    {
      colKey: 'enrollmentNumber',
      title: '招生人数',
      width: 120,
    },
    {
      colKey: 'institutionNumber',
      title: '招生院校',
      width: 120,
      cell: ({ row }) =>
        row.institutionNumber ? (
          <Popup trigger='click' showArrow content={renderInstitutionContent()} onVisibleChange={handleVisibleChange}>
            <Button onClick={() => handleInstitutionNumberClick(row.majorsId)} theme='primary' variant='text'>
              {row.institutionNumber}
            </Button>
          </Popup>
        ) : (
          ''
        ),
    },
    {
      colKey: 'avgSalary',
      title: '平均薪资',
      width: 120,
    },
    {
      colKey: 'levelName',
      title: '层次',
      width: 120,
      cell: ({ row }) => <span>{row.levelName === '1' ? '专科' : '本科'}</span>,
    },
    {
      colKey: 'introduce',
      title: '推荐理由',
    },
    {
      colKey: 'updateTime',
      title: '修改时间',
    },
    {
      colKey: 'updateBy',
      title: '修改人',
    },
    {
      colKey: 'createTime',
      title: '创建时间',
    },
    {
      colKey: 'createBy',
      title: '创建人',
    },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      cell({ row }) {
        return (
          <>
            <PermissionButton permissions={['srRanking:majors:edit']} onClick={() => handleEdit(row.id)}>
              修改
            </PermissionButton>
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleDelete(row.id)}>
              <>
                <PermissionButton permissions={['srRanking:majors:remove']} theme='danger' loading={deleteHotLoading}>
                  删除
                </PermissionButton>
              </>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    if (!strIsNull(active)) {
      dispatch(getHotList(searchParams));
    }
  }, [active]);

  return (
    <div style={{ paddingTop: 'var(--td-comp-paddingTB-l)' }}>
      <Search
        ref={searchRef}
        method={getHotList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '专业名称',
            field: 'majorsName',
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            permissions={['srRanking:majors:add']}
            content='添加热门推荐'
            variant='outline'
            onClick={handleAdd}
          />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'id',
          dragSort: 'row-handler',
          onDragSort,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getHotList}
        params={searchParams}
      />

      <Dialog
        width='800px'
        header={title}
        visible={visible}
        closeOnEscKeydown={false}
        closeOnOverlayClick={false}
        confirmOnEnter={true}
        destroyOnClose={true}
        preventScrollThrough={true}
        confirmBtn={{ content: '确认', loading: addEditHotLoading }}
        cancelBtn={{ content: '取消', onClick: handleClose }}
        onCancel={handleClose}
        onConfirm={handleConfirm}
        onClose={handleClose}
      >
        {visible && <EditAddForm ref={editAddFormRef} hotId={hotId} examId={active} />}
      </Dialog>
    </div>
  );
};

export default memo(SRRankingMajorsHotTable);
