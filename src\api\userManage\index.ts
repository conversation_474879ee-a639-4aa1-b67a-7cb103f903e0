import request from 'utils/request/index';
import {
  IGetStudentExamDetailApi,
  IGetStudentInfoListApi,
  IGetStudentPracticeList,
  IGetStudentCollectionListApi,
  IGetStudentExamListApi,
  IGetStudentWrongListApi,
  IGetStudentFollowMajorsListApi,
  IGetStudentFollowEducationalListApi,
  IGetStudentWatchCourseProgressListApi,
  IGetStudentLoginLogListApi,
} from './type';

/**
 * 查询考试记录详情
 * @param params
 * @returns
 */
export const getStudentExamDetailApi = async (params: IGetStudentExamDetailApi) => {
  const result = await request.get({
    url: '/studentQuestion/getStudentExamDetail',
    params,
  });
  return result;
};

/**
 * 根据id查询学生相关信息
 * @param id
 * @returns
 */
export const getStudentInfoByIdApi = async (id: number | string) => {
  const result = await request.get({
    url: `/studentInfo/getStudentInfoById/${id}`,
  });
  return result;
};

/**
 * 查询用户基本信息列表
 * @param params
 * @returns
 */
export const getStudentInfoListApi = async (params: IGetStudentInfoListApi) => {
  const result = await request.get({
    url: '/studentInfo/getStudentInfoList',
    params,
  });
  return result;
};

/**
 * 查询学生练习记录
 * @param params
 * @returns
 */
export const getStudentPracticeListApi = async (params: IGetStudentPracticeList) => {
  const result = await request.get({
    url: '/studentQuestion/getStudentPracticeList',
    params,
  });
  return result;
};

/**
 * 查询学生考试记录
 * @param params
 * @returns
 */
export const getStudentExamListApi = async (params: IGetStudentExamListApi) => {
  const result = await request.get({
    url: '/studentQuestion/getStudentExamList',
    params,
  });
  return result;
};

/**
 * 查询学生收藏的题目
 * @param params
 * @returns
 */
export const getStudentCollectionListApi = async (params: IGetStudentCollectionListApi) => {
  const result = await request.get({
    url: '/studentQuestion/getStudentCollectionList',
    params,
  });
  return result;
};

/**
 * 错题本列表
 * @param params
 * @returns
 */
export const getStudentWrongListApi = async (params: IGetStudentWrongListApi) => {
  const result = await request.get({
    url: '/studentQuestion/getStudentWrongList',
    params,
  });
  return result;
};

/**
 * 查询学生关注的专业
 * @param params
 * @returns
 */
export const getStudentFollowMajorsListApi = async (params: IGetStudentFollowMajorsListApi) => {
  const result = await request.get({
    url: '/studentInfo/getStudentFollowMajorsList',
    params,
  });
  return result;
};

export const getStudentFollowEducationalListApi = async (params: IGetStudentFollowEducationalListApi) => {
  const result = await request.get({
    url: '/studentInfo/getStudentFollowEducationalList',
    params,
  });
  return result;
};

export const getStudentWatchCourseProgressListApi = async (data: IGetStudentWatchCourseProgressListApi) => {
  const result = await request.post({
    url: '/admStudentProgress/getStuCourseProgress',
    data,
  });
  return result;
};

export const getStudentLoginLogListApi = async (params: IGetStudentLoginLogListApi) => {
  const result = await request.get({
    url: '/monitor/logininfor/queryStuLog',
    params,
  });
  return result;
};
