import React from 'react';
import { Input as InputTD, Form, Textarea as TextareaTD } from 'tdesign-react';

interface IInput {
  label: string;
  type?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (e: React.FocusEvent<HTMLInputElement>) => void;
  onInput?: (e: React.FormEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  clearable?: boolean;
  maxLength?: number;
  className?: string;
  style?: React.CSSProperties;
  name?: string;
  placeholder?: string;
  initialData?: any;
  rules?: any[];
  maxlength?: number;
}

/** 去除空格输入框 */
export const Input = (props: IInput) => {
  const handleInput = (e: React.InvalidEvent<HTMLInputElement>) => {
    const inputValue = { value: e.target.value.replace(/\s+/g, '') }; // 移除所有空格
    if (props.maxlength && inputValue.value.length > props.maxlength) {
      inputValue.value = inputValue.value.slice(0, 10); // 确保长度不超过 10
      return inputValue;
    }
    e.target.value = inputValue.value;
    return inputValue;
  };

  return (
    <Form.FormItem label={props.label} name={props.name} initialData={props.initialData} rules={props.rules}>
      <InputTD
        onInput={handleInput}
        clearable={props.clearable}
        maxlength={props.maxlength}
        showLimitNumber={typeof props.maxlength === 'number'}
        placeholder={props.placeholder ?? `请输入${props.label}`}
      />
    </Form.FormItem>
  );
};

/** 去除空格输入框 */
export const Textarea = (props: IInput) => {
  const handleInput = (e: any) => {
    if (e.target) {
      const inputValue = { value: e.target.value.replace(/\s+/g, '') };
      if (props.maxlength && inputValue.value.length > props.maxlength) {
        inputValue.value = inputValue.value.slice(0, 10);
        return inputValue.value;
      }
      e.target.value = inputValue.value;
      return inputValue.value;
    }
    return e.target.value;
  };

  return (
    <Form.FormItem label={props.label} name={props.name} initialData={props.initialData} rules={props.rules}>
      <TextareaTD
        autosize={true}
        onInput={() => {
          // eslint-disable-next-line no-restricted-globals
          handleInput(event);
        }}
        maxlength={props.maxlength}
        placeholder={props.placeholder ?? `请输入${props.label}`}
      />
    </Form.FormItem>
  );
};
