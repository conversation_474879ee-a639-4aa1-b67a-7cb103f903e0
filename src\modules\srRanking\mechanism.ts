import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  addMechanismApi,
  deleteMechanism<PERSON>pi,
  dragSortMechanismApi,
  editMechanism<PERSON>pi,
  getMechanismInfoApi,
  getMechanism<PERSON>istApi,
} from 'api';
import { IAddMechanismBo, IEditMechanismBo, IGetMechanismListBo } from 'types/srRanking';
import { IDragSort } from 'types';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'srRanking/mechanism';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  mechanismInfoLoading: boolean;
  mechanismInfo: any;
  deleteMechanismLoading: boolean;
  addEditMechanismLoading: boolean;
}

const initialState: IInitialState = {
  loading: false,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  mechanismInfoLoading: false,
  mechanismInfo: {},
  deleteMechanismLoading: false,
  addEditMechanismLoading: false,
};

export const getMechanismList = createAsyncThunk(`${namespace}/getMechanismList`, async (bo: IGetMechanismListBo) => {
  const { data } = await getMechanismListApi(bo);

  return {
    list: data.rows,
    total: data.total,
    pageNum: bo.pageNum,
    pageSize: bo.pageSize,
  };
});

export const getMechanismInfo = createAsyncThunk(`${namespace}/getMechanismInfo`, async (id: number) => {
  const { data } = await getMechanismInfoApi(id);

  return data;
});

export const dragSortMechanism = createAsyncThunk(`${namespace}/dragSortMechanism`, async (bo: IDragSort) => {
  try {
    const { code, data } = await dragSortMechanismApi(bo);

    if (code === 200) {
      return data;
    }
  } catch (_) {
    return false;
  }
});

export const addMechanism = createAsyncThunk(`${namespace}/addMechanism`, async (bo: IAddMechanismBo) => {
  await addMechanismApi(bo);
});

export const editMechanism = createAsyncThunk(`${namespace}/editMechanism`, async (bo: IEditMechanismBo) => {
  await editMechanismApi(bo);
});

export const deleteMechanism = createAsyncThunk(`${namespace}/deleteMechanism`, async (id: number) => {
  await deleteMechanismApi(id);
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getMechanismList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getMechanismList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getMechanismList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getMechanismInfo.pending, (state) => {
        state.mechanismInfoLoading = true;
      })
      .addCase(getMechanismInfo.fulfilled, (state, action) => {
        state.mechanismInfoLoading = false;
        state.mechanismInfo = action.payload;
      })
      .addCase(getMechanismInfo.rejected, (state) => {
        state.mechanismInfoLoading = false;
      })

      .addCase(dragSortMechanism.pending, (state) => {
        state.loading = true;
      })
      .addCase(dragSortMechanism.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(dragSortMechanism.rejected, (state) => {
        state.loading = false;
      })

      .addCase(addMechanism.pending, (state) => {
        state.addEditMechanismLoading = true;
      })
      .addCase(addMechanism.fulfilled, (state) => {
        state.addEditMechanismLoading = false;

        MessagePlugin.success('添加机构成功');
      })
      .addCase(addMechanism.rejected, (state) => {
        state.addEditMechanismLoading = false;
      })

      .addCase(editMechanism.pending, (state) => {
        state.addEditMechanismLoading = true;
      })
      .addCase(editMechanism.fulfilled, (state) => {
        state.addEditMechanismLoading = false;

        MessagePlugin.success('编辑机构成功');
      })
      .addCase(editMechanism.rejected, (state) => {
        state.addEditMechanismLoading = false;
      })

      .addCase(deleteMechanism.pending, (state) => {
        state.deleteMechanismLoading = true;
      })
      .addCase(deleteMechanism.fulfilled, (state) => {
        state.deleteMechanismLoading = false;
      })
      .addCase(deleteMechanism.rejected, (state) => {
        state.deleteMechanismLoading = false;
      });
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectSRRankingMechanism = (state: RootState) => state.srRankingMechanism;

export default listBaseSlice.reducer;
