import { RootState } from '../store';
import {
  publicGetExamListApi,
  getExamQuestionAdminListApi,
  deleteExamQuestionApi,
  addOrUpdateExamQuestionApi,
  examQuestionDragSortApi, openOrCloseExamQuestionTopApi
} from 'api';
import type {
  IGetExamQuestionAdminListApi,
  IDeleteExamQuestionApi,
  IAddOrUpdateExamQuestionApi,
  IExamQuestionDragSortApi, IExamQuestionApi
} from 'api';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'examFrequentlyAsked';

const initialState = {
  loading: true,
  formLoading: false,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  active: undefined as number | undefined,
  broadsideList: [] as Array<any>,
  tablist: [] as Array<any>,
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getEducationalInstitutionsNameAndId = useAsyncThunkWithStatus(
  `getEducationalInstitutionsNameAndId`,
  async (_, { getState }) => {
    const {
      data: { rows },
    } = await publicGetExamListApi();
    const state = getState() as RootState;
    const currentActive = state.examFrequentlyAsked.active;
    if (rows && rows.length > 0 && !currentActive)
      return {
        rows,
        active: rows[0].examId,
      };
    return { rows };
  },
);

export const getExamQuestionAdminList = useAsyncThunkWithStatus('getExamQuestionAdminList', async (params) => {
  const {
    data: { rows, total },
  } = await getExamQuestionAdminListApi(params as IGetExamQuestionAdminListApi);

  return {
    list: rows ?? [],
    total,
    pageNum: params.pageNum,
    pageSize: params.pageSize,
  };
});

export const examQuestionDragSort = useAsyncThunkWithStatus(
  'examQuestionDragSort',
  async (params, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { pageNum, pageSize, active } = state.examFrequentlyAsked;
    await examQuestionDragSortApi(params as IExamQuestionDragSortApi);
    await dispatch(
      getExamQuestionAdminList({
        pageNum,
        pageSize,
        examId: active,
      }),
    );
  },
);

export const addOrUpdateExamQuestion = useAsyncThunkWithStatus('addOrUpdateExamQuestion', async (params) => {
  await addOrUpdateExamQuestionApi(params as IAddOrUpdateExamQuestionApi);
});

export const deleteExamQuestion = useAsyncThunkWithStatus(
  'deleteExamQuestion',
  async (params, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { pageNum, pageSize, active } = state.examFrequentlyAsked;
    await deleteExamQuestionApi(params as IDeleteExamQuestionApi);
    await MessagePlugin.success(`删除成功`);
    await dispatch(
      getExamQuestionAdminList({
        pageNum,
        pageSize,
        examId: active,
      }),
    );
  },
);
export const openOrCloseExamQuestionTop = useAsyncThunkWithStatus(
  `openOrCloseExamQuestionTop`,
  async (params, { dispatch, getState }) => {
    const state = getState() as RootState;
    await openOrCloseExamQuestionTopApi(params as IExamQuestionApi);
    const { pageNum, pageSize, active } = state.examFrequentlyAsked;
    await dispatch(
      getExamQuestionAdminList({
        pageNum,
        pageSize,
        examId: active,
      }),
    );
  },
);
const directorySlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setActive: (state, action) => {
      state.active = action.payload;
    },
  },
  cases: [
    {
      thunk: getEducationalInstitutionsNameAndId,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.broadsideList = action.payload.rows;
        state.active = action.payload.active;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getExamQuestionAdminList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.tablist = action.payload?.list;
        state.pageNum = action.payload?.pageNum;
        state.pageSize = action.payload?.pageSize;
        state.total = action.payload?.total;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: addOrUpdateExamQuestion,
      pending: (state) => {
        state.formLoading = true;
      },
      fulfilled: (state) => {
        state.formLoading = false;
      },
      rejected: (state) => {
        state.formLoading = false;
      },
    },
  ],
});

export const { clearPageState, setActive } = directorySlice.actions;

export const selectExamFrequentlyAsked = (state: RootState) => state.examFrequentlyAsked;

export default directorySlice.reducer;
