.t-list-item__meta-description {
  margin-right: 0 !important;
}

.content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-14) !important;
  color: var(--text-color-secondary) !important;
}

.app {
  width: 370px;
  height: 730.8px;
  background-image: url('assets/image/iPhone13.png');
  top: 0;
  left: 0;
  background-size: 100% 100%;
  position: relative;
  padding-top: 7px;
  border-radius: 30px;
  :global {
    .t-swiper__content {
      border-radius: 0 0 30px 0;
    }
  }
  .swiper {
    background-color: var(--td-bg-color-container);
    width: 345px;
    margin: 0 auto;
    border-radius: 42px 42px 0 0;
    position: relative;
    overflow: hidden;
    .SwiperItem {
      height: 200px;
      img {
        width: 100%;
        height: 100%;
        // border-radius: 42px 42px 42px 0;
      }
    }
  }
  .examItem {
    text-align: center;
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    img {
      width: 65px;
    }
    p {
      font-weight: bold;
      font-size: 14px;
    }
  }
  .askedQuestion {
    display: flex;
    background-color: var(--td-bg-color-secondarycomponent);
    width: 137px;
    padding: 10px 0 10px 10px;
    border-radius: 18px;
    :first-child {
      font-size: 17px;
    }
    :last-child {
      font-size: 12px;
    }
  }

  .communicationGroup {
    display: flex;
    background-color: var(--td-brand-color);
    color: #ffffff;
    width: 137px;
    padding: 10px 0 10px 10px;
    border-radius: 18px;
    :first-child {
      font-size: 17px;
    }
    :last-child {
      font-size: 12px;
    }
  }

  .title {
    font-size: 17px;
    font-weight: bold;
    margin: 20px 0;
  }

  .release-information {
    display: flex;
    justify-content: space-between;

    &-item {
      width: 96px;
      height: 150px;
      border-radius: 10px;
      position: relative;
      div {
        height: 150px;
        :last-child {
          position: absolute;
          width: 22px;
          height: 22px;
          left: 6px;
          top: 6px;
            img {
            width: 100%;
            height: 100%;
            image-rendering: pixelated;
          }
        }
      }
      img {
        border-radius: 10px;
        width: 100%;
        height: 100%;
      }
    }
  }
  .tabBar {
    height: 65px;
    width: 303px;
    border-top: 1px solid #e5e5e5;
    display: flex;
    justify-content: space-between;
    position: absolute;
    bottom: -48px;
    padding: 0 21px;
    padding-top: 10px;
    background-color: var(--td-bg-color-container);
    text-align: center;
    margin: 0 0px;
    border-radius: 0 0 20px 20px;
  }
}
