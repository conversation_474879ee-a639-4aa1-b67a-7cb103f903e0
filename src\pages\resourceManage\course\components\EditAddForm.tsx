import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { Form, Input, Loading, type FormInstanceFunctions, FormProps, InputNumber, Switch } from 'tdesign-react';
import {
  selectResourceManageCourse,
  getChapterInfo,
  addResourceCourseChapter,
  editResourceCourseChapter,
} from 'modules/resourceManage/course';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { strIsNull } from 'utils/tool';
import { IAddResourceCourseChapterBo, IEditResourceCourseChapterBo } from 'types/resourceManage';
import { Icon } from 'tdesign-icons-react';

const { FormItem } = Form;

interface EditAddFormProps {
  type: string;
  chapterId: number | null;
  courseId: number;
}

export interface IEditAddFormRef {
  submit: () => Promise<void>;
  reset: () => void;
}

const ChapterEditAddForm = forwardRef<IEditAddFormRef, EditAddFormProps>(({ type, chapterId, courseId }, ref) => {
  const dispatch = useAppDispatch();
  const { chapterInfoLoading, chapterInfo } = useAppSelector(selectResourceManageCourse);
  const formRef = useRef<FormInstanceFunctions>();
  const [form] = Form.useForm();

  const INITIAL_DATA = {
    chapterName: '',
    orderNum: '',
    tryAndSee: 0,
  };

  const rules: FormProps['rules'] = {
    chapterName: [
      {
        required: true,
        message: `章节名称不能为空`,
        trigger: 'all',
      },
    ],
    orderNum: [
      {
        required: true,
        message: `排序不能为空`,
        trigger: 'all',
      },
    ],
    tryAndSee: [
      {
        required: true,
        message: `是否试看不能为空`,
        trigger: 'all',
      },
    ],
  };

  useEffect(() => {
    if (type === 'add') {
      formRef.current?.setFieldsValue(INITIAL_DATA);
    } else {
      dispatch(getChapterInfo(chapterId as number));
    }
  }, [type]);

  useEffect(() => {
    if (!strIsNull(chapterInfo) && type === 'edit') {
      formRef.current?.setFieldsValue(chapterInfo);
    }
  }, [chapterInfo]);

  useImperativeHandle(ref, () => ({
    submit: () =>
      // eslint-disable-next-line no-async-promise-executor
      new Promise<void>(async (resolve, reject) => {
        const valid = await formRef.current?.validate();

        if (valid === true) {
          if (type === 'add') {
            const form: IAddResourceCourseChapterBo = JSON.parse(JSON.stringify(formRef.current?.getFieldsValue(true)));

            form.courseId = courseId;

            form.parentId = chapterId as number;

            await dispatch(addResourceCourseChapter(form));
          } else {
            const form: IEditResourceCourseChapterBo = JSON.parse(
              JSON.stringify(formRef.current?.getFieldsValue(true)),
            );

            form.id = chapterId as number;

            await dispatch(editResourceCourseChapter(form));
          }

          resolve();
        } else {
          reject(valid);
        }
      }),
    reset: () => {
      formRef.current?.reset();
    },
  }));

  const renderActiveContent = () => <Icon name='check' />;
  const renderInactiveContent = () => <Icon name='close' />;

  return (
    <Loading loading={chapterInfoLoading}>
      <Form ref={formRef} form={form} initialData={INITIAL_DATA} resetType='empty' rules={rules} colon>
        <FormItem label='章节名称' name='chapterName'>
          <Input clearable placeholder='请输入章节名称' />
        </FormItem>
        <FormItem label='排序' name='orderNum'>
          <InputNumber placeholder='请输入排序' theme='normal' min={0} decimalPlaces={0} allowInputOverLimit={false} />
        </FormItem>
        <FormItem label='是否试看' name='tryAndSee'>
          <Switch customValue={[1, 0]} label={[renderActiveContent(), renderInactiveContent()]} />
        </FormItem>
      </Form>
    </Loading>
  );
});

export default ChapterEditAddForm;
