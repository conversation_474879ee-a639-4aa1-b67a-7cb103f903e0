import { IAddResourceLecturerBo, IEditResourceLecturerBo, IGetResourceLecturerListBo } from 'types/resourceManage';
import request from 'utils/request/index';

/**
 * 获取课程列表
 * @param params
 * @returns
 */
export const getResourceLecturerListApi = (params: IGetResourceLecturerListBo) =>
  request.get({
    url: '/teacher/getList',
    params,
  });

/**
 * 获取讲师信息
 * @param id
 * @returns
 */
export const getResourceLecturerInfoApi = (id: number) =>
  request.get({
    url: `/teacher/getById/${id}`,
  });

/**
 * 添加讲师
 * @param data
 * @returns
 */
export const addResourceLecturerApi = (data: IAddResourceLecturerBo) =>
  request.post({
    url: '/teacher/save',
    data,
  });

/**
 * 编辑讲师
 * @param data
 * @returns
 */
export const editResourceLecturerApi = (data: IEditResourceLecturerBo) =>
  request.put({
    url: '/teacher/edit',
    data,
  });

/**
 * 删除讲师
 * @param id
 * @returns
 */
export const deleteResourceLecturerApi = (id: number) =>
  request.delete({
    url: `/teacher/delete/${id}`,
  });
