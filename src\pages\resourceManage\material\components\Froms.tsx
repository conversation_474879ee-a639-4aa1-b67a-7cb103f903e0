import React, { forwardRef, useRef } from 'react';
import {
  Form,
  Row,
  Col,
  Input,
  Button,
  MessagePlugin,
  Loading,
  InputAdornment,
  Space,
  Textarea,
  InputNumber,
  type FormProps,
} from 'tdesign-react';
import { SubmitContext, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectResourceManageQuestionSlice, addOrUpdateSubject } from 'modules/resourceManage/question';
import Style from './index.module.less';

const { FormItem } = Form;

export type FormValueType = {
  subjectId: number;
};

export type SearchFormProps = {
  onCancel: () => void;
  success: () => void;
  type: string;
  row: FormValueType;
};

const SearchForm = forwardRef<FormInstanceFunctions, SearchFormProps>(({ success, onCancel, type, row }) => {
  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>(null);
  const { formLoading } = useAppSelector(selectResourceManageQuestionSlice);

  const addForm = {
    subjectId: '',
    subjectName: '',
    freeQuestionCount: '',
    mockExamQuestionCount: '',
    mockExamDuration: '',
    notes: '',
  };

  const editForm = {
    freeQuestionCount: '',
    mockExamQuestionCount: '',
    mockExamDuration: '',
    notes: '',
    ...row,
  };

  const INITIAL_DATA = type === 'add' ? addForm : editForm;

  const onSubmits = async (e: SubmitContext) => {
    if (e && e.validateResult === true) {
      await dispatch(
        addOrUpdateSubject({
          ...formRef.current?.getFieldsValue?.(true),
          subjectId: type === 'add' ? undefined : row.subjectId,
        }),
      );
      success();
      MessagePlugin.success('修改成功');
    }
  };

  const onReset: FormProps['onReset'] = () => {
    onCancel();
  };

  const handleInput = (e) => {
    let inputValue = e.target.value.replace(/\s+/g, ''); // 移除所有空格
    if (inputValue.length > 10) {
      inputValue = inputValue.slice(0, 10); // 确保长度不超过 10
      return inputValue;
    }
    e.target.value = inputValue;
    return inputValue;
  };

  return (
    <Loading loading={formLoading} showOverlay>
      <Form className={Style.form} ref={formRef} onSubmit={onSubmits} labelAlign='top' onReset={onReset}>
        <Row gutter={[32, 24]}>
          <Col span={12}>
            <FormItem
              label='科目名称'
              name='subjectName'
              initialData={INITIAL_DATA.subjectName}
              rules={[{ required: true, message: '科目名称必填', type: 'error' }]}
            >
              <Input maxlength={10} onInput={handleInput} showLimitNumber placeholder='请输入科目名称' />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label='自由练习题目数'
              name='freeQuestionCount'
              initialData={INITIAL_DATA.freeQuestionCount}
              help='自由练习题目数为 1-50'
              rules={[{ required: true, message: '自由练习题目数必填', type: 'error' }]}
            >
              <InputNumber
                allowInputOverLimit={false}
                min={1}
                max={50}
                style={{ width: '100%' }}
                theme='column'
                placeholder='请输入自由练习题目数'
              />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label='模拟考试题目数'
              name='mockExamQuestionCount'
              initialData={INITIAL_DATA.mockExamQuestionCount}
              help='模拟考试题目数为 10-200'
              rules={[{ required: true, message: '模拟考试题目数必填' }]}
            >
              <InputNumber
                allowInputOverLimit={false}
                min={10}
                max={200}
                style={{ width: '100%' }}
                theme='column'
                placeholder='请输入模拟考试题目数'
              />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label='模拟考试时长'
              name='mockExamDuration'
              initialData={INITIAL_DATA.mockExamDuration}
              help='模拟考试时长为 30-240'
              rules={[{ required: true, message: '模拟考试时长必填', type: 'error' }]}
            >
              <InputAdornment style={{ width: '100%' }} append='分钟'>
                <InputNumber
                  allowInputOverLimit={false}
                  min={30}
                  max={240}
                  style={{ width: '100%' }}
                  theme='column'
                  placeholder='请输入模拟考试时长'
                />
              </InputAdornment>
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label='备注' name='notes' initialData={INITIAL_DATA.notes}>
              <Textarea placeholder='请输入备注' maxcharacter={150} />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem style={{ marginRight: 0 }}>
              <Space>
                <Button type='reset' theme='default'>
                  取消
                </Button>
                <Button type='submit' theme='primary'>
                  提交
                </Button>
              </Space>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Loading>
  );
});

export default SearchForm;
