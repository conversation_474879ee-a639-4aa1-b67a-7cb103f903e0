import React, { useState, useEffect, memo, useRef } from 'react';
import { Steps, Button, Space, Row, Col, MessagePlugin } from 'tdesign-react';
import { Step1, Step2, Step3 } from './step';
import Style from './RelatedMajors.module.less';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectAssociation, addEducationalMajors } from 'modules/directory/association';

const { StepItem } = Steps;

interface IRelatedMajorsProps {
  cancellation: () => void;
  onChangeCunt: (cunt: number) => void;
  onChangeSpeciality: (obj: object) => void;
  rowData: any;
  type: string;
}

interface IRow {
  examId: number | null;
  examName?: string | null;
  isRelated?: number | null;
  educationalMajorsId?: number | null;
  educationalMajorsExamId: number | null;
  enrollmentNumber: number | null;
  connectPreEducationMajor: string | null;
}

export const SelectTable: React.FC<IRelatedMajorsProps> = ({
  cancellation,
  onChangeCunt,
  onChangeSpeciality,
  rowData,
  type,
}) => {
  const { schoolSystem, tuition } = rowData;
  const dispatch = useAppDispatch();
  const [cunt, setCunt] = useState(0);
  const [speciality, setSpeciality] = useState({});
  const [specialityId, onSelectChange] = useState<(string | number)[]>([]);
  const [formData, setFormData] = useState({
    schoolSystem,
    tuition,
  });
  const step2Ref = useRef<any>(null);
  const step3Ref = useRef<any>(null);
  const { active } = useAppSelector(selectAssociation);

  const handleNextStep = async () => {
    if (type === 'add') {
      if (cunt === 0) {
        if (specialityId.length === 0) return MessagePlugin.error('请选择专业');

        onChangeSpeciality(speciality);
      } else if (cunt === 1) {
        if (step2Ref.current) {
          const valid = await step2Ref.current?.validateForm();

          if (!valid) return false;

          setFormData(valid);
        }
      }

      setCunt(cunt + 1);
    } else if (type === 'edit') {
      if (cunt === 0 && step2Ref.current) {
        const valid = await step2Ref.current?.validateForm();
        if (valid) {
          setFormData(valid);
          setCunt(cunt + 1);
        }
      } else {
        setCunt(cunt + 1);
      }
    }

    return false;
  };

  useEffect(() => {
    if (type === 'edit') {
      const { schoolSystem, tuition } = rowData;
      setFormData({
        schoolSystem,
        tuition,
      });
    }
  }, [rowData]);

  useEffect(() => {
    onChangeCunt(cunt);
  }, [cunt]);

  const handleStep3Submit = () => {
    if (step3Ref.current) {
      return step3Ref.current.submitForm();
    }

    return false;
  };

  const onSubmit = async () => {
    try {
      const allList = await handleStep3Submit();

      if (allList) {
        const { formData: row, list, currentExamId } = allList;

        const listData = list.map((item: IRow) => {
          if (item.examId === currentExamId) {
            return {
              ...item,
              enrollmentNumber: row.enrollmentNumber,
              connectPreEducationMajor: row.connectPreEducationMajor,
            };
          }
          return { ...item };
        });

        if (type === 'add') {
          const educationalMajorsExamInfoBoList = listData
            .filter((item: IRow) => item.enrollmentNumber)
            .map((item: IRow) => ({
              examId: item.examId,
              enrollmentNumber: item.enrollmentNumber,
              connectPreEducationMajor: item.connectPreEducationMajor,
            }));
          const params = {
            type: 'add',
            educationalId: active,
            majorsId: specialityId[0],
            ...formData,
            educationalMajorsExamInfoBoList,
          };
          await dispatch(addEducationalMajors(params));
        } else if (type === 'edit') {
          const educationalMajorsExamInfoBoList = listData
            .filter((item: IRow) => item.enrollmentNumber)
            .map((item: IRow) => ({
              educationalMajorsExamId: item.educationalMajorsExamId,
              enrollmentNumber: item.enrollmentNumber,
              connectPreEducationMajor: item.connectPreEducationMajor,
            }));
          const params = {
            type: 'edit',
            ...formData,
            educationalMajorsId: rowData.educationalMajorsId,
            educationalMajorsExamInfoBoList,
          };

          await dispatch(addEducationalMajors(params));
        }
        return cancellation();
      }
    } catch (e) {
      return false;
    }

    return false;
  };

  const handleSelectChange = (val: Array<any>) => {
    if (val.length > 0) {
      const obj = val[0];

      onSelectChange([obj.majorsId]);
      setSpeciality(obj);
    } else {
      onSelectChange([]);
      setSpeciality({});
    }
  };

  const addType = () => {
    switch (cunt) {
      case 0:
        return <Step1 onSelectChange={handleSelectChange} specialityId={specialityId} />;
      case 1:
        return <Step2 ref={step2Ref} formData={formData} />;
      case 2:
        return <Step3 ref={step3Ref} rowData={rowData} type={type} />;
      default:
        return '';
    }
  };

  const edit = () => {
    switch (cunt) {
      case 0:
        return <Step2 ref={step2Ref} formData={formData} />;
      case 1:
        return <Step3 ref={step3Ref} rowData={rowData} type={type} />;
      default:
        return '';
    }
  };

  return (
    <>
      <Steps theme='dot' current={cunt}>
        {type === 'add' && <StepItem title='选择专业' />}
        <StepItem title='编辑信息' />
        <StepItem title='关联考试' />
      </Steps>
      {type === 'add' ? addType() : edit()}
      <Row className={Style.buttonRow}>
        <Col span={24} className={Style.buttonCol}>
          {type === 'add' && (
            <Space>
              <Button theme='default' variant='base' onClick={() => cancellation()}>
                取消
              </Button>
              {cunt !== 0 && (
                <Button theme='primary' variant='base' onClick={() => setCunt(cunt - 1)}>
                  上一步
                </Button>
              )}
              {cunt !== 2 && (
                <Button theme='primary' variant='base' onClick={handleNextStep}>
                  下一步
                </Button>
              )}
              {cunt === 2 && (
                <Button theme='primary' variant='base' onClick={onSubmit}>
                  确定
                </Button>
              )}
            </Space>
          )}
          {type === 'edit' && (
            <Space>
              <Button theme='default' variant='base' onClick={() => cancellation()}>
                取消
              </Button>
              {cunt !== 0 && (
                <Button theme='primary' variant='base' onClick={() => setCunt(cunt - 1)}>
                  上一步
                </Button>
              )}
              {cunt !== 1 && (
                <Button theme='primary' variant='base' onClick={handleNextStep}>
                  下一步
                </Button>
              )}
              {cunt === 1 && (
                <Button theme='primary' variant='base' onClick={onSubmit}>
                  确定
                </Button>
              )}
            </Space>
          )}
        </Col>
      </Row>
    </>
  );
};

export default memo(SelectTable);
