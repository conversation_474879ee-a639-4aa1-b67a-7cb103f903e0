import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { Form, Loading, type FormInstanceFunctions, FormProps, Select, Textarea } from 'tdesign-react';
import { selectSRRankingMajors, getMajorsList, getHotInfo, addHot, editHot } from 'modules/srRanking/majors';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { IAddHotBo, IEditHotBo } from 'types/srRanking';
import { strIsNull } from 'utils/tool';

const { FormItem } = Form;
const { Option } = Select;

interface EditAddFormProps {
  hotId: number | null;
  examId: number;
}

export interface IEditAddFormRef {
  submit: () => Promise<void>;
  reset: () => void;
}

const EditAddForm = forwardRef<IEditAddFormRef, EditAddFormProps>(({ hotId, examId }, ref) => {
  const dispatch = useAppDispatch();
  const { majorsList, hotInfoLoading, hotInfo } = useAppSelector(selectSRRankingMajors);
  const formRef = useRef<FormInstanceFunctions>();
  const [form] = Form.useForm();

  const INITIAL_DATA = {
    examId,
    majorsId: '',
    introduce: '',
  };

  const rules: FormProps['rules'] = {
    majorsId: [
      {
        required: true,
        message: '专业不能为空',
        trigger: 'all',
      },
    ],
  };

  useEffect(() => {
    if (examId && hotId === null) {
      dispatch(getMajorsList(examId));
    }
  }, [examId, hotId]);

  useEffect(() => {
    if (hotId !== null) {
      dispatch(getHotInfo(hotId));
    } else {
      formRef.current?.setFieldsValue(INITIAL_DATA);
    }
  }, [hotId]);

  useEffect(() => {
    if (hotId !== null && !strIsNull(hotInfo)) {
      formRef.current?.setFieldsValue(hotInfo);
    }
  }, [hotInfo]);

  useImperativeHandle(ref, () => ({
    submit: () =>
      // eslint-disable-next-line no-async-promise-executor
      new Promise<void>(async (resolve, reject) => {
        const valid = await formRef.current?.validate();

        if (valid === true) {
          const form = JSON.parse(JSON.stringify(formRef.current?.getFieldsValue(true)));

          if (hotId !== null) {
            const bo: IEditHotBo = {
              id: hotId,
              introduce: form.introduce,
            };

            const data = await dispatch(editHot(bo)).unwrap();

            resolve(data);
          } else {
            const bo: IAddHotBo = {
              examId,
              ...form,
            };

            const data = await dispatch(addHot(bo)).unwrap();

            resolve(data);
          }
        } else {
          reject(valid);
        }
      }),
    reset: () => {
      formRef.current?.reset();
    },
  }));

  return (
    <Loading loading={hotInfoLoading}>
      <Form ref={formRef} form={form} initialData={INITIAL_DATA} resetType='empty' rules={rules} colon>
        <FormItem label='专业' name='majorsId'>
          {hotId === null ? (
            <Select placeholder='请选择专业' filterable clearable>
              {majorsList.map((item: any) => (
                <Option key={item.value} value={item.value} label={item.label} />
              ))}
            </Select>
          ) : (
            <span>{hotInfo.majorsName}</span>
          )}
        </FormItem>

        <FormItem label='推荐理由' name='introduce'>
          <Textarea placeholder='请输入推荐理由' />
        </FormItem>
      </Form>
    </Loading>
  );
});

export default EditAddForm;
