import React, { memo, useEffect, useRef, useState } from 'react';
import { DialogPlugin, Popconfirm, Row, Space, TableProps, Tag } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  selectSystemConfig,
  getSystemConfigList,
  delSystemConfig,
  refreshSystemConfigCache,
} from 'modules/system/config';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IRef } from 'components/Form';
import { IGetConfigListBo } from 'types/system';
import PermissionButton from 'components/PermissionButton';
import { Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { useNavigate, Link } from 'react-router-dom';
import { downLoad } from 'utils/tool';

const ConfigTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total, delLoading, refreCacheLoading } = useAppSelector(selectSystemConfig);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetConfigListBo = { pageNum, pageSize };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'row-select',
    'configId',
    'configName',
    'configKey',
    'configValue',
    'configType',
    'remark',
    'createBy',
    'createTime',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['remark', 'createTime', 'createBy', 'updateTime', 'updateBy'];

  /**
   * 删除参数
   */
  const handleDelete = async (configId: number) => {
    const { type } = await dispatch(delSystemConfig(configId));

    if (type.endsWith('fulfilled')) {
      dispatch(getSystemConfigList(searchParams));
    }
  };

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  /**
   * 批量删除参数
   */
  const handleBatchDelete = async () => {
    const confirmDia = DialogPlugin.confirm({
      header: '批量删除提示',
      body: '确定批量删除该数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        const { type } = await dispatch(delSystemConfig(selectedRowKeys.join(',')));

        if (type.endsWith('fulfilled')) {
          handleResetSelection();
          confirmDia.hide();
          dispatch(getSystemConfigList(searchParams));
        }
      },
      onClose: () => {
        confirmDia.hide();
      },
    });

    return false;
  };

  /**
   * 导出参数
   */
  const handleExport = async () => {
    try {
      setExportLoading(true);

      const params = searchRef.current?.getFormParams();

      await downLoad(
        '/system/config/export',
        {
          ...params,
        },
        `参数_${new Date().getTime()}`,
      );

      setExportLoading(false);
    } catch (error) {
      setExportLoading(false);
    }
  };

  /**
   * 刷新缓存
   */
  const handleRefreshCache = () => {
    dispatch(refreshSystemConfigCache());
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    {
      colKey: 'configId',
      width: 120,
      title: '参数主键',
    },
    {
      colKey: 'configName',
      title: '参数名称',
    },
    {
      colKey: 'configKey',
      title: '参数键名',
      width: 240,
    },
    {
      colKey: 'configValue',
      title: '参数键值',
      width: 240,
    },
    {
      colKey: 'configType',
      title: '系统内置',
      cell({ row }) {
        return (
          <Tag theme={row.configType === 'N' ? 'danger' : 'primary'} variant='light'>
            {row.configType === 'N' ? '否' : '是'}
          </Tag>
        );
      },
    },
    {
      colKey: 'remark',
      title: '备注',
    },
    {
      colKey: 'updateTime',
      title: '修改时间',
    },
    {
      colKey: 'updateBy',
      title: '修改人',
    },
    {
      colKey: 'createTime',
      title: '创建时间',
    },
    {
      colKey: 'createBy',
      title: '创建人',
    },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['system:config:edit']}
              onClick={() => navigate('/system/config/edit', { state: { configId: row.configId } })}
            >
              修改
            </PermissionButton>
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleDelete(row.configId)}>
              <>
                <PermissionButton permissions={['system:config:remove']} theme='danger' loading={delLoading}>
                  删除
                </PermissionButton>
              </>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    dispatch(getSystemConfigList(searchParams));
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getSystemConfigList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '参数名称',
            field: 'configName',
          },
          {
            type: 'input',
            label: '参数键名',
            field: 'configKey',
          },
          {
            type: 'select',
            label: '系统内置',
            field: 'configType',
            options: [
              {
                label: '是',
                value: 'Y',
              },
              {
                label: '否',
                value: 'N',
              },
            ],
          },
          {
            type: 'datePicker',
            label: '创建时间',
            field: 'dateArr',
            isTime: false,
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            permissions={['system:config:add']}
            content='添加参数'
            variant='outline'
            onClick={() => navigate('/system/config/add')}
          />
          <PermissionButton
            disabled={selectedRowKeys.length === 0}
            permissions={['system:config:remove']}
            content='批量删除'
            variant='outline'
            theme='danger'
            onClick={() => handleBatchDelete()}
          />
          <PermissionButton
            loading={exportLoading}
            permissions={['system:config:export']}
            content='导出'
            variant='outline'
            theme='warning'
            onClick={() => handleExport()}
          />
          <PermissionButton
            loading={refreCacheLoading}
            permissions={['system:config:remove']}
            content='刷新缓存'
            variant='outline'
            theme='warning'
            onClick={() => handleRefreshCache()}
          />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'configId',
          selectedRowKeys,
          onSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getSystemConfigList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(ConfigTable);
