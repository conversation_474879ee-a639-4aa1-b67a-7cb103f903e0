import React, { useState, useEffect } from 'react';
import { Menu } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { setActive, selectResourceManageMaterialSlice } from 'modules/resourceManage/material';
import Style from './index.module.less';

interface IListItemData<T = any> {
  listProps?: object;
  style?: object;
  title?: string;
  description?: string;
  image?: string;
  action?: React.ReactElement;
  listItemProps?: T;
  id: T;
  name: string;
  subjectId: number;
  subjectName: string;
}

interface IListData<T = any> {
  listProps?: object;
  IListItemData: IListItemData[];
  active?: T;
  broadsideList: IListItemData[];
}

interface IData extends React.HTMLAttributes<HTMLElement> {
  broadsideList: IListData;
}

const SideList: React.FC<IData> = ({ broadsideList }) => {
  const dispatch = useAppDispatch();
  const { active } = useAppSelector(selectResourceManageMaterialSlice);
  const [listItemData, setListItemData] = useState<any>(broadsideList);

  useEffect(() => {
    setListItemData(broadsideList);
  }, [broadsideList]);

  return (
    <React.Fragment>
      <div className={Style.menuList}>
        <Menu
          value={active}
          onChange={(v) => {
            dispatch(setActive(v as number));
          }}
          style={{ marginRight: 20 }}
        >
          {listItemData &&
            listItemData.length !== 0 &&
            listItemData.map((item) => (
              <Menu.MenuItem style={{ width: '100%' }} value={item.subjectId} key={item.subjectId}>
                <div className={Style.content}>
                  <span>{item.subjectName}</span>
                </div>
              </Menu.MenuItem>
            ))}
          {listItemData.length === 0 && <div style={{ textAlign: 'center' }}>暂无数据</div>}
        </Menu>
      </div>
    </React.Fragment>
  );
};

export default SideList;
