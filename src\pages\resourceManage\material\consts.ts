export interface IOptions {
  oid: number;
  opt: string;
  optionContent: string;
}

export const option: IOptions[] = [
  {
    oid: 1,
    opt: 'A',
    optionContent: '',
  },
  {
    oid: 2,
    opt: 'B',
    optionContent: '',
  },
  {
    oid: 3,
    opt: 'C',
    optionContent: '',
  },
  {
    oid: 4,
    opt: 'D',
    optionContent: '',
  },
];

/**
 * 显示菜单
 */
export const MENU_CONFIG = ['insertFormula'];

/**
 * 编辑器样式
 */
export const EDITOR_STYLE = { height: '50px', width: '100%' };

export const createData = {
  questionId: '',
  stem: '',
  answer: '',
  answerId: '',
  analysis: '',
  provenance: null,
  updateBy: '',
  updateTime: '',
  optionVoList: [
    {
      oid: 1,
      opt: 'A',
      optionContent: '',
    },
    {
      oid: 2,
      opt: 'B',
      optionContent: '',
    },
    {
      oid: 3,
      opt: 'C',
      optionContent: '',
    },
    {
      oid: 4,
      opt: 'D',
      optionContent: '',
    },
  ],
};

/**
 * 题干----编辑器显示的菜单
 */
export const STEM_MENU_CONFIG = [
  'bold',
  'bulletedList',
  'numberedList',
  {
    key: 'group-justify',
    title: '对齐',
    iconSvg:
      '<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',
    menuKeys: ['justifyLeft', 'justifyRight', 'justifyCenter', 'justifyJustify'],
  },
  'fontSize',
  'insertFormula',
  {
    key: 'group-image',
    title: '图片',
    iconSvg:
      '<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>',
    menuKeys: ['insertImage', 'uploadImage'],
  },
];

/**
 * 题干---编辑器样式
 */
export const STEM_EDITOR_STYLE = { height: '200px', width: '100%' };
