0 verbose cli E:\Node\node.exe e:\Node\node_modules\npm\bin\npm-cli.js
1 info using npm@10.7.0
2 info using node@v20.15.1
3 silly config:load:file:E:\Nvm\nvm\v20.15.1\node_modules\npm\npmrc
4 silly config:load:file:d:\.0001\dzst\brush-question-adm\.npmrc
5 silly config:load:file:C:\Users\<USER>\.npmrc
6 silly config:load:file:e:\Node\etc\npmrc
7 verbose title npm config get prefix
8 verbose argv "config" "get" "prefix"
9 verbose logfile logs-max:10 dir:d:\.0001\dzst\brush-question-adm\_logs\2025-07-09T08_58_26_348Z-
10 verbose logfile d:\.0001\dzst\brush-question-adm\_logs\2025-07-09T08_58_26_348Z-debug-0.log
11 verbose exit 0
12 info ok
