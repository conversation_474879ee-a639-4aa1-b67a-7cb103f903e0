import React, { useEffect, useState } from 'react';
import { addSpansBackToFormulas } from 'utils/LaTeX';
import { renderToString } from 'react-dom/server';
import { InlineMath } from 'react-katex';
import 'katex/dist/katex.min.css';

/**
 * 加入图片预览
 * @param html
 * @returns
 */

// 将包含公式的字符串转换为 HTML
const renderMathInHtml = (html: string): string => {
  const div = document.createElement('div');
  div.innerHTML = html;

  div.querySelectorAll('span[data-w-e-type="formula"]').forEach((span) => {
    const math = span.getAttribute('data-value');
    if (math) {
      const mathElement = renderToString(<InlineMath math={math} />);
      span.outerHTML = mathElement;
    }
  });

  return div.innerHTML;
};

const ContentRenderer: React.FC<{ content: string }> = ({ content }) => {
  const [htmlContent, setHtmlContent] = useState('');
  useEffect(() => {
    const processedContent = addSpansBackToFormulas(content);
    const renderedHtml = renderMathInHtml(processedContent);
    setHtmlContent(renderedHtml);
  }, [content]);

  return <div dangerouslySetInnerHTML={{ __html: htmlContent }} />;
};

export default ContentRenderer;
