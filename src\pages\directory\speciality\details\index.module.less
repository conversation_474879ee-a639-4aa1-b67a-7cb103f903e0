.formContainer {
  width: 776px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--td-bg-color-container);
  padding: 30px 0 34px;
}

.titleText {
  font-size: 20px;
  margin: 64px 0 32px;
  color: var(--td-text-color-primary);
}

.dateCol {
  :global {
    .t-form__controls-content {
      display: inline;
    }
  }
}

.titleBox {
  padding: 20px;
}

.update {
  :global {
    .t-upload__card-item.t-is-background {
      width: 200px;
    }

    .t-upload__card-container {
      width: 100%;

    }
  }
}
