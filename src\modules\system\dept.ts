import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getSystemDeptListApi,
  delSystemDeptApi,
  addSystemDeptApi,
  editSystemDeptApi,
  getSystemDeptDetailsInfoApi,
  getSystemDeptExcludeListApi,
} from 'api';
import { MessagePlugin } from 'tdesign-react/es/message/Message';
import { IAddDeptBo, IEditDeptBo, IGetDeptListBo } from 'types/system';
import { handleTree, strIsNull } from 'utils/tool';

const namespace = 'system/dept';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  excludeList: Array<any>;
  addEditLoading: boolean;
  delLoading: boolean;
  infoLoading: boolean;
  info: any;
}

const initialState: IInitialState = {
  loading: true,
  list: [],
  excludeList: [],
  addEditLoading: false,
  delLoading: false,
  infoLoading: false,
  info: {},
};

export const getSystemDeptList = createAsyncThunk(`${namespace}/getSystemDeptList`, async (bo: IGetDeptListBo) => {
  const { data } = await getSystemDeptListApi(bo);

  return data;
});

export const getSystemDeptExcludeList = createAsyncThunk(
  `${namespace}/getSystemDeptExcludeList`,
  async (deptId: number) => {
    const { data } = await getSystemDeptExcludeListApi(deptId);

    return data;
  },
);

export const addSystemDept = createAsyncThunk(`${namespace}/addSystemDept`, async (bo: IAddDeptBo) => {
  const { data } = await addSystemDeptApi(bo);

  return data;
});

export const editSystemDept = createAsyncThunk(`${namespace}/editSystemDept`, async (bo: IEditDeptBo) => {
  await editSystemDeptApi(bo);
});

export const delSystemDept = createAsyncThunk(`${namespace}/delSystemDept`, async (deptId: number) => {
  await delSystemDeptApi(deptId);
});

export const getSystemDeptDetailsInfo = createAsyncThunk(
  `${namespace}/getSystemDeptDetailsInfo`,
  async (deptId: number) => {
    const { data } = await getSystemDeptDetailsInfoApi(deptId);

    return data;
  },
);

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSystemDeptList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSystemDeptList.fulfilled, (state, action) => {
        state.loading = false;
        if (!strIsNull(action.payload)) {
          const list = handleTree(action.payload, 'deptId');

          state.list = list;
        } else {
          state.list = [];
        }
      })
      .addCase(getSystemDeptList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getSystemDeptExcludeList.fulfilled, (state, action) => {
        if (!strIsNull(action.payload)) {
          const list = handleTree(action.payload, 'deptId');

          state.excludeList = list;
        } else {
          state.excludeList = [];
        }
      })

      .addCase(addSystemDept.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(addSystemDept.fulfilled, (state) => {
        MessagePlugin.success('添加成功');
        state.addEditLoading = false;
      })
      .addCase(addSystemDept.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(editSystemDept.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(editSystemDept.fulfilled, (state) => {
        MessagePlugin.success('编辑成功');
        state.addEditLoading = false;
      })
      .addCase(editSystemDept.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(delSystemDept.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(delSystemDept.fulfilled, (state) => {
        MessagePlugin.success('删除成功');
        state.delLoading = false;
      })
      .addCase(delSystemDept.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(getSystemDeptDetailsInfo.pending, (state) => {
        state.infoLoading = true;
      })
      .addCase(getSystemDeptDetailsInfo.fulfilled, (state, action) => {
        state.info = action.payload;
        state.infoLoading = false;
      })
      .addCase(getSystemDeptDetailsInfo.rejected, (state) => {
        state.infoLoading = false;
      });
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectSystemDept = (state: RootState) => state.systemDept;

export default listBaseSlice.reducer;
