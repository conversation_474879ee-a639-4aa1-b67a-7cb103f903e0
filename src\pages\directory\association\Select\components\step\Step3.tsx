import React, { useState, memo, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { Table, Form, Tag, Button, Row, Col, Textarea, InputNumber } from 'tdesign-react';

import type { TableProps, TdTagProps } from 'tdesign-react';

import { useAppDispatch, useAppSelector } from 'modules/store';
import { FormInstanceFunctions } from 'tdesign-react/es/form/type';

import {
  selectAssociation,
  getEducationAlMajorsExamList,
  addOrDeleteEducationalMajorsExam,
} from 'modules/directory/association';

const { FormItem } = Form;

interface Status {
  label: string;
  theme: TdTagProps['theme'];
}

interface IRow {
  examId: number | null;
  examName?: string | null;
  isRelated?: number | null;
  educationalMajorsId?: number | null;
  educationalMajorsExamId: number | null;
  enrollmentNumber: number | null;
  connectPreEducationMajor: string | null;
}

interface Step3Props {
  ref?: any;
  rowData: any;
  type: string;
}

const statusNameListMap: Record<number | string, Status> = {
  0: { label: '未关联', theme: 'danger' },
  1: { label: '已关联', theme: 'success' },
  default: { label: '暂无该信息', theme: 'warning' },
};

const Step3: React.FC<Step3Props> = forwardRef(({ rowData, type }: Step3Props, ref) => {
  const dispatch = useAppDispatch();
  const pageState = useAppSelector(selectAssociation);
  const [expandedRowKeys, setExpandedRowKeys] = useState<(string | number)[]>([]);
  const formRef = useRef<FormInstanceFunctions>();

  const associationStatus = async (row: IRow) => {
    await dispatch(
      addOrDeleteEducationalMajorsExam({
        educationalMajorsId: rowData?.educationalMajorsId,
        examId: row.examId,
        isRelated: row.isRelated === 1 ? 0 : 1,
      }),
    );
  };

  const {
    step3: { list, loading },
  } = pageState;

  useEffect(() => {
    const getAllMajorsExamList = async () => {
      await dispatch(getEducationAlMajorsExamList(rowData?.educationalMajorsId ?? null));
    };

    getAllMajorsExamList();
  }, []);

  const associationStatusRow = (isRelated: number) => {
    const relatedLabel = statusNameListMap[isRelated === 0 || isRelated === 1 ? isRelated : 'default'];

    return (
      <Tag shape='round' theme={relatedLabel.theme} variant='light-outline'>
        {relatedLabel.label}
      </Tag>
    );
  };

  const operationRow = (row: IRow) => (
    <>
      {type === 'edit' && (
        <Button theme='primary' variant='text' onClick={() => associationStatus(row)}>
          {row.isRelated === 0 ? '关联类目' : '取消关联'}
        </Button>
      )}

      <Button
        theme='warning'
        variant='text'
        disabled={row.isRelated === 0 && type === 'edit'}
        onClick={() =>
          setExpandedRowKeys((prevKeys: any) => {
            if (prevKeys.includes(row.examId)) return prevKeys.filter((key: number) => key !== row.examId);
            return [row.examId];
          })
        }
      >
        编辑
      </Button>
    </>
  );

  const columns: () => TableProps['columns'] = () => [
    { colKey: 'examName', title: '考试类目' },
    {
      colKey: 'enrollmentNumber',
      title: '招生人数',
    },
    {
      colKey: 'isRelated',
      title: '关联状态',
      cell: ({ row: { isRelated: association } }) => associationStatusRow(association),
    },
    {
      colKey: 'operation',
      title: '操作',
      width: 200,
      cell: ({ row }) => operationRow(row as IRow),
    },
  ];

  const expandedRow: TableProps['expandedRow'] = ({ row }) => (
    <Form ref={formRef} labelWidth={100} labelAlign='top'>
      <Row gutter={[42, 34]} style={{ width: '100%' }}>
        <Col span={12}>
          <FormItem
            label='招生人数'
            name='enrollmentNumber'
            initialData={row.enrollmentNumber}
            rules={[{ required: true, message: '招生人数必填', type: 'error' }]}
            tips='招生人数最小值为1，最大值为9999'
          >
            <InputNumber
              style={{ width: '100%' }}
              allowInputOverLimit={false}
              max={9999}
              min={1}
              theme='normal'
              placeholder='请输入招生人数'
            />
          </FormItem>
        </Col>
        <Col span={12}>
          <FormItem
            label='衔接前置学历/专业'
            name='connectPreEducationMajor'
            initialData={row.connectPreEducationMajor}
          >
            <Textarea maxlength={300} autosize={true} placeholder='请输入此专业可衔接前置学历/专业' />
          </FormItem>
        </Col>
      </Row>
    </Form>
  );

  const submitForm = async () => {
    const isValid = await formRef.current?.validate();

    if (isValid !== true) return false;

    const formData: any = formRef.current?.getFieldsValue?.(true);

    return {
      formData,
      list,
      currentExamId: expandedRowKeys[0],
    };
  };

  useImperativeHandle(ref, () => ({
    submitForm,
  }));

  return (
    <div style={{ marginTop: '20px' }}>
      <p>点击“编辑”按钮填写信息，可自动关联该考试类目</p>
      <Table
        loading={loading}
        bordered
        maxHeight={490}
        rowKey='examId'
        columns={columns()}
        data={list}
        expandedRowKeys={expandedRowKeys}
        expandedRow={expandedRow}
        expandOnRowClick={false}
        onExpandChange={setExpandedRowKeys}
        lazyLoad
        resizable
        expandIcon={false}
      ></Table>
    </div>
  );
});

export default memo(Step3);
