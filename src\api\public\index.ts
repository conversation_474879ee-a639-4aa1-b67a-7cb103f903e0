import request from 'utils/request/index';
/**
 * 查询所有考试
 * @returns
 */
export const publicGetExamListApi = async () => {
  const result = await request.get({
    url: '/exam/getExamList',
  });
  return result;
};

/**
 * 查询所有院校
 * @returns
 */
export const publicAllSchoolListApi = async (educationalName: string | undefined) => {
  const result = await request.get({
    url: '/educationalInstitutions/getEducationalInstitutionsNameAndId',
    params: { educationalName },
  });
  return result;
};

/**
 * 查询所有标签
 * @returns
 */
export const publicAllLabelListApi = async () => {
  const result = await request.get({
    url: '/bulletinTrend/getLabelList',
  });
  return result;
};

// 根据字典类型查询字典数据信息
export const getDictsApi = async (dictType: string) =>
  await request.get({
    url: '/system/dict/data/type/' + dictType,
  });
