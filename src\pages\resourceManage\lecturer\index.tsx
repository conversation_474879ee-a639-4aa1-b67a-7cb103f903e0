import React, { memo, useEffect, useRef, useState } from 'react';
import { Popconfirm, Row, Space, TableProps } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectResourceManageLecturer, getLecturerList, deleteLecturer } from 'modules/resourceManage/lecturer';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IRef } from 'components/Form';
import { IGetResourceLecturerListBo } from 'types/resourceManage';
import PermissionButton from 'components/PermissionButton';
import { ImageViewer, Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { useNavigate } from 'react-router-dom';

const LecturerTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total, deleteLecturerLoading } =
    useAppSelector(selectResourceManageLecturer);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetResourceLecturerListBo = { pageNum, pageSize };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'serial-number',
    'teacherName',
    'phoneNumber',
    'technicalTitle',
    'brief',
    'teacherImgUrl',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['technicalTitle', 'brief', 'createTime', 'createBy', 'updateTime', 'updateBy'];

  /**
   * 删除课程
   */
  const handleDelete = async (id: number) => {
    const { type } = await dispatch(deleteLecturer(id));

    if (type.endsWith('fulfilled')) {
      dispatch(getLecturerList(searchParams));
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'serial-number',
      width: 80,
      title: '序号',
    },
    {
      colKey: 'teacherName',
      title: '讲师姓名',
    },
    {
      colKey: 'phoneNumber',
      title: '联系电话',
    },
    {
      colKey: 'technicalTitle',
      title: '职称',
    },
    {
      colKey: 'brief',
      title: '简介',
    },
    {
      colKey: 'teacherImgUrl',
      title: '讲师照片',
      width: 200,
      cell({ row }) {
        return row.teacherImgUrl
          ? ImageViewer({
            type: 'item',
            images: [row.teacherImgUrl],
            style: {
              width: 30,
              height: 30,
            },
          })
          : null;
      },
    },
    {
      colKey: 'updateTime',
      title: '修改时间',
    },
    {
      colKey: 'updateBy',
      title: '修改人',
    },
    {
      colKey: 'createTime',
      title: '创建时间',
    },
    {
      colKey: 'createBy',
      title: '创建人',
    },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['resource:lecturer:query']}
              onClick={() => navigate('/resource/lecturer/details', { state: { id: row.id } })}
            >
              详情
            </PermissionButton>
            <PermissionButton
              permissions={['resource:lecturer:edit']}
              onClick={() => navigate('/resource/lecturer/edit', { state: { id: row.id } })}
            >
              修改
            </PermissionButton>
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleDelete(row.id)}>
              <>
                <PermissionButton
                  permissions={['resource:lecturer:remove']}
                  theme='danger'
                  loading={deleteLecturerLoading}
                >
                  删除
                </PermissionButton>
              </>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    dispatch(getLecturerList(searchParams));
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getLecturerList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '讲师名称',
            field: 'teacherName',
          },
          {
            type: 'input',
            label: '联系电话',
            field: 'phoneNumber',
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            permissions={['resource:lecturer:add']}
            content='添加讲师'
            variant='outline'
            onClick={() => navigate('/resource/lecturer/add')}
          />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'id',
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getLecturerList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(LecturerTable);
