import React, { memo, useEffect, useRef } from 'react';
import { Button, TableProps } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectSRRankingEducational, getEduEnrollmentList } from 'modules/srRanking/educational';
import { IRef } from 'components/Form';
import { IGetEduEnrollmentListBo } from 'types/srRanking';
import { ImageViewer, Search, Tables } from 'components';
import { strIsNull } from 'utils/tool';
import { useNavigate } from 'react-router-dom';

const SRRankingEducationalEnrollmentTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total, active } = useAppSelector(selectSRRankingEducational);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetEduEnrollmentListBo = { pageNum, pageSize, examId: active };

  const columns: TableProps['columns'] = [
    {
      colKey: 'serial-number',
      width: 80,
      title: '排名',
    },
    {
      colKey: 'educationalName',
      title: '院校名称',
    },
    {
      colKey: 'educationalLogoImg',
      title: '院校logo',
      width: 120,
      cell: ({ row }) =>
        row.educationalLogoImg
          ? ImageViewer({
            type: 'item',
            images: [row.educationalLogoImg],
            style: {
              width: 30,
              height: 30,
            },
          })
          : null,
    },
    {
      colKey: 'enrollmentNumber',
      title: '招生数',
      width: 120,
    },
    {
      colKey: 'fractionBar',
      title: '录取分数',
      width: 120,
    },
    {
      colKey: 'op',
      title: '操作',
      width: 120,
      cell({ row }) {
        return (
          <>
            <Button
              variant='text'
              theme='primary'
              size='medium'
              onClick={() =>
                navigate('/directory/school/details', {
                  state: { id: row.id, type: 2, path: '/srRanking/educational', header: '单招榜-院校榜-招生榜' },
                })
              }
            >
              详情
            </Button>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    if (!strIsNull(active)) {
      dispatch(getEduEnrollmentList(searchParams));
    }
  }, [active]);

  return (
    <div style={{ paddingTop: 'var(--td-comp-paddingTB-l)' }}>
      <Search
        ref={searchRef}
        method={getEduEnrollmentList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '院校名称',
            field: 'educationalName',
          },
        ]}
      />

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: list ?? [],
          loading,
          rowKey: 'id',
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getEduEnrollmentList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(SRRankingEducationalEnrollmentTable);
