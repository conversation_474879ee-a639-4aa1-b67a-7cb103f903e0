import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getSubjectListApi,
  getResourceCourseListApi,
  getTeacherList<PERSON>pi,
  editResourceCourseApi,
  addResourceCourseApi,
  deleteResourceCourseApi,
  getResourceCourseInfoApi,
  getResourceCourseChapterTreeListApi,
  getResourceCourseChapterInfoApi,
  addResourceCourseChapterApi,
  editResourceCourseChapterApi,
  deleteResourceCourseChapterApi,
  previewVideoApi,
} from 'api';
import {
  IGetResourceCourseListBo,
  IEditResourceCourseBo,
  IAddResourceCourseBo,
  IAddResourceCourseChapterBo,
  IEditResourceCourseChapterBo,
  IPreviewVideoBo,
} from 'types/resourceManage';
import { MenuValue, MessagePlugin } from 'tdesign-react';
import { strIsNull } from 'utils/tool';

const namespace = 'resourceManage/course';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  active: MenuValue;
  subjectLoading: boolean;
  subjectList: Array<any>;
  teacherLoading: boolean;
  teacherList: Array<any>;
  courseInfoLoading: boolean;
  courseInfo: any;
  addEditCourseLoading: boolean;
  deleteCourseLoading: boolean;
  chapterTreeLoading: boolean;
  chapterTreeList: Array<any>;
  addChapterLoading: boolean;
  editChapterLoading: boolean;
  deleteChapterLoading: boolean;
  chapterInfoLoading: boolean;
  chapterInfo: any;
  previewVideoLoading: boolean;
  previewVideoInfo: any;
}

const initialState: IInitialState = {
  loading: false,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  active: '',
  subjectLoading: false,
  subjectList: [],
  teacherLoading: false,
  teacherList: [],
  courseInfoLoading: false,
  courseInfo: {},
  addEditCourseLoading: false,
  deleteCourseLoading: false,
  chapterTreeLoading: false,
  chapterTreeList: [],
  addChapterLoading: false,
  editChapterLoading: false,
  deleteChapterLoading: false,
  chapterInfoLoading: false,
  chapterInfo: {},
  previewVideoLoading: false,
  previewVideoInfo: {},
};

export const getCourseList = createAsyncThunk(`${namespace}/getCourseList`, async (bo: IGetResourceCourseListBo) => {
  const { data } = await getResourceCourseListApi(bo);

  return {
    list: data.rows,
    total: data.total,
    pageNum: bo.pageNum,
    pageSize: bo.pageSize,
  };
});

export const getCourseInfo = createAsyncThunk(`${namespace}/getCourseInfo`, async (id: number) => {
  const { data } = await getResourceCourseInfoApi(id);

  return data;
});

export const getSubjectList = createAsyncThunk(`${namespace}/getSubjectList`, async () => {
  const { data } = await getSubjectListApi(null);

  return data;
});

export const getTeacherList = createAsyncThunk(`${namespace}/getTeacherList`, async () => {
  const { data } = await getTeacherListApi();

  return data;
});

export const addResourceCourse = createAsyncThunk(
  `${namespace}/addResourceCourse`,
  async (bo: IAddResourceCourseBo) => {
    const { data } = await addResourceCourseApi(bo);

    return data;
  },
);

export const editResourceCourse = createAsyncThunk(
  `${namespace}/editResourceCourse`,
  async (bo: IEditResourceCourseBo) => {
    await editResourceCourseApi(bo);
  },
);

export const deleteResourceCourse = createAsyncThunk(`${namespace}/deleteResourceCourse`, async (id: number) => {
  await deleteResourceCourseApi(id);
});

export const getChapterTreeList = createAsyncThunk(`${namespace}/getChapterTreeList`, async (courseId: number) => {
  const { data } = await getResourceCourseChapterTreeListApi(courseId);

  return data;
});

export const getChapterInfo = createAsyncThunk(`${namespace}/getChapterInfo`, async (chapterId: number) => {
  const { data } = await getResourceCourseChapterInfoApi(chapterId);

  return data;
});

export const addResourceCourseChapter = createAsyncThunk(
  `${namespace}/addResourceCourseChapter`,
  async (bo: IAddResourceCourseChapterBo) => {
    await addResourceCourseChapterApi(bo);
  },
);

export const editResourceCourseChapter = createAsyncThunk(
  `${namespace}/editResourceCourseChapter`,
  async (bo: IEditResourceCourseChapterBo) => {
    await editResourceCourseChapterApi(bo);
  },
);

export const deleteResourceCourseChapter = createAsyncThunk(
  `${namespace}/deleteResourceCourseChapter`,
  async (chapterId: number) => {
    await deleteResourceCourseChapterApi(chapterId);
  },
);

export const previewVideo = createAsyncThunk(`${namespace}/previewVideo`, async (bo: IPreviewVideoBo) => {
  const { data } = await previewVideoApi(bo);

  return data;
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setActive: (state, { payload }) => {
      state.active = payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getCourseList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCourseList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getCourseList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getCourseInfo.pending, (state) => {
        state.courseInfoLoading = true;
      })
      .addCase(getCourseInfo.fulfilled, (state, action) => {
        state.courseInfo = action.payload;
        state.courseInfoLoading = false;
      })
      .addCase(getCourseInfo.rejected, (state) => {
        state.courseInfoLoading = false;
      })

      .addCase(getSubjectList.pending, (state) => {
        state.subjectLoading = true;
      })
      .addCase(getSubjectList.fulfilled, (state, action) => {
        state.subjectList = action.payload.rows;
        if (!strIsNull(action.payload.rows)) {
          state.active = action.payload.rows[0].subjectId;
        }
        state.subjectLoading = false;
      })
      .addCase(getSubjectList.rejected, (state) => {
        state.subjectLoading = false;
      })

      .addCase(getTeacherList.pending, (state) => {
        state.teacherLoading = true;
      })
      .addCase(getTeacherList.fulfilled, (state, action) => {
        state.teacherList = action.payload;
        state.teacherLoading = false;
      })
      .addCase(getTeacherList.rejected, (state) => {
        state.teacherLoading = false;
      })

      .addCase(addResourceCourse.pending, (state) => {
        state.addEditCourseLoading = true;
      })
      .addCase(addResourceCourse.fulfilled, (state) => {
        MessagePlugin.success('添加成功！');
        state.addEditCourseLoading = false;
      })
      .addCase(addResourceCourse.rejected, (state) => {
        state.addEditCourseLoading = false;
      })

      .addCase(editResourceCourse.pending, (state) => {
        state.addEditCourseLoading = true;
      })
      .addCase(editResourceCourse.fulfilled, (state) => {
        MessagePlugin.success('编辑成功！');
        state.addEditCourseLoading = false;
      })
      .addCase(editResourceCourse.rejected, (state) => {
        state.addEditCourseLoading = false;
      })

      .addCase(deleteResourceCourse.pending, (state) => {
        state.deleteCourseLoading = true;
      })
      .addCase(deleteResourceCourse.fulfilled, (state) => {
        state.deleteCourseLoading = false;

        const isPrePage = (state.total - (state.pageNum - 1) * state.pageSize) % state.pageSize === 1;

        if (isPrePage) {
          // eslint-disable-next-line no-plusplus
          --state.pageNum;
        }
      })
      .addCase(deleteResourceCourse.rejected, (state) => {
        state.deleteCourseLoading = false;
      })

      .addCase(getChapterTreeList.pending, (state) => {
        state.chapterTreeLoading = true;
      })
      .addCase(getChapterTreeList.fulfilled, (state, action) => {
        state.chapterTreeLoading = false;
        state.chapterTreeList = action.payload;
      })
      .addCase(getChapterTreeList.rejected, (state) => {
        state.chapterTreeLoading = false;
      })

      .addCase(getChapterInfo.pending, (state) => {
        state.chapterInfoLoading = true;
      })
      .addCase(getChapterInfo.fulfilled, (state, action) => {
        state.chapterInfo = action.payload;
        state.chapterInfoLoading = false;
      })
      .addCase(getChapterInfo.rejected, (state) => {
        state.chapterInfoLoading = false;
      })

      .addCase(addResourceCourseChapter.pending, (state) => {
        state.addChapterLoading = true;
      })
      .addCase(addResourceCourseChapter.fulfilled, (state) => {
        state.addChapterLoading = false;
        MessagePlugin.success('添加成功！');
      })
      .addCase(addResourceCourseChapter.rejected, (state) => {
        state.addChapterLoading = false;
      })

      .addCase(editResourceCourseChapter.pending, (state) => {
        state.editChapterLoading = true;
      })
      .addCase(editResourceCourseChapter.fulfilled, (state) => {
        state.editChapterLoading = false;
        MessagePlugin.success('编辑成功！');
      })
      .addCase(editResourceCourseChapter.rejected, (state) => {
        state.editChapterLoading = false;
      })

      .addCase(deleteResourceCourseChapter.pending, (state) => {
        state.deleteChapterLoading = true;
      })
      .addCase(deleteResourceCourseChapter.fulfilled, (state) => {
        state.deleteChapterLoading = false;
        MessagePlugin.success('删除成功！');
      })
      .addCase(deleteResourceCourseChapter.rejected, (state) => {
        state.deleteChapterLoading = false;
      })

      .addCase(previewVideo.pending, (state) => {
        state.previewVideoLoading = true;
      })
      .addCase(previewVideo.fulfilled, (state, action) => {
        state.previewVideoLoading = false;
        state.previewVideoInfo = action.payload;
      })
      .addCase(previewVideo.rejected, (state) => {
        state.previewVideoLoading = false;
      });
  },
});

export const { clearPageState, setActive } = listBaseSlice.actions;

export const selectResourceManageCourse = (state: RootState) => state.resourceManageCourse;

export default listBaseSlice.reducer;
