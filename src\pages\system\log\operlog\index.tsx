import React, { Fragment, memo, useEffect, useRef, useState } from 'react';
import { Col, Dialog, DialogPlugin, Form, FormInstanceFunctions, Row, Space, TableProps, Tag } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectSystemLog, getSystemOperLogList, delSystemOperLog, clearSystemOperLog } from 'modules/system/log';
import { selectPublic, getDicts } from 'modules/public';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IRef } from 'components/Form';
import { IGetOperLogListBo } from 'types/system';
import PermissionButton from 'components/PermissionButton';
import { Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { downLoad, strIsNull } from 'utils/tool';
import useForm from 'tdesign-react/es/form/hooks/useForm';

const { FormItem } = Form;

const OperLogTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const { loading, list, pageNum, pageSize, total, delLoading } = useAppSelector(selectSystemLog);
  const { dictList } = useAppSelector(selectPublic);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetOperLogListBo = { pageNum, pageSize };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'row-select',
    'operId',
    'title',
    'businessType',
    'operName',
    'operIp',
    'operLocation',
    'status',
    'operTime',
    'costTime',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['operLocation', 'costTime'];
  const [visible, setVisible] = useState(false);
  const [row, setRow] = useState({});

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  /**
   * 批量删除操作日志
   */
  const handleBatchDelete = async () => {
    const confirmDia = DialogPlugin.confirm({
      header: '批量删除提示',
      body: '确定批量删除该数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      confirmLoading: delLoading,
      onConfirm: async () => {
        const { type } = await dispatch(delSystemOperLog(selectedRowKeys.join(',')));

        if (type.endsWith('fulfilled')) {
          handleResetSelection();
          confirmDia.hide();
          dispatch(getSystemOperLogList(searchParams));
        }
      },
      onClose: () => {
        confirmDia.hide();
      },
    });

    return false;
  };

  /**
   * 清空操作日志
   */
  const handleClear = async () => {
    const confirmDia = DialogPlugin.confirm({
      header: '系统提示',
      body: '是否确认清空所有操作日志数据项？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        const { type } = await dispatch(clearSystemOperLog());

        if (type.endsWith('fulfilled')) {
          confirmDia.hide();
          dispatch(getSystemOperLogList(searchParams));
        }
      },
      onClose: () => {
        confirmDia.hide();
      },
    });

    return false;
  };

  /**
   * 导出操作日志
   */
  const handleExport = async () => {
    try {
      setExportLoading(true);

      const params = searchRef.current?.getFormParams();

      await downLoad(
        '/monitor/operlog/export',
        {
          ...params,
        },
        `操作日志_${new Date().getTime()}`,
      );

      setExportLoading(false);
    } catch (error) {
      setExportLoading(false);
    }
  };

  const handleDetails = (row: any) => {
    setRow(row);
    setVisible(true);
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    {
      colKey: 'operId',
      width: 120,
      title: '日志编号',
    },
    {
      colKey: 'title',
      title: '系统模块',
    },
    {
      colKey: 'businessType',
      title: '操作类型',
      cell({ row }) {
        const dict = !strIsNull(dictList) ? dictList.find((item) => item.dictValue == row.businessType) : {};

        return (
          <Tag theme={dict?.listClass === 'info' ? 'default' : dict?.listClass} variant='light'>
            {dict?.dictLabel}
          </Tag>
        );
      },
    },
    {
      colKey: 'operName',
      title: '操作人员',
    },
    {
      colKey: 'operIp',
      title: '操作地址',
    },
    {
      colKey: 'operLocation',
      title: '操作地点',
    },
    {
      colKey: 'status',
      title: '状态',
      cell({ row }) {
        return (
          <Tag theme={row.status === 0 ? 'primary' : 'danger'} variant='light'>
            {row.status === 0 ? '成功' : '失败'}
          </Tag>
        );
      },
    },
    {
      colKey: 'operTime',
      title: '操作时间',
    },
    {
      colKey: 'costTime',
      title: '消耗时间',
      cell({ row }) {
        return <span>{row.costTime}毫秒</span>;
      },
    },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      cell({ row }) {
        return (
          <PermissionButton permissions={['monitor:operlog:query']} theme='primary' onClick={() => handleDetails(row)}>
            详情
          </PermissionButton>
        );
      },
    },
  ];

  useEffect(() => {
    dispatch(getDicts('sys_oper_type'));
    dispatch(getSystemOperLogList(searchParams));
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getSystemOperLogList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '操作地址',
            field: 'operIp',
          },
          {
            type: 'input',
            label: '系统模块',
            field: 'title',
          },
          {
            type: 'input',
            label: '操作人员',
            field: 'operName',
          },
          {
            type: 'select',
            label: '类型',
            field: 'businessType',
            options: dictList,
            nameField: 'dictLabel',
            valueField: 'dictValue',
          },
          {
            type: 'select',
            label: '状态',
            field: 'status',
            options: [
              {
                label: '成功',
                value: '0',
              },
              {
                label: '失败',
                value: '1',
              },
            ],
          },
          {
            type: 'datePicker',
            label: '操作时间',
            field: 'dateArr',
            isTime: true,
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            disabled={selectedRowKeys.length === 0}
            permissions={['monitor:operlog:remove']}
            content='批量删除'
            variant='outline'
            theme='danger'
            onClick={() => handleBatchDelete()}
          />
          <PermissionButton
            permissions={['monitor:operlog:remove']}
            content='清空'
            variant='outline'
            theme='danger'
            onClick={() => handleClear()}
          />
          <PermissionButton
            loading={exportLoading}
            permissions={['monitor:operlog:export']}
            content='导出'
            variant='outline'
            theme='warning'
            onClick={() => handleExport()}
          />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'operId',
          selectedRowKeys,
          onSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getSystemOperLogList}
        params={searchParams}
      />

      <Dialog
        width='800px'
        header='操作日志详细'
        visible={visible}
        closeOnEscKeydown={true}
        closeOnOverlayClick={true}
        confirmOnEnter={true}
        destroyOnClose={true}
        preventScrollThrough={true}
        confirmBtn={{
          content: '确认',
          onClick: () => {
            setVisible(false);
          },
        }}
        cancelBtn={null}
        onClose={() => setVisible(false)}
      >
        <Form labelWidth={100} colon={true}>
          <Row>
            <Col span={6}>
              <FormItem label='操作模块'>
                {row.title} /{' '}
                {!strIsNull(dictList)
                  ? dictList.find((item) => item.dictValue == row.businessType)?.dictLabel
                  : row.businessType}
              </FormItem>
              <FormItem label='登录信息'>
                {row.operName} / {row.operIp} / {row.operLocation}
              </FormItem>
            </Col>
            <Col span={6}>
              <FormItem label='请求地址'>{row.operUrl}</FormItem>
              <FormItem label='请求方式'>{row.requestMethod}</FormItem>
            </Col>
            <Col span={12}>
              <FormItem label='操作方法'>{row.method}</FormItem>
            </Col>
            <Col span={12}>
              <FormItem label='请求参数'>{row.operParam}</FormItem>
            </Col>
            <Col span={12}>
              <FormItem label='返回参数'>{row.jsonResult}</FormItem>
            </Col>
            <Col span={4}>
              <FormItem label='操作状态'>{row.status === 0 ? '正常' : '失败'}</FormItem>
            </Col>
            <Col span={4}>
              <FormItem label='消耗时间'>{row.costTime}毫秒</FormItem>
            </Col>
            <Col span={4}>
              <FormItem label='操作时间'>{row.operTime}</FormItem>
            </Col>
            {row.status === 1 && (
              <Col span={12}>
                <FormItem label='异常信息'>{row.errorMsg}</FormItem>
              </Col>
            )}
          </Row>
        </Form>
      </Dialog>
    </div>
  );
};

export default memo(OperLogTable);
