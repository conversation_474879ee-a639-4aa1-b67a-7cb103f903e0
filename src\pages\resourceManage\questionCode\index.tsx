import React, { memo, useEffect, useRef, useState } from 'react';
import { MessagePlugin, Popconfirm, Row, Space, TableProps, Switch } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  generateQrCode,
  deleteQrCode,
  deleteQuesCode,
  fetchQuesCodeList,
  selectQuesCodeState,
  editQuesCode,
} from 'modules/resourceManage/questionCodeRedux';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IRef } from 'components/Form';
import PermissionButton from 'components/PermissionButton';
import CustomColumns from 'components/CustomColumns';
import { useNavigate } from 'react-router-dom';
import { IQuesCodeQueryBo } from 'types/resourceManage';
import { Search, Tables, ImageViewer } from 'components';
import { downloadImage } from 'utils/tool';

const QuesCodeTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'serial-number',
    'codeName',
    'codeBrief',
    'coverImageUrl',
    'qrCodeStatus',
    'videoStatus',
    'isUse',
    'op',
  ];
  const { loading, list, pageNum, pageSize, total, deleteLoading } = useAppSelector(selectQuesCodeState);
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['updateTime', 'updateBy', 'createTime', 'createBy'];
  const searchParams: IQuesCodeQueryBo = {
    pageNum,
    pageSize,
  };
  useEffect(() => {
    dispatch(fetchQuesCodeList(searchParams));
  }, []);
  /**
   * 清除二维码
   */
  const handleDeleteQrCode = async (id: number) => {
    const { type, payload } = await dispatch(deleteQrCode(id.toString()));

    if (type.endsWith('fulfilled')) {
      MessagePlugin.success('二维码清除成功！');
      // 刷新列表
      dispatch(fetchQuesCodeList({ pageNum, pageSize }));
    } else {
      MessagePlugin.error(payload || '清除二维码失败');
    }
  };
  /**
   * 删除
   */
  const handleDelete = async (id: number) => {
    const { type, payload } = await dispatch(deleteQuesCode(id));

    if (type.endsWith('fulfilled')) {
      dispatch(fetchQuesCodeList({ pageNum, pageSize }));
    } else {
      MessagePlugin.error(payload || '删除失败');
    }
  };
  /**
   * 生成二维码
   */
  const handleCreateQuesCode = async (id: number) => {
    const { type, payload } = await dispatch(generateQrCode(id));

    if (type.endsWith('fulfilled')) {
      MessagePlugin.success('二维码生成成功！');
      // 刷新列表
      dispatch(fetchQuesCodeList({ pageNum, pageSize }));
    } else {
      MessagePlugin.error(payload || '生成二维码失败');
    }
  };

  /**
   * 下载二维码
   */
  const handleDownloadQrCode = async (row: any) => {
    if (!row.codeUrl) {
      MessagePlugin.warning('该记录暂无二维码，无法下载');
      return;
    }

    try {
      const fileName = `${row.codeName || '二维码'}_${new Date().getTime()}.png`;
      await downloadImage(row.codeUrl, fileName);
      MessagePlugin.success('二维码下载成功！');
    } catch (error) {
      MessagePlugin.error('二维码下载失败，请重试');
    }
  };

  const columns: TableProps['columns'] = [
    { colKey: 'serial-number', width: 80, title: '序号' },
    { colKey: 'codeName', title: '标题', width: 100 },
    { colKey: 'codeBrief', title: '视频简介', width: 100 },
    {
      colKey: 'coverImageUrl',
      title: '二维码',
      width: 160,
      cell({ row }) {
        return row.codeUrl ? (
          <Space>
            <ImageViewer
              type='item'
              images={[row.codeUrl]}
              style={{
                width: 40,
                height: 40,
              }}
            />
            <PermissionButton
              permissions={['resource:quesCode:download']}
              theme='primary'
              variant='text'
              size='small'
              onClick={() => handleDownloadQrCode(row)}
            >
              下载
            </PermissionButton>
          </Space>
        ) : (
          <span style={{ color: '#ccc' }}>未生成</span>
        );
      },
    },
    {
      colKey: 'videoStatus',
      title: '视频状态',
      width: 100,
      cell({ row }) {
        // 判断是否有视频文件ID来确定视频状态
        const hasVideo = row.filesId && row.filesId > 0;
        return hasVideo ? (
          <span
            style={{
              color: '#52c41a',
              fontWeight: 500,
            }}
          >
            已上传
          </span>
        ) : (
          <span
            style={{
              color: '#faad14',
              fontWeight: 500,
            }}
          >
            未上传
          </span>
        );
      },
    },
    {
      colKey: 'isUse',
      title: '使用状态',
      width: 100,
      cell({ row }) {
        return (
          <Switch
            value={row.isUse === 1}
            onChange={async (val) => {
              try {
                await dispatch(
                  editQuesCode({
                    id: row.id,
                    codeName: row.codeName || '',
                    codeUrl: row.codeUrl || '',
                    isUse: val ? 1 : 0,
                  }),
                );
                MessagePlugin.success(`${val ? '启用' : '禁用'}成功！`);
                // 刷新列表数据
                dispatch(fetchQuesCodeList({ pageNum, pageSize }));
              } catch (error) {
                MessagePlugin.error('操作失败，请重试');
              }
            }}
          />
        );
      },
    },
    { colKey: 'updateTime', title: '修改时间' },
    { colKey: 'updateBy', title: '修改人' },
    { colKey: 'createTime', title: '创建时间' },
    { colKey: 'createBy', title: '创建人' },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['resource:quesCode:edit']}
              onClick={() => navigate('/resource/quesCode/edit', { state: { id: row.id } })}
            >
              修改
            </PermissionButton>

            {/* 根据是否已生成二维码和使用状态显示不同按钮 */}
            {!row.codeUrl ? (
              <Popconfirm
                content={row.isUse === 1 ? '该问题代码正在使用中，无法生成二维码' : '确定要生成二维码吗？'}
                onConfirm={row.isUse === 1 ? undefined : () => handleCreateQuesCode(row.id)}
              >
                <PermissionButton
                  permissions={['resource:quesCode:create']}
                  theme='primary'
                  loading={loading}
                  disabled={row.isUse === 1}
                >
                  生成二维码
                </PermissionButton>
              </Popconfirm>
            ) : (
              <Popconfirm
                content={row.isUse === 1 ? '该问题代码正在使用中，无法清除二维码' : '确定要清除二维码吗？'}
                theme='warning'
                onConfirm={row.isUse === 1 ? undefined : () => handleDeleteQrCode(row.id)}
              >
                <PermissionButton
                  permissions={['resource:quesCode:remove']}
                  theme='warning'
                  loading={deleteLoading}
                  disabled={row.isUse === 1}
                >
                  清除二维码
                </PermissionButton>
              </Popconfirm>
            )}
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleDelete(row.id)}>
              <PermissionButton permissions={['resource:quesCode:remove']} theme='danger' loading={deleteLoading}>
                删除
              </PermissionButton>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  return (
    <div className={classnames(CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={fetchQuesCodeList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '标题',
            field: 'codeName',
          },
          {
            type: 'select',
            label: '二维码状态',
            field: 'isCreate',
            options: [
              { label: '全部', value: '' },
              { label: '已生成', value: '1' },
              { label: '未生成', value: '0' },
            ],
          },
          {
            type: 'select',
            label: '使用状态',
            field: 'isUse',
            options: [
              { label: '全部', value: '' },
              { label: '使用中', value: '1' },
              { label: '未使用', value: '0' },
            ],
          },
          {
            type: 'select',
            label: '视频状态',
            field: 'isUpload',
            options: [
              { label: '全部', value: '' },
              { label: '已上传', value: '1' },
              { label: '未上传', value: '0' },
            ],
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            permissions={['resource:quesCode:add']}
            content='添加'
            variant='outline'
            onClick={() => navigate('/resource/quesCode/add')}
          />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'id',
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={fetchQuesCodeList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(QuesCodeTable);
