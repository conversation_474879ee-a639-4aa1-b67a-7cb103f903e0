import React, { useState, memo, useEffect, useRef } from 'react';
import {
  Dialog,
  Button,
  Row,
  Col,
  Popconfirm,
  DialogPlugin,
  MessagePlugin,
  ImageViewer,
  Image,
  type ImageViewerProps,
  TableRowData,
  TableProps,
} from 'tdesign-react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectSpeciality, getMajorsList, deleteMajors } from 'modules/directory/speciality';
import Exam from './components/Exam';
import { BrowseIcon } from 'tdesign-icons-react';
import { Search, Tables } from 'components';

export const SelectTable = () => {
  const dispatch = useAppDispatch();
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const [visible, setVisible] = useState(false);
  const [visibleProps, setVisibleProps] = useState<TableRowData>();

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  const navigate = useNavigate();

  const { active, loading, list, pageNum, pageSize, total } = useAppSelector(selectSpeciality);
  const searchParams = { pageNum, pageSize, categoryId: active };

  useEffect(() => {
    if (active) {
      searchRef.current?.resetForm();
    }
  }, [active]);

  const batchDelete = () => {
    if (selectedRowKeys.length === 0) return MessagePlugin.warning('请选择数据！');
    const confirmDia = DialogPlugin.confirm({
      header: '批量删除提示',
      body: '确定批量删除该数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        await dispatch(
          deleteMajors({
            majorsIdList: selectedRowKeys,
          }),
        );
        handleResetSelection();
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
    return false;
  };

  const handleClickDelete = (row: any) => {
    dispatch(
      deleteMajors({
        majorsIdList: [row.majorsId],
      }),
    );
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    { colKey: 'serial-number', width: 80, title: '序号' },
    {
      title: '专业名称',
      fixed: 'left',
      colKey: 'majorsName',
    },
    {
      title: '专业简介',
      colKey: 'majorsDesc',
    },
    {
      title: '专业介绍',
      colKey: 'majorsIntroduce',
    },
    {
      title: '专业背景图',
      colKey: 'majorsBackgroundImgUrl',
      cell({ row }) {
        const imgArr = [row.majorsBackgroundImgUrl];
        return (
          <>
            {imgArr.map((imgSrc, index) => {
              const trigger: ImageViewerProps['trigger'] = ({ open }) => {
                const mask = row.logoUrl && (
                  <div
                    style={{
                      background: 'rgba(0,0,0,.6)',
                      color: '#fff',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onClick={open}
                  >
                    <span>
                      <BrowseIcon size='16px' name={'browse'} />
                    </span>
                  </div>
                );

                return (
                  <Image
                    alt={'test'}
                    src={imgSrc}
                    overlayContent={mask}
                    overlayTrigger='hover'
                    fit='contain'
                    style={{
                      width: 50,
                      height: 50,
                      border: '4px solid var(--td-bg-color-secondarycontainer)',
                      borderRadius: 'var(--td-radius-medium)',
                      backgroundColor: '#fff',
                    }}
                  />
                );
              };
              return <ImageViewer key={imgSrc} trigger={trigger} images={imgArr} defaultIndex={index} />;
            })}
          </>
        );
      },
    },
    {
      title: '创建人',
      colKey: 'createNickName',
    },
    {
      title: '创建时间',
      colKey: 'createTime',
    },
    {
      title: '修改人',
      colKey: 'updateNickName',
    },
    {
      title: '修改时间',
      colKey: 'updateTime',
    },
    {
      fixed: 'right',
      width: 240,
      colKey: 'op',
      title: '操作',
      cell({ row }) {
        return (
          <>
            <Button
              theme='primary'
              variant='text'
              onClick={() => navigate('/directory/speciality/edit', { state: { row, type: 'edit' } })}
            >
              编辑
            </Button>
            <Button
              theme='primary'
              variant='text'
              onClick={() => {
                setVisible(true);
                setVisibleProps(row);
              }}
            >
              所属考试
            </Button>
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleClickDelete(row)}>
              <Button theme='danger' variant='text'>
                删除
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Search
        ref={searchRef}
        method={getMajorsList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '专业名称',
            field: 'majorsName',
          },
        ]}
      ></Search>
      <Row gutter={8} align='middle'>
        <Col>
          <Button
            variant='outline'
            theme='primary'
            onClick={() => navigate('/directory/speciality/add', { state: { type: 'add' } })}
          >
            新增专业
          </Button>
        </Col>
        <Col>
          <Button variant='outline' theme='danger' onClick={batchDelete}>
            批量删除
          </Button>
        </Col>
      </Row>
      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: list ?? [],
          loading,
          rowKey: 'majorsId',
          selectedRowKeys,
          onSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getMajorsList}
        params={searchParams}
      />
      {visible && (
        <Dialog
          header={`${visibleProps?.majorsName} — 关联考试类目`}
          visible={visible}
          cancelBtn={null}
          footer={false}
          width={1000}
          onCloseBtnClick={() => setVisible(false)}
        >
          <p>请关联此专业参加的考试类目</p>
          <Exam visibleProps={visibleProps}></Exam>
        </Dialog>
      )}
    </>
  );
};
