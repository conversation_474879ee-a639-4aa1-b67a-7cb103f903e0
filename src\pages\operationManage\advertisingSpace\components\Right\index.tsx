import React, { useState, useEffect } from 'react';
import { Swiper } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectAdvertisingSpace, getCarouselImageList } from 'modules/operationManage/advertisingSpace';
import Style from './index.module.less';
import Component28 from 'assets/image/Component28.png';
import Component30 from 'assets/image/Component30.png';
import Group84 from 'assets/image/Group84.png';
import Group87 from 'assets/image/Group87.png';
import Group88 from 'assets/image/Group88.png';
import MaskGroup from 'assets/image/MaskGroup.png';
import ReleaseInformation1 from 'assets/image/ReleaseInformation1.jpg';
import ReleaseInformation2 from 'assets/image/ReleaseInformation2.jpg';
import ReleaseInformation3 from 'assets/image/ReleaseInformation3.jpg';
import Group64 from 'assets/image/Group64.png';
import Home from 'assets/svg/assets-home.svg?component';
import Student from 'assets/svg/assets-t-Student.svg?component';
import OutlinedProfile from 'assets/svg/assets-OutlinedProfile.svg?component';

const { SwiperItem } = Swiper;

interface IListItemData<T = any> {
  listProps?: object;
  style?: object;
  title?: string;
  description?: string;
  image?: string;
  action?: React.ReactElement;
  listItemProps?: T;
  id?: T;
  name?: string;
  educationalId: number;
  educationalName: string;
  educationalDisciplines: number;
}

interface IListData<T = any> {
  active?: T;
  broadsideList?: IListItemData[];
}

interface IData extends React.HTMLAttributes<HTMLElement> {
  broadsideList?: IListData;
}

type Advert = {
  id: number;
  advertName: string;
  advertContent: string;
  advertType: string | null;
  advertCover: number;
  advertCoverUrl: string;
  updateTime: string;
  isOpen: number;
};

const BasicList: React.FC<IData> = () => {
  const dispatch = useAppDispatch();
  const { list, pageNum, carouselImageList } = useAppSelector(selectAdvertisingSpace);
  const [imgList, setImgList] = useState<any>([]);

  const getList = async () => {
    await dispatch(getCarouselImageList());
    setImgList(carouselImageList);
  };

  useEffect(() => {
    if (pageNum === 1) {
      const openAdverts = (list as Advert[]).filter((advert) => advert.isOpen === 1);
      setImgList(openAdverts);
    } else {
      getList();
    }
  }, [pageNum, list]);
  return (
    <React.Fragment>
      <h3>广告位预览</h3>
      <div className={Style.app}>
        <div className={Style.swiper}>
          <Swiper duration={300} interval={2000}>
            {imgList.map((item: Advert) => (
              <SwiperItem key={item.id}>
                <div className={Style.SwiperItem}>
                  <img src={item.advertCoverUrl} alt='' />
                </div>
              </SwiperItem>
            ))}
          </Swiper>
          <div style={{ margin: '24px 16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div className={Style.askedQuestion}>
                <div>
                  <p>常见问题</p>
                  <p>什么是对口单招</p>
                </div>
                <img style={{ imageRendering: 'pixelated' }} width={'40px'} src={Component28} alt='' />
              </div>
              <div className={Style.communicationGroup}>
                <div>
                  <p>家长交流群</p>
                  <p>免费领真题哦</p>
                </div>
                <img style={{ imageRendering: 'pixelated' }} width={'40px'} src={Component30} alt='' />
              </div>
            </div>
            <div className={Style.title}>热门考试</div>
            <div className={Style.examItem}>
              <div>
                <img src={Group87} alt='' />
                <p>高职单招</p>
              </div>
              <div>
                <img src={Group84} alt='' />
                <p>对口单招</p>
              </div>
              <div>
                <img src={MaskGroup} alt='' />
                <p>艺术单招</p>
              </div>
              <div>
                <img src={Group88} alt='' />
                <p>体育单招</p>
              </div>
            </div>
            <div className={Style.title}>资讯发布</div>
            <div className={Style['release-information']}>
              <div className={Style['release-information-item']}>
                <div>
                  <img src={ReleaseInformation1} alt='' />
                  <img src={Group64} alt='' />
                </div>
              </div>
              <div className={Style['release-information-item']}>
                <div>
                  <img src={ReleaseInformation2} alt='' />
                  <img src={Group64} alt='' />
                </div>
              </div>
              <div className={Style['release-information-item']}>
                <div>
                  <img src={ReleaseInformation3} alt='' />
                  <img src={Group64} alt='' />
                </div>
              </div>
            </div>
          </div>
          <div className={Style.tabBar}>
            <div>
              <Home />
              <p>首页</p>
            </div>
            <div>
              <Student />
              <p>院校专业</p>
            </div>
            <div>
              <OutlinedProfile />
              <p>我的</p>
            </div>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
};

export default BasicList;
