import React, { useEffect } from 'react';
import classnames from 'classnames';
import { SelectTable } from './Select';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import BasicList from './components/BasicList';
import { useAppDispatch, useAppSelector } from 'modules/store';
import {
  getPrivateDomainExamList,
  selectPrivateDomainManage,
  clearPageState,
} from 'modules/operationManage/privateDomainManage';

const TreeTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const { sidebarList } = useAppSelector(selectPrivateDomainManage);

  useEffect(() => {
    dispatch(getPrivateDomainExamList(''));
    return () => {
      dispatch(clearPageState());
    };
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithColor, Style.content)}>
      <div className={Style.treeContent}>
        <BasicList broadsideList={sidebarList}></BasicList>
      </div>
      <div className={Style.tableContent}>
        <SelectTable />
      </div>
    </div>
  );
};

export default TreeTable;
