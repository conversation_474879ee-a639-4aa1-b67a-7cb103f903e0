import request from 'utils/request/index';

/**
 * 查询私域考试列表
 * @param params
 * @returns
 */
export const getPrivateDomainExamListApi = async () => {
  const result = await request.get({
    url: '/privateDomain/getPrivateDomainExamList',
  });
  return result;
};

export interface IGetPrivateDomainListApi {
  pageSize: number;
  pageNum: number;

  /** 考试学生角色id */
  examStudentRoleId: number;

  /** 私域类型(1-群聊 2-咨询师) */
  type?: number;

  /** 群名/咨询师昵称/咨询师姓名 */
  vague: string;
}

/**
 * 条件分页查询群/咨询师列表
 * @param params
 * @returns
 */
export const getPrivateDomainListApi = async (params: IGetPrivateDomainListApi) => {
  const result = await request.get({
    url: '/privateDomain/getPrivateDomainList',
    params,
  });
  return result;
};

/**
 *  删除群/咨询师信息
 * @param params
 * @returns
 */
export const deletePrivateDomainApi = async (privateDomainId: number | string) => {
  const result = await request.delete({
    url: `/privateDomain/deletePrivateDomain/${privateDomainId}`,
  });
  return result;
};

export interface IAddOrUpdatePrivateDomainApi {
  /** 私域id 修改传 */
  privateDomainId: number;

  /** 考试学生角色id 必传 */
  examStudentRoleId: number;

  /** 群名/咨询师昵称 */
  name: string;

  /** 真实姓名 */
  actualName?: string;

  /** 群二维码图片id/咨询师二维码图片id */
  qrCodeId?: string;

  /** 私域类型(1-群聊 2-咨询师)必传 */
  type?: string;
}

/**
 * 新增/修改，群/咨询师信息
 * @param params
 * @returns
 */
export const addOrUpdatePrivateDomainApi = async (params: IAddOrUpdatePrivateDomainApi) => {
  const result = await request.post({
    url: `/privateDomain/addOrUpdatePrivateDomain`,
    params,
  });
  return result;
};

export interface IOpenOrClosePrivateDomainApi {
  /** 私域id */
  privateDomainId: number;

  /** 考试学生角色id 必传 */
  examStudentRoleId: number;

  /** 是否开启(1:开启，0:关闭) */
  isOpen: string;
}

/**
 * 开启/关闭，群/咨询师信息
 * @param params
 * @returns
 */
export const openOrClosePrivateDomainApi = async (params: IOpenOrClosePrivateDomainApi) => {
  const result = await request.post({
    url: `/privateDomain/openOrClosePrivateDomain`,
    params,
  });
  return result;
};
