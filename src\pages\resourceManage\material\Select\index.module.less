.upload {
  :global {
    .t-upload__dragger {
      width: 100%;
    }
  }
}

.stem {
  img {
    width: 60px;
    image-rendering: pixelated;
  }

  p {
    margin: 0;
  }
}

// ::v-deep .pg-viewer-wrapper {
//   min-height: 500px !important;
// }

:global {
  .t-upload__file-name {
    display: inline-block;
    max-width: 300px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
