import { enumFormData } from 'utils/enumFormData';

enum MajorType {
  Comprehensive = 1,
  ScienceEngineering = 2,
  Humanities = 3,
  Arts = 4,
  Normal = 5,
  Sports = 6,
}

// 自定义label映射到枚举
const MajorLabels: { [key in MajorType]: string } = {
  [MajorType.Comprehensive]: '综合类',
  [MajorType.ScienceEngineering]: '理工类',
  [MajorType.Humanities]: '文史类',
  [MajorType.Arts]: '艺术类',
  [MajorType.Normal]: '师范类',
  [MajorType.Sports]: '体育类',
};

export const MAJOR_TYPE = enumFormData(MajorType, MajorLabels);

enum SchoolType {
  Public = 1,
  Private = 2,
}

// 自定义label映射到枚举
const SchoolLabels: { [key in SchoolType]: string } = {
  [SchoolType.Public]: '公办院校',
  [SchoolType.Private]: '民办院校',
};

export const SCHOOL_TYPE = enumFormData(SchoolType, SchoolLabels);
