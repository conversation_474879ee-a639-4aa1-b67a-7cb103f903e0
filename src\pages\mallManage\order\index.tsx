/* eslint-disable no-nested-ternary */
import React, { memo, useEffect, useRef, useState } from 'react';
import { Row, Space, TableProps } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectMallManageOrder, getOrderList } from 'modules/mallManage/order';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IRef } from 'components/Form';
import { IGetResourceLecturerListBo } from 'types/resourceManage';
import PermissionButton from 'components/PermissionButton';
import { Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { useNavigate } from 'react-router-dom';

const OrderTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total } = useAppSelector(selectMallManageOrder);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetResourceLecturerListBo = { pageNum, pageSize };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'serial-number',
    'orderNo',
    'price',
    'status',
    'goodsName',
    'goodsType',
    'goodsAttr',
    'singlePerson',
    'orderTime',
    'payTradeNo',
    'paymentTime',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['singlePerson', 'orderTime', 'payTradeNo', 'paymentTime'];
  const statusList = [
    {
      value: 1,
      label: '待支付',
    },
    {
      value: 2,
      label: '已支付',
    },
    {
      value: 3,
      label: '已取消',
    },
  ];
  const goodsTypeList = [
    {
      value: 1,
      label: '视频课程',
    },
    {
      value: 2,
      label: '电子教辅',
    },
  ];

  const columns: TableProps['columns'] = [
    {
      colKey: 'serial-number',
      width: 80,
      title: '序号',
    },
    {
      colKey: 'orderNo',
      title: '订单号',
      width: 240,
    },
    {
      colKey: 'price',
      title: '订单金额/元',
      width: 120,
    },
    {
      colKey: 'status',
      title: '订单状态',
      width: 120,
      cell({ row }) {
        return <span>{row.status === 1 ? '待支付' : row.status === 2 ? '已支付' : '已取消'}</span>;
      },
    },
    {
      colKey: 'goodsName',
      title: '商品名称',
      cell({ row }) {
        return <span>{row?.ordersDetailVo?.goodsName}</span>;
      },
      width: 200,
    },
    {
      colKey: 'goodsType',
      title: '商品类型',
      cell({ row }) {
        return <span>{row?.ordersDetailVo?.goodsType === 1 ? '电子教辅' : '视频课程'}</span>;
      },
      width: 120,
    },
    {
      colKey: 'goodsAttr',
      title: '商品属性',
      cell({ row }) {
        return <span>{row?.ordersDetailVo?.goodsAttr === 1 ? '单体' : '套装'}</span>;
      },
      width: 120,
    },
    {
      colKey: 'singlePerson',
      title: '下单人',
    },
    {
      colKey: 'orderTime',
      title: '下单时间',
      width: 200,
    },
    {
      colKey: 'payTradeNo',
      title: '支付流水号',
      width: 280,
    },
    {
      colKey: 'paymentTime',
      title: '支付时间',
      width: 200,
    },
    {
      colKey: 'op',
      title: '操作',
      width: 120,
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['mall:goods:query']}
              onClick={() =>
                navigate(`/mall/goods/details/${row.ordersDetailVo.goodsType}`, {
                  state: {
                    goodsId: row.ordersDetailVo.goodsId,
                    backPath: '/mall/order',
                    backHeader: '商品管理-订单管理',
                  },
                })
              }
            >
              商品详情
            </PermissionButton>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    dispatch(getOrderList(searchParams));
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getOrderList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '订单号',
            field: 'orderNo',
          },
          {
            type: 'select',
            label: '订单状态',
            field: 'status',
            valueField: 'value',
            nameField: 'label',
            options: statusList,
          },
          {
            type: 'input',
            label: '商品名称',
            field: 'goodsName',
          },
          {
            type: 'select',
            label: '商品类型',
            field: 'goodsType',
            valueField: 'value',
            nameField: 'label',
            options: goodsTypeList,
          },
          {
            type: 'input',
            label: '下单人',
            field: 'generic',
          },
          {
            type: 'datePicker',
            label: '下单时间',
            field: 'orderTimeArr',
            pickerPlaceholder: ['开始时间', '结束时间'],
            isTime: true,
          },
          {
            type: 'datePicker',
            label: '支付时间',
            field: 'payTimeArr',
            pickerPlaceholder: ['开始时间', '结束时间'],
            isTime: true,
          },
        ]}
      />

      <Row justify='space-between'>
        <Space></Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'orderId',
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getOrderList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(OrderTable);
