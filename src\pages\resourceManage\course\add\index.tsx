import React, { memo, useState } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import '../index.less';
import { Tabs, TabValue } from 'tdesign-react';
import BasicInfo from './BasicInfo';
import DirInfo from './DirInfo';
import { Back } from 'components';

const { TabPanel } = Tabs;

const AddCourse: React.FC = () => {
  const [tabValue, setTabValue] = useState<TabValue>(1);
  const [courseId, setCourseId] = useState(0);

  const handleChange = (val: TabValue) => {
    setTabValue(val);
  };

  const handleSuccess = (id: number) => {
    setCourseId(id);
    setTabValue(2);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Back path='/resource/course' header='资源管理-课程管理' />
      <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)} style={{ height: '100%' }}>
        <Tabs className='addEditTabsContainer' value={tabValue} onChange={handleChange}>
          <TabPanel className='' label='基本信息' value={1} disabled={courseId !== 0}>
            {tabValue === 1 && <BasicInfo onSuccess={handleSuccess} />}
          </TabPanel>
          <TabPanel label='目录信息' value={2} disabled={courseId === 0}>
            {tabValue === 2 && <DirInfo courseId={courseId} />}
          </TabPanel>
        </Tabs>
      </div>
    </div>
  );
};

export default memo(AddCourse);
