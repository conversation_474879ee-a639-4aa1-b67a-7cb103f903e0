import React, { useEffect } from 'react';
import classnames from 'classnames';
import { SelectTable } from './Select';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import BasicList from './components/BasicList';
import { useAppDispatch, useAppSelector } from 'modules/store';
import {
  selectExamFrequentlyAsked,
  getEducationalInstitutionsNameAndId,
  clearPageState,
} from 'modules/operationManage/examFrequentlyAsked';

const TreeTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const pageState = useAppSelector(selectExamFrequentlyAsked);
  const { broadsideList } = pageState;

  useEffect(() => {
    dispatch(getEducationalInstitutionsNameAndId(''));
    return () => {
      dispatch(clearPageState());
    };
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithColor, Style.content)}>
      <div className={Style.treeContent}>
        <BasicList broadsideList={broadsideList}></BasicList>
      </div>
      <div className={Style.tableContent}>
        <SelectTable broadsideList={broadsideList} />
      </div>
    </div>
  );
};

export default TreeTable;
