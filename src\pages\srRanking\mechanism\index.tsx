/* eslint-disable no-nested-ternary */
import React, { memo, useEffect, useRef, useState } from 'react';
import { Popconfirm, Row, Space, TableProps } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  selectSRRankingMechanism,
  getMechanismList,
  dragSortMechanism,
  deleteMechanism,
} from 'modules/srRanking/mechanism';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IRef } from 'components/Form';
import { IGetResourceLecturerListBo } from 'types/resourceManage';
import PermissionButton from 'components/PermissionButton';
import { Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { useNavigate } from 'react-router-dom';
import { MoveIcon } from 'tdesign-icons-react';
import { IDragSort } from 'types';

const MechanismTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total, deleteMechanismLoading } = useAppSelector(selectSRRankingMechanism);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetResourceLecturerListBo = { pageNum, pageSize };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'drag',
    'sort',
    'name',
    'shortName',
    'addressList',
    'addressNum',
    'serveNum',
    'teacherNum',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['orderNo', 'shortName', 'addressList', 'updateTime', 'updateBy', 'createTime', 'createBy'];

  const handleDelete = async (id: number) => {
    const { type } = await dispatch(deleteMechanism(id));

    if (type.endsWith('fulfilled')) {
      dispatch(getMechanismList(searchParams));
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'drag',
      title: '排序',
      cell: () => <MoveIcon />,
      width: 50,
    },
    {
      colKey: 'sort',
      title: '排名',
      width: 120,
    },
    {
      colKey: 'name',
      title: '机构名称',
    },
    {
      colKey: 'shortName',
      title: '机构简称',
    },
    {
      colKey: 'addressList',
      title: '校区地址',
      cell({ row }) {
        return row.addressList && row.addressList.join('、');
      },
    },
    {
      colKey: 'addressNum',
      title: '校区',
      width: 120,
    },
    {
      colKey: 'serveNum',
      title: '精选服务',
      width: 120,
    },
    {
      colKey: 'teacherNum',
      title: '师资',
      width: 120,
    },
    {
      colKey: 'updateTime',
      title: '修改时间',
    },
    {
      colKey: 'updateBy',
      title: '修改人',
    },
    {
      colKey: 'createTime',
      title: '创建时间',
    },
    {
      colKey: 'createBy',
      title: '创建人',
    },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['srRanking:mechanism:query']}
              onClick={() => navigate('/srRanking/mechanism/details', { state: { id: row.id } })}
            >
              详情
            </PermissionButton>
            <PermissionButton
              permissions={['srRanking:mechanism:edit']}
              onClick={() => navigate('/srRanking/mechanism/edit', { state: { id: row.id } })}
            >
              编辑
            </PermissionButton>
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleDelete(row.id)}>
              <>
                <PermissionButton
                  permissions={['srRanking:mechanism:remove']}
                  theme='danger'
                  loading={deleteMechanismLoading}
                >
                  删除
                </PermissionButton>
              </>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  const onDragSort: TableProps['onDragSort'] = async ({ current, targetIndex }) => {
    try {
      const bo: IDragSort = {
        id: current.id,
        position: pageNum === 1 ? targetIndex + 1 : (pageNum - 1) * pageSize + (targetIndex + 1),
      };

      const data = await dispatch(dragSortMechanism(bo)).unwrap();

      if (data) {
        dispatch(getMechanismList(searchParams));
      }
    } catch (error) {
      throw new Error('排序失败');
    }
  };

  useEffect(() => {
    dispatch(getMechanismList(searchParams));
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getMechanismList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '机构名称',
            field: 'name',
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            permissions={['srRanking:mechanism:add']}
            content='添加机构'
            variant='outline'
            onClick={() => navigate('/srRanking/mechanism/add')}
          />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'id',
          dragSort: 'row-handler',
          onDragSort,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getMechanismList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(MechanismTable);
