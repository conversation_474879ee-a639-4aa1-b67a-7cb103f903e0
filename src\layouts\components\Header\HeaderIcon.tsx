import React, { memo, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button, Popup, Dropdown, Space, Switch, Avatar } from 'tdesign-react';
import { Icon, SettingIcon, PoweroffIcon, UserCircleIcon } from 'tdesign-icons-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { toggleSetting, selectGlobal, switchTheme, switchColor } from 'modules/global';
import { logout, selectUserInfo, getUserInfo } from 'modules/user';
import Style from './HeaderIcon.module.less';
import { ETheme } from 'types/index.d';

const { DropdownMenu, DropdownItem } = Dropdown;

export default memo(() => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const userInfo = useAppSelector(selectUserInfo);
  const renderActiveContent = () => <Icon name='sunny' />;
  const renderInactiveContent = () => <Icon name='moon' style={{ color: '#ffc107' }} />;
  const globalState = useAppSelector(selectGlobal);
  const location = useLocation();
  useEffect(() => {
    if (location.pathname !== '/login' && Object.keys(userInfo).length === 0) {
      dispatch(getUserInfo());
    }
  }, [dispatch, location]);

  const handlePersonalCenter = () => {
    navigate('/user/index');
  };
  const handleLogout = async () => {
    dispatch(logout());
    navigate('/login');
  };

  const switchMode = (val: boolean) => {
    dispatch(switchTheme(val ? ETheme.light : ETheme.dark));
    dispatch(switchColor(globalState.color));
  };

  return (
    <Space align='center'>
      <Avatar
        image={userInfo.avatar ?? 'https://tdesign.gtimg.com/site/avatar.jpg'}
        hideOnLoadFailed={false}
      // style={{ marginRight: '10px' }}
      />
      <Dropdown trigger={'click'}>
        <Button variant='text' className={Style.dropdown}>
          {/* <Icon name='user-circle' className={Style.icon} /> */}
          <span className={Style.text}>{userInfo.userName ?? '未登录'}</span>
          <Icon name='chevron-down' className={Style.icon} />
        </Button>
        <DropdownMenu>
          <DropdownItem value={0} onClick={handlePersonalCenter}>
            <div className={Style.dropItem}>
              <UserCircleIcon />
              <span>个人中心</span>
            </div>
          </DropdownItem>
          <DropdownItem value={1} onClick={handleLogout}>
            <div className={Style.dropItem}>
              <PoweroffIcon />
              <span>退出登录</span>
            </div>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Popup content='切换模式' placement='bottom' showArrow destroyOnClose>
        <Switch
          size='large'
          defaultValue
          label={[renderActiveContent(), renderInactiveContent()]}
          onChange={switchMode}
        />
      </Popup>
      <Popup content='页面设置' placement='bottom' showArrow destroyOnClose>
        <Button
          className={Style.menuIcon}
          shape='square'
          size='large'
          variant='text'
          onClick={() => dispatch(toggleSetting())}
          icon={<SettingIcon />}
        />
      </Popup>
    </Space>
  );
});
