import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { previewGoods, selectMallManageGoods, clearPageState, setFileUrl } from 'modules/mallManage/goods';
import { IPreviewGoodsBo } from 'types/mallManage';
import { useAppSelector, useAppDispatch } from 'modules/store';
import DocumentPreview from 'components/DocumentPreview';
import { Loading } from 'tdesign-react';
import { strIsNull } from 'utils/tool';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back } from 'components';

const PreviewGoods: React.FC = () => {
  const dispatch = useAppDispatch();
  const { previewGoodsLoading, fileUrl } = useAppSelector(selectMallManageGoods);
  const location = useLocation();
  const { goodsId, params } = location.state || {};
  const [routerParams, setRouterParams] = useState({
    path: '/mall/goods',
    header: '商品管理-电子教辅',
    params: {
      type: 2,
    },
  });

  useEffect(() => {
    if (!strIsNull(goodsId)) {
      const bo: IPreviewGoodsBo = {
        goodsId,
      };

      dispatch(previewGoods(bo));
    }
  }, [goodsId]);

  useEffect(() => {
    if (!strIsNull(params)) {
      dispatch(setFileUrl(params.url));
      setRouterParams(params);
    }
  }, [params]);

  useEffect(
    () => () => {
      dispatch(clearPageState());
    },
    [],
  );

  return (
    <Loading loading={previewGoodsLoading} showOverlay>
      <Back path={routerParams.path} header={routerParams.header} params={routerParams.params} />

      <div className={classnames(CommonStyle.pageWithColor)}>
        {!strIsNull(fileUrl) && <DocumentPreview fileUrl={fileUrl} />}
      </div>
    </Loading>
  );
};

export default PreviewGoods;
