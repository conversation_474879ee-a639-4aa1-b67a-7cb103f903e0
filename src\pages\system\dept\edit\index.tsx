import React, { memo, useEffect, useRef } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back } from 'components';
import {
  Button,
  Form,
  FormInstanceFunctions,
  FormProps,
  Input,
  Loading,
  Space,
  TreeSelect,
  InputNumber,
  Radio,
} from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import {
  selectSystemDept,
  editSystemDept,
  getSystemDeptExcludeList,
  getSystemDeptDetailsInfo,
} from 'modules/system/dept';
import { useLocation, useNavigate } from 'react-router-dom';
import { IEditDeptBo } from 'types/system';
import { strIsNull } from 'utils/tool';

const { FormItem } = Form;

const EditDept: React.FC = () => {
  const location = useLocation();
  const { deptId } = location.state || {};
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { infoLoading, addEditLoading, excludeList, info } = useAppSelector(selectSystemDept);
  const formRef = useRef<FormInstanceFunctions>();

  const INITIAL_DATA = {
    parentId: '',
    deptName: '',
    orderNum: '',
    leader: '',
    phone: '',
    email: '',
    status: '0',
  };

  const rules: FormProps['rules'] = {
    parentId: [
      {
        required: true,
        message: '上级部门不能为空',
        trigger: 'all',
      },
    ],
    deptName: [
      {
        required: true,
        message: '部门名称不能为空',
        trigger: 'all',
      },
    ],
    orderNum: [
      {
        required: true,
        message: '显示排序不能为空',
        trigger: 'all',
      },
    ],
    phone: [
      {
        pattern: /^((\+|00)86)?1[3-9]\d{9}$/,
        message: '联系方式错误',
        trigger: 'all',
      },
    ],
    email: [
      {
        email: { ignore_max_length: true },
        message: '邮箱格式错误',
        trigger: 'all',
      },
    ],
  };

  useEffect(() => {
    if (!strIsNull(deptId)) {
      dispatch(getSystemDeptExcludeList(deptId));
      dispatch(getSystemDeptDetailsInfo(deptId));
    }
  }, [deptId]);

  useEffect(() => {
    if (!strIsNull(info)) {
      formRef.current?.setFieldsValue(info);
    }
  }, [info]);

  const handleReset: FormProps['onReset'] = () => {
    if (!strIsNull(deptId)) {
      dispatch(getSystemDeptExcludeList(deptId));
      dispatch(getSystemDeptDetailsInfo(deptId));
    }
  };

  const handleSubmit: FormProps['onSubmit'] = async ({ validateResult, fields }) => {
    if (validateResult === true) {
      const bo: IEditDeptBo = {
        ...fields,
        deptId,
      };

      await dispatch(editSystemDept(bo));
    }
  };

  const handleCancel = () => {
    navigate('/system/dept');
  };

  return (
    <Loading loading={infoLoading} showOverlay style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Back path='/system/dept' header='系统管理-部门管理' />
      <div className={classnames(CommonStyle.pageWithColor)}>
        <Form
          ref={formRef}
          initialData={INITIAL_DATA}
          resetType='initial'
          rules={rules}
          colon
          className={classnames(CommonStyle.commonSubjectForm)}
          onSubmit={handleSubmit}
          onReset={handleReset}
        >
          {info.parentId !== 0 && (
            <FormItem label='上级部门' name='parentId'>
              <TreeSelect
                data={excludeList}
                keys={{ value: 'deptId', label: 'deptName' }}
                filterable
                clearable
                treeProps={{ valueMode: 'onlyLeaf' }}
                placeholder='请选择上级部门'
              />
            </FormItem>
          )}
          <FormItem label='部门名称' name='deptName'>
            <Input placeholder='请输入部门名称' clearable />
          </FormItem>
          <FormItem label='显示排序' name='orderNum'>
            <InputNumber placeholder='请输入显示排序' theme='column' min={0} style={{ width: '100%' }} />
          </FormItem>
          <FormItem label='负责人' name='leader'>
            <Input placeholder='请输入负责人' clearable />
          </FormItem>
          <FormItem label='联系电话' name='phone'>
            <Input placeholder='请输入联系电话' clearable />
          </FormItem>
          <FormItem label='邮箱' name='email'>
            <Input placeholder='请输入邮箱' clearable />
          </FormItem>
          <FormItem label='部门状态' name='status'>
            <Radio.Group>
              <Radio value='0'>正常</Radio>
              <Radio value='1'>停用</Radio>
            </Radio.Group>
          </FormItem>
          <FormItem className={classnames(CommonStyle.commonSubjectFormBtn)}>
            <Space>
              <Button loading={addEditLoading} type='submit' theme='primary'>
                提交
              </Button>
              <Button theme='default' onClick={handleCancel}>
                取消
              </Button>
              <Button type='reset' theme='warning'>
                重置
              </Button>
            </Space>
          </FormItem>
        </Form>
      </div>
    </Loading>
  );
};

export default memo(EditDept);
