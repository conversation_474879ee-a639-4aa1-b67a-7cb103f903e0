import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getSubjectListApi,
  getQuestionListApi,
  deleteQuestionApi,
  addOrUpdateQuestionApi,
  deleteSubject<PERSON>pi,
  addOrUpdateSubject<PERSON>pi,
  questionDragSortApi,
  IQuestionDragSortApi,
} from 'api';
import type { ExamAllListType, IAddOrUpdateSubjectApi } from 'api';
import { notification } from 'tdesign-react';

const namespace = 'resourceManage/question';

interface IQuestionAnalysis {
  oid: string;
  opt: string;
  optionContent: string;
}

interface ISubjectBo {
  subjectName?: string | null | undefined;
  subjectId?: number | null | undefined;
}

interface IInitialState {
  loading: boolean;
  formLoading: boolean;
  pageNum: number;
  current: number;
  pageSize: number;
  total: number;
  active: number | undefined;
  contractList: ExamAllListType[];
  broadsideList: Array<any>;
  tablist: Array<any>;
  answer: string;
  answerId: number | string;
  questionId: string;
  subjectId: number;
  stem: string;
  optionBoList: IQuestionAnalysis[];
  questionAnalysis: string;
  questionProvenance: string;
  deleteQuestionSubjectLoading: boolean;
}

const initialState: IInitialState = {
  loading: true,
  formLoading: false,
  pageNum: 1,
  current: 1,
  pageSize: 10,
  total: 0,
  contractList: [],
  active: undefined,
  broadsideList: [],
  tablist: [],
  answer: '',
  answerId: '',
  questionId: '',
  subjectId: 0,
  stem: '',
  questionAnalysis: '',
  optionBoList: [],
  questionProvenance: '',
  deleteQuestionSubjectLoading: false,
};

export const getQuestionList = createAsyncThunk(`${namespace}/getQuestionList`, async (params: any) => {
  if (params.subjectId) {
    const {
      data: { rows, total },
    } = await getQuestionListApi(params);
    return {
      list: rows,
      total,
      pageSize: params.pageSize,
      pageNum: params.pageNum,
    };
  }
  return {
    list: [],
    total: 0,
    pageSize: params.pageSize,
    pageNum: params.pageNum,
  };
});

export const getList = createAsyncThunk(`${namespace}/getList`, async (subjectBo: ISubjectBo, { getState }) => {
  const {
    data: { rows },
  } = await getSubjectListApi(subjectBo.subjectName);

  const state = getState() as RootState;
  const currentActive = state.resourceManageQuestion.active;

  const obj: any = {
    rows,
  };
  if (subjectBo.subjectId) {
    obj.active = subjectBo.subjectId;
  } else if (!currentActive) {
    obj.active = rows[0].subjectId;
  }
  return obj;
});

export const addOrUpdateQuestion = createAsyncThunk(`${namespace}/addOrUpdateQuestion`, async (params) => {
  await addOrUpdateQuestionApi(params);
});

export const deleteQuestion = createAsyncThunk(
  `${namespace}/deleteQuestion`,
  async (params: object | undefined, { getState, dispatch }) => {
    const {
      resourceManageQuestion: { pageNum, pageSize, active },
    } = getState() as RootState;
    try {
      await deleteQuestionApi(params);
      await dispatch(
        getQuestionList({
          pageNum,
          pageSize,
          subjectId: active,
        }),
      );
      // 删除成功提示
      notification.success({ content: '题目删除成功！' });
    } catch (error) {
      // 删除失败提示
      notification.error({ content: '题目删除失败，请重试！' });
      throw error;
    }
  },
);

export const deleteSubject = createAsyncThunk(`${namespace}/deleteSubject`, async (id: number, { dispatch }) => {
  try {
    await deleteSubjectApi(id);
    dispatch(getList({}));
    // 删除成功提示
    await notification.success({ content: '科目删除成功！' });
  } catch (error) {
    // 删除失败提示
    await notification.error({ content: '科目删除失败，请重试！' });
    throw error;
  }
});

export const addOrUpdateSubject = createAsyncThunk(
  `${namespace}/addOrUpdateSubject`,
  async (params: IAddOrUpdateSubjectApi, { dispatch }) => {
    const { data } = await addOrUpdateSubjectApi(params);
    await dispatch(getList({}));

    return {
      active: data,
    };
  },
);

export const questionDragSort = createAsyncThunk(
  `${namespace}/questionDragSort`,
  async (params: IQuestionDragSortApi, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { pageNum, pageSize, active } = state.resourceManageQuestion;
    await questionDragSortApi(params);

    await dispatch(
      getQuestionList({
        pageNum,
        pageSize,
        subjectId: active,
      }),
    );
  },
);

const resourceManageQuestionSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,

    setActive: (state, action: PayloadAction<number>) => {
      state.active = action.payload;
    },

    /** 添加题干 */
    setStem: (state, action) => {
      state.stem = action.payload;
    },

    /** 题目选项 */
    setOption: (state, action) => {
      state.optionBoList = action.payload;
    },

    /** 添加题目解析 */
    setQuestionAnalysis: (state, action) => {
      state.questionAnalysis = action.payload;
    },

    /** 添加题目出处 */
    setQuestionProvenance: (state, action) => {
      state.questionProvenance = action.payload;
    },

    /** 添加正确答案 */
    setAnswer: (state, action) => {
      state.answer = action.payload.opt;
      state.answerId = action.payload.oid;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getList.fulfilled, (state, action) => {
        state.loading = false;
        state.broadsideList = action.payload?.rows;

        if (action.payload?.active) {
          state.active = action.payload?.active;
        }
      })
      .addCase(getList.rejected, (state) => {
        state.loading = false;
      })
      .addCase(deleteSubject.pending, (state) => {
        state.deleteQuestionSubjectLoading = true;
      })
      .addCase(deleteSubject.fulfilled, (state) => {
        state.active = undefined;
        state.deleteQuestionSubjectLoading = false;
      })
      .addCase(deleteSubject.rejected, (state) => {
        state.deleteQuestionSubjectLoading = false;
      })
      .addCase(getQuestionList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getQuestionList.fulfilled, (state, action) => {
        state.loading = false;
        state.tablist = action.payload?.list;
        state.total = action.payload?.total;
        state.pageSize = action.payload?.pageSize;
        state.pageNum = action.payload?.pageNum;
      })
      .addCase(getQuestionList.rejected, (state) => {
        state.loading = false;
      })
      .addCase(addOrUpdateSubject.fulfilled, (state, action) => {
        state.active = action.payload?.active;
      });
  },
});

export const { clearPageState, setActive, setAnswer, setStem, setOption, setQuestionAnalysis, setQuestionProvenance } =
  resourceManageQuestionSlice.actions;

export const selectResourceManageQuestionSlice = (state: RootState) => state.resourceManageQuestion;

export default resourceManageQuestionSlice.reducer;
