import React, { memo, useEffect, useRef, useState } from 'react';
import { Form, Row, Col, Input, Button, Textarea, MessagePlugin, Space, InputNumber, Loading } from 'tdesign-react';
import { DeleteIcon, AddIcon } from 'tdesign-icons-react';
import classnames from 'classnames';
import { SubmitContext, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';

import Upload from 'components/Upload';
import Back from 'components/Back';
import { useLocation, useNavigate } from 'react-router-dom';
import { addOrUpdateMajors, selectSpeciality } from 'modules/directory/speciality';
import { useAppDispatch, useAppSelector } from 'modules/store';

const { FormItem } = Form;

const ossUrl = '/oss/uploadFile';

export default memo(() => {
  const location = useLocation();
  const { row, type } = location.state || { row: {} };
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>();
  const { active, formLoading } = useAppSelector(selectSpeciality);

  const itemData = {
    employmentDirectionId: undefined,
    jobName: undefined,
    jobDesc: undefined,
    minAvgMoney: undefined,
    maxAvgMoney: undefined,
  };

  const [formItemList, setFormItemList] = useState([itemData]);

  const addFormData = {
    categoryId: active,
    majorsId: undefined,
    majorsName: undefined,
    majorsDesc: undefined,
    majorsIntroduce: undefined,
    majorsBackgroundImgId: undefined,
    employmentDirectionBoList: [itemData],
    majorUndergraduate: undefined,
  } as const;

  const { employmentDirectionVoList, majorsBackgroundImgUrl, ...rest } = row || addFormData;
  const editFormData = {
    employmentDirectionBoList: employmentDirectionVoList || [],
    majorsBackgroundImgUrl,
    ...rest,
  };
  const INITIAL_DATA = type === 'edit' ? editFormData : addFormData;

  useEffect(() => {
    if (type === 'edit') {
      setFormItemList(() =>
        Array.isArray(employmentDirectionVoList) && employmentDirectionVoList.length === 0
          ? [itemData]
          : employmentDirectionVoList,
      );
    }
  }, [employmentDirectionVoList]);

  const onSuccess = ({
    file: {
      response: {
        data: { id },
      },
    },
  }: {
    file: {
      response: {
        data: { id: number };
      };
    };
  }) => {
    formRef.current?.setFieldsValue?.({ majorsBackgroundImgId: id });
  };

  const onSubmit = async (e: SubmitContext) => {
    if (e.validateResult === true) {
      const params = formRef.current?.getFieldsValue?.(true);
      if (!params) return;
      const employmentDirectionBoList = formItemList.map((_, index) => ({
        employmentDirectionId: index + 1,
        jobName: params[`employmentDirectionBoList[${index}].jobName`],
        jobDesc: params[`employmentDirectionBoList[${index}].jobDesc`],
        minAvgMoney: params[`employmentDirectionBoList[${index}].minAvgMoney`],
        maxAvgMoney: params[`employmentDirectionBoList[${index}].maxAvgMoney`],
      }));

      formItemList.forEach((_, index) => {
        delete params[`employmentDirectionBoList[${index}].jobName`];
        delete params[`employmentDirectionBoList[${index}].jobDesc`];
        delete params[`employmentDirectionBoList[${index}].minAvgMoney`];
        delete params[`employmentDirectionBoList[${index}].maxAvgMoney`];
      });

      const finalParams = {
        ...params,
        employmentDirectionBoList,
        categoryId: active,
        majorsId: type === 'edit' ? row.majorsId : undefined,
      };
      await dispatch(addOrUpdateMajors(finalParams));
      MessagePlugin.success(`${type === 'edit' ? '修改' : '添加'}成功！`);
      navigate('/directory/speciality');
    }
  };

  const addTtem = () => {
    const newList = INITIAL_DATA.employmentDirectionBoList.concat([itemData]);
    INITIAL_DATA.employmentDirectionBoList = newList;
    setFormItemList((i) => i.concat([itemData]));
  };

  const deleteItem = (i: number) => {
    if (formItemList.length === 1) return MessagePlugin.warning('至少有一项就业方向');
    const newList = [...formItemList];
    newList.splice(i, 1);
    INITIAL_DATA.employmentDirectionBoList.splice(i, 1);
    setFormItemList(newList);
    return false;
  };

  const employmentDirection = JSON.parse(JSON.stringify(INITIAL_DATA.employmentDirectionBoList)) || formItemList;
  const EmploymentDirection = () =>
    formItemList &&
    formItemList.map((t, i) => (
      <Col key={i} span={12} className={Style.dateCol}>
        <Space>
          <Row gutter={[42, 20]}>
            <Col span={5}>
              <FormItem
                label={`就业方向${i + 1}`}
                name={`employmentDirectionBoList[${i}].jobName`}
                initialData={employmentDirection[i]?.jobName}
                rules={[{ required: i === 0, message: '就业方向必填', type: 'error' }]}
              >
                <Input placeholder='请输入内容' />
              </FormItem>
            </Col>
            <Col span={7} className={Style.dateCol}>
              <FormItem label='岗位平均月薪' requiredMark={true}>
                <Space style={{ alignItems: 'center' }}>
                  <FormItem
                    style={{ marginRight: 0 }}
                    name={`employmentDirectionBoList[${i}].minAvgMoney`}
                    initialData={employmentDirection[i]?.minAvgMoney}
                    rules={[{ required: true, message: '最小平均月薪必填', type: 'error' }]}
                  >
                    <InputNumber theme='normal' allowInputOverLimit={false} min={0} placeholder='请输入最小平均月薪' />
                  </FormItem>
                  <span>—</span>
                  <FormItem
                    name={`employmentDirectionBoList[${i}].maxAvgMoney`}
                    initialData={employmentDirection[i]?.maxAvgMoney}
                    rules={[{ required: true, message: '最大平均月薪必填', type: 'error' }]}
                  >
                    <InputNumber theme='normal' allowInputOverLimit={false} min={0} placeholder='请输入最大平均月薪' />
                  </FormItem>
                  <span style={{ marginRight: 20 }}>(k)</span>
                </Space>
              </FormItem>
            </Col>
            <Col span={12} className={Style.dateCol}>
              <FormItem name={`employmentDirectionBoList[${i}].jobDesc`} initialData={employmentDirection[i]?.jobDesc}>
                <Textarea placeholder='请输入备注' />
              </FormItem>
            </Col>
          </Row>
          <Space style={{ marginTop: '30px', marginLeft: '30px' }}>
            <Button shape='circle' onClick={addTtem} icon={<AddIcon />} />
            <Button shape='circle' onClick={() => deleteItem(i)} icon={<DeleteIcon />} />
          </Space>
        </Space>
      </Col>
    ));

  return (
    <>
      <Loading loading={formLoading} showOverlay>
        <Back path='/directory/speciality' header='专业目录'></Back>
        <div className={classnames(CommonStyle.pageWithColor)}>
          <div className={Style.formContainer}>
            <Form ref={formRef} onSubmit={onSubmit} labelWidth={100} labelAlign='top'>
              <Row gutter={[42, 34]}>
                <Col span={12}>
                  <FormItem
                    label='专业名称'
                    name='majorsName'
                    initialData={INITIAL_DATA.majorsName}
                    rules={[{ required: true, message: '专业名称必填', type: 'error' }]}
                  >
                    <Input maxlength={30} clearable showLimitNumber placeholder='请输入专业名称' />
                  </FormItem>
                </Col>
                <Col span={12} className={Style.update}>
                  <FormItem
                    label='详情背景'
                    name='majorsBackgroundImgId'
                    initialData={INITIAL_DATA.majorsBackgroundImgId}
                    rules={[{ required: true }]}
                  >
                    <Upload
                      url={ossUrl}
                      params={{ businessType: 4 }}
                      theme='image'
                      tips='请上传 .jpg/.png，文件大小在 512kb 以内，建议宽高比例为 75:56'
                      max={1}
                      draggable
                      success={onSuccess}
                      maxFileSize={512}
                      fileUnit={'kb'}
                      accept='.jpg, .png'
                      files={type === 'edit' ? [{ url: INITIAL_DATA.majorsBackgroundImgUrl }] : []}
                    />
                  </FormItem>
                </Col>

                <Col span={6} className={Style.dateCol}>
                  <FormItem
                    label='专业简介'
                    name='majorsDesc'
                    initialData={INITIAL_DATA.majorsDesc}
                    rules={[{ required: true }]}
                  >
                    <Textarea placeholder='专业简介必填' />
                  </FormItem>
                </Col>
                <Col span={6} className={Style.dateCol}>
                  <FormItem
                    label='专业介绍'
                    name='majorsIntroduce'
                    initialData={INITIAL_DATA.majorsIntroduce}
                    rules={[{ required: true }]}
                  >
                    <Textarea placeholder='专业介绍必填' />
                  </FormItem>
                </Col>
                {EmploymentDirection()}
                <Col span={12} className={Style.dateCol}>
                  <FormItem
                    label='衔接本科专业'
                    name='majorUndergraduate'
                    initialData={INITIAL_DATA.majorUndergraduate}
                    rules={[{ required: true }]}
                  >
                    <Textarea placeholder='请输入此专业可衔接的本科专业' />
                  </FormItem>
                </Col>
              </Row>
              <div className={Style.titleBox}></div>
              <FormItem>
                <Button type='submit' theme='primary'>
                  提交
                </Button>
                <Button type='reset' style={{ marginLeft: 12 }}>
                  重置
                </Button>
              </FormItem>
            </Form>
          </div>
        </div>
      </Loading>
    </>
  );
});
