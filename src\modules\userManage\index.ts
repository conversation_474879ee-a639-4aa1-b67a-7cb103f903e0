import { PayloadAction, Draft, AsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getStudentExamDetailApi,
  getStudentInfoByIdApi,
  getStudentInfoListApi,
  getStudentPracticeList<PERSON>pi,
  getStudentExamListApi,
  getStudentCollectionListApi,
  getStudentWrongListApi,
  getStudentFollowMajorsListApi,
  getStudentFollowEducationalListApi,
  getExamListApi,
  getSubjectListApi,
  dropDownOptionApi,
  getStudentWatchCourseProgressListApi,
  getStudentLoginLogListApi,
} from 'api';
import type {
  IGetStudentExamDetailApi,
  IGetStudentInfoListApi,
  IGetStudentExamListApi,
  IGetStudentCollectionListApi,
  IGetStudentWrongListApi,
  IGetStudentFollowMajorsListApi,
  IGetStudentFollowEducationalListApi,
  IGetStudentWatchCourseProgressListApi,
  IGetStudentLoginLogListApi,
} from 'api';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import { useParams } from 'react-router-dom';

const namespace = 'userManage';

const initialState = {
  loading: true,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  list: [],
  examList: [],
  subjectList: [],
  roleList: [],
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getStudentExamDetail = useAsyncThunkWithStatus(`getStudentExamDetail`, async (params) => {
  const {
    data: { rows, total },
  } = await getStudentExamDetailApi(params as IGetStudentExamDetailApi);
  return {
    list: rows,
    total,
    pageNum: params.pageNum,
    pageSize: params.pageSize,
  };
});

export const getStudentInfoById = useAsyncThunkWithStatus(`getStudentInfoById`, async (params) => {
  const {
    data: { rows, total },
  } = await getStudentInfoByIdApi(params);
  return {
    list: rows,
    total,
  };
});

export const getStudentInfoList = useAsyncThunkWithStatus(`getStudentInfoList`, async (params) => {
  const {
    data: { rows, total },
  } = await getStudentInfoListApi(params as IGetStudentInfoListApi);
  return {
    list: rows,
    total,
    pageNum: params.pageNum,
    pageSize: params.pageSize,
  };
});

export const getStudentPracticeList = useAsyncThunkWithStatus(`getStudentPracticeList`, async (params) => {
  const {
    data: { rows, total },
  } = await getStudentPracticeListApi(params);
  return {
    list: rows,
    total,
    pageNum: params.pageNum,
    pageSize: params.pageSize,
  };
});

export const getStudentExamList = useAsyncThunkWithStatus(`getStudentExamList`, async (params) => {
  const {
    data: { rows, total },
  } = await getStudentExamListApi(params as IGetStudentExamListApi);
  return {
    list: rows,
    total,
    pageNum: params.pageNum,
    pageSize: params.pageSize,
  };
});

export const getStudentCollectionList = useAsyncThunkWithStatus(`getStudentCollectionList`, async (params: any) => {
  const {
    data: { rows, total },
  } = await getStudentCollectionListApi(params as IGetStudentCollectionListApi);
  return {
    list: rows ?? [],
    total,
    pageNum: params.pageNum,
    pageSize: params.pageSize,
  };
});

export const getStudentWrongList = useAsyncThunkWithStatus(`getStudentWrongList`, async (params) => {
  const {
    data: { rows, total },
  } = await getStudentWrongListApi(params as IGetStudentWrongListApi);
  return {
    list: rows,
    total,
    pageNum: params.pageNum,
    pageSize: params.pageSize,
  };
});

export const getStudentFollowMajorsList = useAsyncThunkWithStatus(`getStudentFollowMajorsList`, async (params) => {
  const {
    data: { rows, total },
  } = await getStudentFollowMajorsListApi(params as IGetStudentFollowMajorsListApi);
  return {
    list: rows,
    total,
  };
});

export const getStudentFollowEducationalList = useAsyncThunkWithStatus(
  `getStudentFollowEducationalList`,
  async (params) => {
    const {
      data: { rows, total },
    } = await getStudentFollowEducationalListApi(params as IGetStudentFollowEducationalListApi);
    return {
      list: rows,
      total,
    };
  },
);

export const getStudentWatchCourseProgressList = useAsyncThunkWithStatus(
  `getStudentWatchCourseProgressList`,
  async (params) => {
    const {
      data: { rows, total },
    } = await getStudentWatchCourseProgressListApi(params as IGetStudentWatchCourseProgressListApi);

    return {
      list: rows,
      total,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
    };
  },
);

export const getStudentLoginLogList = useAsyncThunkWithStatus(`getStudentLoginLogList`, async (params) => {
  const {
    data: { rows, total },
  } = await getStudentLoginLogListApi(params as IGetStudentLoginLogListApi);

  return {
    list: rows,
    total,
    pageNum: params.pageNum,
    pageSize: params.pageSize,
  };
});

export const getExamList = useAsyncThunkWithStatus(`getExamList`, async () => {
  const {
    data: { rows },
  } = await getExamListApi();
  return rows;
});

export const dropDownOption = useAsyncThunkWithStatus(`dropDownOption`, async () => {
  const { data } = await dropDownOptionApi();
  return data;
});

export const getSubjectList = useAsyncThunkWithStatus(`getSubjectList`, async (_, { getState }) => {
  const {
    data: { rows },
  } = await getSubjectListApi();
  const state = getState() as RootState;
  const currentActive = state.resourceManageQuestion.active;

  if (rows && rows.length > 0 && !currentActive) {
    return {
      rows,
    };
  }
  return {
    rows,
  };
});

const createThunkCases = <State, Payload>(thunk: AsyncThunk<Payload, any, {}>) => ({
  thunk,
  pending: (state: Draft<State>) => {
    (state as any).loading = true;
  },
  fulfilled: (state: Draft<State>, action: PayloadAction<Payload>) => {
    (state as any).loading = false;
    (state as any).list = action.payload?.list;
    (state as any).total = action.payload?.total;
    state.pageNum = action.payload.pageNum;
    state.pageSize = action.payload.pageSize;
  },
  rejected: (state: Draft<State>) => {
    (state as any).loading = false;
  },
});

const userManageSlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  cases: [
    createThunkCases(getStudentPracticeList),
    createThunkCases(getStudentExamList),
    createThunkCases(getStudentCollectionList),
    createThunkCases(getStudentWrongList),
    createThunkCases(getStudentFollowMajorsList),
    createThunkCases(getStudentFollowEducationalList),
    createThunkCases(getStudentWatchCourseProgressList),
    createThunkCases(getStudentLoginLogList),
    createThunkCases(getStudentInfoList),
    createThunkCases(getStudentExamDetail),

    {
      thunk: getExamList,
      pending: () => {},
      fulfilled: (state, action) => {
        state.examList = action.payload;
      },
      rejected: () => {},
    },
    {
      thunk: getSubjectList,
      pending: () => {},
      fulfilled: (state, action) => {
        state.subjectList = action.payload.rows;
      },
      rejected: () => {},
    },
    {
      thunk: dropDownOption,
      pending: () => {},
      fulfilled: (state, action) => {
        state.roleList = action.payload;
      },
      rejected: () => {},
    },
  ],
});

export const { clearPageState } = userManageSlice.actions;

export const selecUserManage = (state: RootState) => state.userManage;

export default userManageSlice.reducer;
