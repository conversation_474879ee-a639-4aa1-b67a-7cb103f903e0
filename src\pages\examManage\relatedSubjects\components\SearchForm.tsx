import React, { useRef, memo } from 'react';
import { Row, Col, Form, Input, Button } from 'tdesign-react';
import { FormInstanceFunctions } from 'tdesign-react/es/form/type';

const { FormItem } = Form;

export type FormValueType = {
  subjectName?: string;
  value?: string;
};

export type SearchFormProps = {
  onCancel: () => void;
  onSubmit: (values: FormValueType) => Promise<void>;
};

const SearchForm: React.FC<SearchFormProps> = (props) => {
  const formRef = useRef<FormInstanceFunctions>();
  const onSubmit = () => {
    const queryValue = formRef?.current?.getFieldsValue?.(true);
    if (queryValue?.subjectName) {
      props.onSubmit(queryValue);
    }
  };

  const onReset = () => {
    props.onCancel();
    props.onSubmit({ subjectName: '' });
  };

  return (
    <div className='list-common-table-query'>
      <Form ref={formRef} onSubmit={onSubmit} onReset={onReset} labelWidth={80} colon>
        <Row>
          <Col flex='1'>
            <Row gutter={[16, 16]}>
              <Col span={23} xs={22} sm={26} xl={24}>
                <FormItem label='科目名称' name='subjectName'>
                  <Input placeholder='请输入科目名称' />
                </FormItem>
              </Col>
            </Row>
          </Col>
          <Col flex='160px'>
            <Button theme='primary' type='submit' style={{ margin: '0px 20px' }}>
              查询
            </Button>
            <Button type='reset' variant='base' theme='default'>
              重置
            </Button>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default memo(SearchForm);
