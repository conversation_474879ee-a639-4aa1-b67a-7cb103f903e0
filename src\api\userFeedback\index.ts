import request from 'utils/request/index';

/**
 * 查询用户反馈的详情
 * @returns
 */
export const getFeedbackDetailApi = async (id: number) => {
  const result = await request.get({
    url: `/feedback/getFeedbackDetail/${id}`,
  });
  return result;
};

export interface IUpdateFeedbackStatusApi {
  feedbackId: number;
  processingStatus: 0 | 1;
}

/**
 * 修改处理状态
 * @returns
 */
export const updateFeedbackStatusApi = async (params: IUpdateFeedbackStatusApi) => {
  const result = await request.put({
    url: `/feedback/updateFeedbackStatus`,
    params,
  });
  return result;
};

export interface IGetFeedbackListApi {
  pageNum: number;
  pageSize: number;
  feedbackType?: number;
  processingStatus: 0 | 1;
  nickName: string;
  phoneNumber: string;
}

/**
 * 分页条件查询用户反馈列表
 * @returns
 */
export const getFeedbackListApi = async (params: IGetFeedbackListApi) => {
  const result = await request.get({
    url: `/feedback/getFeedbackList`,
    params,
  });
  return result;
};
