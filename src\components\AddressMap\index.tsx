// Map 子组件
import React, { useEffect, useState, useRef, forwardRef, useImperativeHandle, memo } from 'react';
import AMapLoader from '@amap/amap-jsapi-loader';
import { MessagePlugin, Space } from 'tdesign-react';
import Style from './index.module.less';

type GeneralType = number | string | null | undefined;

export type initDataType = {
  longitude: GeneralType;
  latitude: GeneralType;
  addressName: string;
};

interface IAMapComponent {
  initData: initDataType;
  domId: string;
  tipinput: string;
  onAddressInfo: (info: any, location: any) => void;
  placeholder?: string;
  disabled?: boolean;
}

interface IEvent {
  poi: {
    id: string;
    name: string;
    district: string;
    adcode: string;
    location: [number, number];
    address: string;
    typecode: string;
    city: any[];
  };
  type: string;
}

const { VITE_MAP_KEY, VITE_MAP_VERSION } = import.meta.env;

const optionsMap = {
  key: VITE_MAP_KEY,
  version: VITE_MAP_VERSION,
  plugins: [
    'AMap.Scale',
    'AMap.ToolBar',
    'AMap.PlaceSearch',
    'AMap.AutoComplete',
    'AMap.DistrictSearch',
    'AMap.Geolocation',
    'AMap.Geocoder',
  ],
};

const AMapComponent = forwardRef(
  ({ initData, domId, tipinput, onAddressInfo, placeholder, disabled = false }: IAMapComponent, ref) => {
    const [markerInstance, setMarkerInstance] = useState<AMap.Marker | null>(null);
    const [addressName, setAddressName] = useState<string>('');
    const mapRef = useRef<AMap.Map | null>(null);

    const positionBlock = (map: any, AMap: AMap.Map, latLon: any, event: IEvent) => {
      if (map) {
        map.setZoom(17);
        map.setCenter(latLon);

        if (markerInstance) {
          markerInstance.setPosition(latLon);
        } else {
          const marker = new AMap.Marker({
            position: new AMap.LngLat(latLon[0], latLon[1]),
            map,
          });
          setMarkerInstance(marker);
        }
      }

      const geocoder = new AMap.Geocoder({
        radius: 1000,
        extensions: 'all',
      });
      geocoder.getAddress(latLon, (status: string, result: any) => {
        if (status === 'complete' && result.regeocode) {
          setAddressName(event.poi.name);
          onAddressInfo(result.regeocode, event.poi);
        } else {
          throw new Error('获取详细地址信息失败');
        }
      });
    };

    const handleStartSelect = (event: IEvent, map: AMap.Map, AMap: any) => {
      if (!event.poi.location) {
        MessagePlugin.success('该地点无经纬度数据，请输入具体一点的地点!');
        return;
      }

      const location = [event.poi.location.lng, event.poi.location.lat];

      positionBlock(map, AMap, location, event);
    };

    const loadMap = (id: string, tipinput: string, initData: initDataType | undefined) => {
      AMapLoader.load(optionsMap)
        .then((AMap) => {
          const map = new AMap.Map(id, {
            resizeEnable: true,
            viewMode: '3D',
            pitch: 50,
            rotateEnable: true,
            pitchEnable: true,
            zoom: 10,
            rotation: -15,
            buildingAnimation: true,
            expandZoomRange: true,
            zooms: [3, 20],
          });
          mapRef.current = map;
          map.addControl(new AMap.Scale());
          map.addControl(new AMap.ToolBar());

          const geolocation = new AMap.Geolocation({
            enableHighAccuracy: true,
            timeout: 10000,
            zoomToAccuracy: true,
            buttonPosition: 'RB',
            showButton: false,
            showMarker: true,
            showCircle: false,
            panToLocation: true,
          });
          map.addControl(geolocation);

          AMap.plugin([], () => {
            const autoOptions1 = {
              input: tipinput,
              city: '全国',
              radius: 500,
              extensions: 'all',
            };
            const startAutoComplete = new AMap.AutoComplete(autoOptions1);
            startAutoComplete.on('select', (res: IEvent) => {
              handleStartSelect(res, map, AMap);
            });
          });

          if (initData) {
            const latLon = [initData.longitude, initData.latitude];
            const mockEvent = {
              poi: {
                id: '',
                name: initData.addressName,
                district: '',
                adcode: '',
                location: latLon,
                address: '',
                typecode: '',
                city: [],
              },
              type: '',
            };
            positionBlock(map, AMap, latLon, mockEvent);
          } else {
            // 定位
            // geolocation.getCurrentPosition();
            geolocation.getCurrentPosition((status: string, result: any) => {
              if (status === 'complete') {
                // eslint-disable-next-line no-console
                console.log('定位成功', result);
              } else {
                // eslint-disable-next-line no-console
                console.log('定位失败', result);
              }
            });
          }
          map.setZoom(17);
        })
        .catch((e) => {
          throw new Error(e);
        });
    };
    useImperativeHandle(ref, () => ({
      resetMap() {
        if (mapRef.current) {
          mapRef.current.destroy();
          setAddressName('');
          loadMap(domId, tipinput, undefined);
        }
      },
    }));

    useEffect(() => {
      setAddressName(initData?.addressName ?? '');
      const timer = setTimeout(() => {
        loadMap(domId, tipinput, initData);
      }, 0);
      return () => clearTimeout(timer);
    }, [domId, tipinput, initData]);

    return (
      <div style={{ display: 'flex', flexDirection: 'column', rowGap: '10px', width: '100%', height: '100%' }}>
        <div className='t-input__wrap'>
          <div className={`t-input t-align-left ${disabled === true ? 't-is-disabled' : ''}`}>
            <input
              disabled={disabled}
              className='t-input__inner'
              value={addressName}
              placeholder={placeholder || '请输入学校地址'}
              id={tipinput}
              type='text'
              onChange={(e) => setAddressName(e.target.value)}
            />
          </div>
        </div>
        <div
          className={Style.AMapComponent}
          id={domId}
          style={{ width: '100%', height: '100%', borderRadius: '8px' }}
        ></div>
      </div>
    );
  },
);

export default memo(AMapComponent);
