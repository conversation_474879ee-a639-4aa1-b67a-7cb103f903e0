import React, { memo, useEffect, useState, useRef } from 'react';
import { TableProps, Button } from 'tdesign-react';
import { useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'modules/store';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { selectPointsManagement, getIntegralLogList } from 'modules/pointsManagement';
import { Tables, Search, Back } from 'components';
import { ClearDialog } from '../components';
import { IRef } from 'components/Form';

const changePointsList = [
  { value: 1, label: '自由练习' },
  { value: 2, label: '模拟考试' },
  { value: 3, label: '积分商城' },
  { value: 4, label: '连续签到' },
  { value: 5, label: '邀请新人' },
  { value: 6, label: '首次注册' },
  { value: 7, label: '兑换' },
];

const columns: TableProps['columns'] = [
  { colKey: 'serial-number', width: 80, title: '序号' },
  {
    title: '积分变更来源',
    fixed: 'left',
    colKey: 'integralSource',
    cell({ row }) {
      return changePointsList.find((item) => item.value === row.integralSource)?.label ?? '暂无变更来源';
    },
  },
  { title: '变更积分', colKey: 'integralValue' },
  { title: '总积分', colKey: 'integralCurrentAssemble' },
  { title: '积分变更时间', colKey: 'updateTime' },
];

export const LogTable: React.FC = () => {
  const dispatch = useAppDispatch();

  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);

  const {
    state,
    state: { studentId },
  } = useLocation();
  const {
    log: { loading, list, pageNum, pageSize, total },
  } = useAppSelector(selectPointsManagement);
  const [visible, setVisible] = useState(false);

  // const handleResetSelection = () => {
  //   if (tableRef.current) {
  //     // 调用重置选中方法
  //     tableRef.current.resetSelection();
  //   }
  // };

  const searchParams = {
    pageSize,
    pageNum,
    studentId,
  };

  useEffect(() => {
    dispatch(getIntegralLogList(searchParams));
  }, [dispatch]);

  const onCancel = (val: boolean) => {
    // e.stopPropagation();
    setVisible(val);
  };

  const BackTitle = () => (
    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: '50px' }}>
      <div>用户：{state.nickName}</div>
      <div>积分：{state.assembleIntegral}</div>
      <Button
        theme='danger'
        onClick={(e) => {
          e.stopPropagation();
          setVisible(true);
        }}
      >
        清空全部积分
      </Button>
    </div>
  );

  return (
    <>
      <Back path='/integralManage/userIntegral' header={BackTitle()} />
      <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
        <Search
          ref={searchRef}
          method={getIntegralLogList}
          params={searchParams}
          list={[
            {
              type: 'select',
              label: '积分变更来源',
              field: 'integralSource',
              options: changePointsList,
            },
          ]}
        />
        <Tables
          ref={tableRef}
          formRef={searchRef}
          tabletData={{
            columns,
            list,
            loading,
            rowKey: 'id',
            selectedRowKeys,
            onSelectChange,
          }}
          paging={{
            pageNum,
            pageSize,
            total,
          }}
          method={getIntegralLogList}
          params={searchParams}
        />
      </div>
      {ClearDialog({
        visibleProp: visible,
        rowProp: state,
        typeProp: 'all',
        onCancel: (val) => onCancel(val),
      })}
    </>
  );
};

export default memo(LogTable);
