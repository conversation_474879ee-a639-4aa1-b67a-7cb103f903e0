import { TdTabPanelProps } from 'tdesign-react';
import EnrollmentTable from './EnrollmentTable';
import SalaryTable from './SalaryTable';
import HotTable from './HotTable';

export const TAB_LIST: Array<TdTabPanelProps> = [
  {
    lazy: true,
    value: 1,
    label: '招生榜',
    // eslint-disable-next-line react/react-in-jsx-scope
    panel: <EnrollmentTable />,
  },
  {
    lazy: true,
    value: 2,
    label: '薪资榜',
    // eslint-disable-next-line react/react-in-jsx-scope
    panel: <SalaryTable />,
  },
  {
    lazy: true,
    value: 3,
    label: '热门推荐',
    // eslint-disable-next-line react/react-in-jsx-scope
    panel: <HotTable />,
  },
];
