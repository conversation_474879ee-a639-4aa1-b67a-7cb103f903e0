import React from 'react';
import {
  <PERSON><PERSON>,
  Card,
  Divider,
  Comment,
  Switch,
  Popconfirm,
  ImageViewer,
  Image,
  type ImageViewerProps,
} from 'tdesign-react';
import { Edit1Icon, DeleteIcon, BrowseIcon } from 'tdesign-icons-react';
import classnames from 'classnames';
import Style from './index.module.less';

interface ICardImageProps {
  /** 样式类名 */
  className?: string;

  /** 行内样式 */
  style?: React.CSSProperties;

  /** 状态 */
  status?: boolean;

  /** 图片路径 */
  cover?: string;

  /** 卡片宽度 */
  width?: number | string;

  /** 卡片高度 */
  height?: number | string;

  /** 图片高度 */
  imgHeight?: string;

  /** 图片宽度 */
  imgWidth?: string;

  /** 卡片标题 */
  title?: string | undefined;

  /** 卡片内容 */
  content?: string | undefined;

  /** key */
  key?: string | number;

  /** 删除提示语 */
  deleteTip?: string;

  /** 删除回调 */
  onDelete?: () => void;

  /** 编辑回调 */
  onEdit?: () => void;

  /** 开关回调 */
  onChange?: (value: boolean) => void;
}

const ICardImage = (props: ICardImageProps) => {
  const trigger: ImageViewerProps['trigger'] = ({ open }) => {
    const mask = (
      <div
        style={{
          background: 'rgba(0,0,0,.6)',
          color: '#fff',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
        onClick={open}
      >
        <span>
          <BrowseIcon size='16px' name={'browse'} /> 预览
        </span>
      </div>
    );

    return (
      <Image
        className={Style.img}
        alt={'test'}
        src={props?.cover}
        overlayContent={mask}
        overlayTrigger='hover'
        fit='contain'
        style={{
          height: `${props?.imgHeight ?? 'auto'}`,
          width: `${props?.imgWidth ?? 'auto'}`,
        }}
      />
    );
  };

  const title = (
    <div
      style={{
        width: `calc(${props?.width ?? '400px'} - 58px)`,
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
      }}
    >
      {props?.title}
    </div>
  );

  const PropsContent = (val: string | number | string[] | number[]) => {
    if (typeof val === 'string' || typeof val === 'number') return <div>{val}</div>;
    if (Array.isArray(val) && val.length > 0)
      return (
        <div
          style={{
            width: `calc(${props?.width ?? '400px'} - 58px)`,
            display: 'flex',
            justifyContent: 'space-between',
            flexWrap: 'nowrap',
          }}
        >
          {val.map((prop, index) => (
            <div
              title={prop as string}
              style={{
                width: `${index === 1 ? '30%' : '60'}%`,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
              key={index}
              dangerouslySetInnerHTML={{ __html: prop }}
            ></div>
          ))}
        </div>
      );
    return val;
  };

  return (
    <div key={props?.key ?? ''} className={classnames(props?.className ?? '')}>
      <Card
        bordered
        theme='poster2'
        cover={<ImageViewer trigger={trigger} images={[props?.cover ?? '']} />}
        style={{ width: `${props?.width ?? '400px'}`, ...props?.style }}
        footer={
          <>
            <Comment author={title} content={PropsContent(props?.content)}></Comment>
            <Divider layout='horizontal' style={{ margin: 'var(--td-comp-margin-s) 0' }}></Divider>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Button variant='text' onClick={props?.onEdit}>
                <Edit1Icon />
              </Button>
              <Divider layout='vertical'></Divider>
              <Popconfirm
                content={props.deleteTip ?? '确定要删除该数据吗？'}
                theme='danger'
                onConfirm={props?.onDelete}
              >
                <Button variant='text'>
                  <DeleteIcon />
                </Button>
              </Popconfirm>
              <Divider layout='vertical'></Divider>
              <Switch value={props?.status} onChange={props?.onChange} />
            </div>
          </>
        }
      ></Card>
    </div>
  );
};

export default ICardImage;
