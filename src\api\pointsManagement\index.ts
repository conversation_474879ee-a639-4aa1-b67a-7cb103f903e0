import request from 'utils/request/index';

export interface IGetIntegralListApi {
  pageNum: number;
  pageSize: number;
  nickName: string;
  phoneNumber: string;
}
export interface IUpdateContinuousSignInApi {
  ruleId: number | '';
  isOpen: number;
}

/**
 * 分页条件查询积分列表
 * @param params
 * @returns
 */
export const getIntegralListApi = async (params: IGetIntegralListApi) => {
  const result = await request.get({
    url: '/integral/getIntegralList',
    params,
  });
  return result;
};

/**
 * 生成清空积分验证的图形验证码
 * @returns
 */
export const clearIntegralCaptchaImageApi = async () => {
  const result = await request.get({
    url: '/integral/clearIntegralCaptchaImage',
  });
  return result;
};

export interface IClearIntegralApi {
  password: string;
  codeValue: number;
  uuid: string;
  integralIdList: (number | string)[];
  studentIdList: (number | string)[];
}
export interface IntegralReceiptAndPaymentListApi {
  pageNum: number;
  pageSize: number;
  phoneNumber: string;
  integralSource: number;
  integralType: number;
  startTime: (number | string)[];
  endTime: (number | string)[];
}

/**
 * 清空积分
 * @param params
 * @returns
 */
export const clearIntegralApi = async (params: IClearIntegralApi) => {
  const result = await request.put({
    url: '/integral/clearIntegral',
    params,
  });
  return result;
};

/**
 * 查询积分规则
 * @returns
 */
export const getIntegralRuleApi = async () => {
  const result = await request.get({
    url: '/integral/getIntegralRule',
  });
  return result;
};

export interface IAddOrUpdateIntegralRuleApi {
  ruleId: number;
  successAddIntegral: number;
  failAddIntegral: number;
  theSameObtainIntegral: 0 | 1;
}

/**
 *  添加/修改积分规则
 * @param params
 * @returns
 */
export const addOrUpdateIntegralRuleApi = async (params: IAddOrUpdateIntegralRuleApi) => {
  const result = await request.put({
    url: '/integral/addOrUpdateIntegralRule',
    params,
  });
  return result;
};

export interface IGetIntegralLogListApi {
  studentId: number;
  integralSource: 1 | 2 | 3;
  pageNum: number;
  pageSize: number;
}

/**
 * 条件分页查询积分日志
 * @param params
 * @returns
 */
export const getIntegralLogListApi = async (params: IGetIntegralLogListApi) => {
  const result = await request.get({
    url: '/integral/getIntegralLogList',
    params,
  });
  return result;
};

/**
 * 根据用户id查询积分和昵称
 * @param id
 * @returns
 */
export const getIntegralIdApi = async (id: number | string) => {
  const result = await request.get({
    url: `/integral/getIntegral/${id}`,
  });
  return result;
};

/**
 * 开启/关闭连续签到积分规则
 * */

export const updateContinuousSignInApiApi = async (params: IUpdateContinuousSignInApi) => {
  const result = await request.put({
    url: '/integral/updateContinuousSignIn',
    params,
  });
  return result;
};

/**
 * 分页条件查询积分收支列表
 * */
export const getIntegralReceiptAndPaymentListApi = async (params: IntegralReceiptAndPaymentListApi) => {
  const result = await request.get({
    url: '/integral/getIntegralReceiptAndPaymentList',
    params,
  });
  return result;
};
/**
 * 查询积分收入/支出 类型
 * */
export const getIntegralReceiptAndPaymentTypeListApi = async () => {
  const result = await request.get({
    url: '/integral/getIntegralReceiptAndPaymentTypeList',
  });
  return result;
};
