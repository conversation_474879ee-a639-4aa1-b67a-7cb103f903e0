import React, { memo, useState } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Tabs, TabValue } from 'tdesign-react';
import { TAB_LIST } from './consts';
import { useLocation, useNavigate } from 'react-router-dom';

const Goods: React.FC = () => {
  const location = useLocation();
  const { type } = location.state || {};
  const [tabValue, setTabValue] = useState<TabValue>(type || 1);
  const navigate = useNavigate();

  const handleChange = (val: TabValue) => {
    navigate('/mall/goods', {
      state: {
        type: val,
      },
    });
    setTabValue(val);
  };

  return (
    <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
      <Tabs value={tabValue} onChange={handleChange} list={TAB_LIST} />
    </div>
  );
};

export default memo(Goods);
