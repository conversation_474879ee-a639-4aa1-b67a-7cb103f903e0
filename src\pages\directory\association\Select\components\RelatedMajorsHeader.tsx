import React, { memo } from 'react';
import { Tag } from 'tdesign-react';
import Style from './RelatedMajorsHeader.module.less';

interface IRelatedMajorsHeaderProps {
  cunt: number;
  type: string;
  activeName: string;
  speciality: object;
}

export const RelatedMajorsHeader: React.FC<IRelatedMajorsHeaderProps> = ({ cunt, type, activeName, speciality }) => {
  type LevelType = {
    label: string;
    value: number;
    description: string;
  };

  const LevelEnum: LevelType[] = [
    { label: '专科', value: 1, description: '专科枚举' },
    { label: '本科', value: 2, description: '本科枚举' },
  ];

  // 根据 value 搜索 label
  function findLabelByValue(value: number): string | undefined {
    const level = LevelEnum.find((item) => item.value === value);
    return level ? level.label : undefined;
  }

  return (
    <div className={Style.dialogHeaderContainer}>
      <div>
        {cunt === 0 && type === 'add'
          ? `${activeName} - 请选择您要添加的专业`
          : `${activeName} - ${speciality.majorsName} - 编辑关联信息`}
      </div>
      {((type === 'add' && cunt > 0) || type === 'edit') && <Tag content={findLabelByValue(speciality.eduLevel)} />}
    </div>
  );
};

export default memo(RelatedMajorsHeader);
