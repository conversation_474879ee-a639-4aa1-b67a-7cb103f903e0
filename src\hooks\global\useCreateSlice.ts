import {
  useAsyncThunkWithStatus as originalUseAsyncThunkWithStatus,
  useCreateSlice as originalUseCreateSlice,
} from '../useCreateSlice';

export const useGlobalAsyncThunkWithStatus = originalUseAsyncThunkWithStatus;
export const useGlobalCreateSlice = originalUseCreateSlice;

export type UseGlobalAsyncThunkWithStatus = typeof originalUseAsyncThunkWithStatus;
export type UseGlobalCreateSlice = typeof originalUseCreateSlice;
