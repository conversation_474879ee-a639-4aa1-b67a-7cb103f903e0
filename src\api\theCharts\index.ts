import request from 'utils/request/index';

export interface IGetRankingListApi {
  pageNum: number;
  pageSize: number;
  examId?: number;
}
/**
 * 分页条件查询考试的排行榜列表
 * @returns
 */
export const getRankingListApi = async (params: IGetRankingListApi) => {
  const result = await request.get({
    url: '/rankingList/getRankingList',
    params,
  });
  return result;
};

export interface IGetAdminRankingListApi {
  pageNum: number;
  pageSize: number;
  examId: number;
  type: 0 | 1 | 2 | 3 | 4 | 5;
  phone: string;
  nickName: string;
  userType: 1 | 2;
}
/**
 * 查询每场考试的排行榜详情
 * @returns
 */
export const getAdminRankingListApi = async (params: IGetAdminRankingListApi) => {
  const result = await request.get({
    url: '/rankingList/getAdminRankingList',
    params,
  });
  return result;
};

export interface IAddVirtualUserApi {
  addNum: number;
  scoreMin: number;
  scoreMax: number;
  examId: number;
}
/**
 * 新增虚拟用户
 * @returns
 */
export const addVirtualUserApi = async (params: IAddVirtualUserApi) => {
  const result = await request.post({
    url: '/rankingList/addVirtualUser',
    params,
  });
  return result;
};

export interface IDeleteVirtualUserApi {
  nickName: string;
  phone: string;
  examId: number;
  stuIds: (string | number)[];
}

/**
 * 批量/单个删除虚拟用户
 * @returns
 */
export const deleteVirtualUserApi = async (params: IDeleteVirtualUserApi) => {
  const result = await request.delete({
    url: '/rankingList/deleteVirtualUser',
    params,
  });
  return result;
};
