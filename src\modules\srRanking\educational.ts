import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { getScaleListApi, getEduEnrollmentListApi, getEmployListApi, publicGetExamListApi } from 'api';
import { IGetScaleListBo, IGetEduEnrollmentListBo, IGetEmployListBo } from 'types/srRanking';
import { MenuValue } from 'tdesign-react';
import { strIsNull } from 'utils/tool';

const namespace = 'srRanking/educational';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  treeLoading: boolean;
  treeList: Array<any>;
  active: MenuValue;
}

const initialState: IInitialState = {
  loading: false,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  treeLoading: false,
  treeList: [],
  active: '',
};

export const getTreeList = createAsyncThunk(`${namespace}/getTreeList`, async () => {
  const { data } = await publicGetExamListApi();

  return data;
});

export const getScaleList = createAsyncThunk(`${namespace}/getScaleList`, async (bo: IGetScaleListBo) => {
  const { data } = await getScaleListApi(bo);

  return {
    list: data.rows,
    total: data.total,
    pageNum: bo.pageNum,
    pageSize: bo.pageSize,
  };
});

export const getEduEnrollmentList = createAsyncThunk(
  `${namespace}/getEduEnrollmentList`,
  async (bo: IGetEduEnrollmentListBo) => {
    const { data } = await getEduEnrollmentListApi(bo);

    return {
      list: data.rows,
      total: data.total,
      pageNum: bo.pageNum,
      pageSize: bo.pageSize,
    };
  },
);

export const getEmployList = createAsyncThunk(`${namespace}/getEmployList`, async (bo: IGetEmployListBo) => {
  const { data } = await getEmployListApi(bo);

  return {
    list: data.rows,
    total: data.total,
    pageNum: bo.pageNum,
    pageSize: bo.pageSize,
  };
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setActive: (state, { payload }) => {
      state.active = payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getTreeList.pending, (state) => {
        state.treeLoading = true;
      })
      .addCase(getTreeList.fulfilled, (state, action) => {
        state.treeList = action.payload.rows;

        if (!strIsNull(action.payload.rows)) {
          state.active = action.payload.rows[0].examId;
        }

        state.treeLoading = false;
      })
      .addCase(getTreeList.rejected, (state) => {
        state.treeLoading = false;
      })

      .addCase(getScaleList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getScaleList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getScaleList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getEduEnrollmentList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getEduEnrollmentList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getEduEnrollmentList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getEmployList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getEmployList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getEmployList.rejected, (state) => {
        state.loading = false;
      });
  },
});

export const { clearPageState, setActive } = listBaseSlice.actions;

export const selectSRRankingEducational = (state: RootState) => state.srRankingEducational;

export default listBaseSlice.reducer;
