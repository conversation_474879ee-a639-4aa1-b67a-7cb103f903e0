/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable no-nested-ternary */
import React, { useEffect, useRef, useState } from 'react';
import { selectSRRankingMechanism, clearPageState, editMechanism, getMechanismInfo } from 'modules/srRanking/mechanism';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  Button,
  Col,
  Form,
  Loading,
  Row,
  Space,
  Table,
  Input as InputTD,
  InputNumber,
  MessagePlugin,
} from 'tdesign-react';
import type { FormInstanceFunctions, FormProps, TableProps } from 'tdesign-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back, Editor, ImageViewer, Input, Textarea, Upload } from 'components';
import { useLocation, useNavigate } from 'react-router-dom';
import { IEditMechanismBo } from 'types/srRanking';
import { DeleteIcon, EditIcon, MapCancelIcon, SaveIcon } from 'tdesign-icons-react';
import { strIsNull } from 'utils/tool';
import CustomTag from 'components/CustomTag';
import CustomUpload from '../components/CustomUpload';
import CustomAddressMap from '../components/CustomAddressMap';

const { FormItem } = Form;

const EditMechanism: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const location = useLocation();
  const { id } = location.state || {};
  const { mechanismInfo, mechanismInfoLoading, addEditMechanismLoading } = useAppSelector(selectSRRankingMechanism);
  const formRef = useRef<FormInstanceFunctions>();
  const [form] = Form.useForm();

  const [agencyDesc, setAgencyDesc] = useState('');
  const [honors, setHonors] = useState('');
  const [picList, setPicList] = useState<Array<any>>([]);
  const teacherTableRef = useRef(null);
  const [teacherList, setTeacherList] = useState([]);
  // 保存变化过的行信息
  const [editTeacherMap, setEditTeacherMap] = useState({});
  const [editTeacherTableRowKeys, setEditTeacherTableRowKeys] = useState([1]);
  const serveTableRef = useRef(null);
  const [serveList, setServeList] = useState([]);
  // 保存变化过的行信息
  const [editServeMap, setEditServeMap] = useState({});
  const [editServeTableRowKeys, setEditServeTableRowKeys] = useState([1]);
  const addressTableRef = useRef(null);
  const [addressList, setAddressList] = useState([]);
  // 保存变化过的行信息
  const [editAddressMap, setEditAddressMap] = useState({});
  const [editAddressTableRowKeys, setEditAddressTableRowKeys] = useState([1]);

  const INITIAL_DATA: IEditMechanismBo = {
    id,
    name: '',
    shortName: '',
    phoneNumber: '',
    agencyDesc: '',
    honors: '',
    venueDesc: '',
    picList: [],
    teacherList: [],
    serveList: [],
    addressList: [],
  };

  const rules: FormProps['rules'] = {
    name: [
      {
        required: true,
        message: '机构名称不能为空',
        trigger: 'all',
      },
    ],
    shortName: [
      {
        required: true,
        message: '机构简称不能为空',
        trigger: 'all',
      },
    ],
    agencyDesc: [
      {
        required: true,
        message: '机构简介不能为空',
        trigger: 'all',
      },
    ],
  };

  const handleSubmit: FormProps['onSubmit'] = ({ validateResult, fields }) => {
    if (validateResult === true) {
      teacherTableRef.current?.clearValidateData();
      serveTableRef.current?.clearValidateData();
      addressTableRef.current?.clearValidateData();

      const bo: IEditMechanismBo = {
        id,
        ...fields,
      };

      dispatch(editMechanism(bo));
    }
  };

  const handleReset: FormProps['onReset'] = () => {
    setTeacherList([]);
    teacherTableRef.current?.clearValidateData();
    teacherTableRef.current?.refreshTable();
    setEditTeacherMap({});
    setEditTeacherTableRowKeys([]);

    setServeList([]);
    serveTableRef.current?.clearValidateData();
    serveTableRef.current?.refreshTable();
    setEditServeMap({});
    setEditServeTableRowKeys([]);

    setAddressList([]);
    addressTableRef.current?.clearValidateData();
    addressTableRef.current?.refreshTable();
    setEditAddressMap({});
    setEditAddressTableRowKeys([]);

    dispatch(getMechanismInfo(id));
  };

  const handleCancel = () => {
    navigate('/srRanking/mechanism');
  };

  const updateTeacherEditState = (id) => {
    // 更新 editableRowKeys
    const index = editTeacherTableRowKeys.findIndex((t) => t === id);
    editTeacherTableRowKeys.splice(index, 1);
    setEditTeacherTableRowKeys([...editTeacherTableRowKeys]);
  };

  const handleEditTeacher = (id) => {
    if (!editTeacherTableRowKeys.includes(id)) {
      setEditTeacherTableRowKeys((prev) => [...prev, id]);
    }
  };

  const handleDeleteTeacher = (id) => {
    const list = teacherList.filter((item) => item.id !== id);

    setTeacherList([...list]);

    delete editTeacherMap[id.toString()];
  };

  const handleAddTeacher = (id, isAdd) => {
    teacherTableRef.current?.validateRowData(id).then((params) => {
      if (params.result.length) return;

      const current = editTeacherMap[id.toString()];

      teacherList.splice(current.rowIndex, 1, current.editedRow);

      if (isAdd) {
        teacherList.push({
          id: id + 1,
          teacherName: '',
          teacherTitle: [],
          headImgId: {},
        });
      }

      setTeacherList([...teacherList]);

      updateTeacherEditState(id);

      if (isAdd) {
        handleEditTeacher(id + 1);
      }

      MessagePlugin.success('老师添加成功');
    });
  };

  // 行数据编辑时触发，返回最新输入结果
  const onTeacherRowEdit: TableProps['onRowEdit'] = (params) => {
    const { row, col, value } = params;
    const oldRowData = editTeacherMap[row.id.toString()]?.editedRow || row;
    const editedRow = { ...oldRowData, [col.colKey]: value };
    editTeacherMap[row.id.toString()] = {
      ...params,
      editedRow,
    };
    setEditTeacherMap(editTeacherMap);
  };

  const teacherColumns: TableProps['columns'] = [
    {
      colKey: 'teacherName',
      title: '老师名称',
      width: '28%',
      edit: {
        component: InputTD,
        // 透传至component中
        props: {
          clearable: true,
          placeholder: '请输入老师名称',
        },
        showEditIcon: false,
        // 校验规则
        rules: [{ required: true, message: '老师名称不能为空' }],
      },
    },
    {
      colKey: 'teacherTitle',
      title: '老师标签',
      width: '28%',
      cell({ row }) {
        return row.teacherTitle && row.teacherTitle.join(' | ');
      },
      edit: {
        component: CustomTag,
        // 透传至component中
        props: {
          placeholder: '请输入老师标签',
        },
        showEditIcon: false,
        // 校验规则
        rules: [{ required: true, message: '老师标签不能为空' }],
      },
    },
    {
      colKey: 'headImgId',
      title: '老师头像',
      width: '28%',
      cell({ row }) {
        return row.headImgId?.url
          ? ImageViewer({
            type: 'item',
            images: [row.headImgId.url],
            style: {
              width: 30,
              height: 30,
            },
          })
          : null;
      },
      edit: {
        component: CustomUpload,
        // 透传至component中
        props: () => ({
          url: '/oss/uploadFile',
          theme: 'image',
          title: '请上传 .jpg/.jpeg/.png 格式的图片 文件大小5M',
          accept: '.jpg, .jpeg, .png',
          maxFileSize: 5,
          max: 1,
          draggable: true,
          params: {
            businessType: 15,
          },
        }),
        showEditIcon: false,
        // 校验规则
        rules: [{ required: true, message: '老师头像不能为空' }],
      },
    },
    {
      title: '操作栏',
      colKey: 'operate',
      cell: ({ row }) => {
        const editTeacherTable = editTeacherTableRowKeys.includes(row.id);

        return (
          <>
            {!editTeacherTable && (
              <Space>
                <Button theme='default' shape='circle' icon={<EditIcon />} onClick={() => handleEditTeacher(row.id)} />
                <Button
                  theme='default'
                  shape='circle'
                  icon={<DeleteIcon />}
                  onClick={() => handleDeleteTeacher(row.id)}
                />
              </Space>
            )}

            {editTeacherTable && (
              <Space>
                <Button
                  theme='default'
                  shape='circle'
                  icon={<SaveIcon />}
                  data-id={row.id}
                  onClick={() =>
                    handleAddTeacher(
                      row.id,
                      strIsNull(row.teacherName) || strIsNull(row.teacherTitle) || strIsNull(row.headImgId),
                    )
                  }
                />
                {!strIsNull(row.teacherName) && !strIsNull(row.teacherTitle) && !strIsNull(row.headImgId) && (
                  <Button
                    theme='default'
                    shape='circle'
                    icon={<MapCancelIcon />}
                    onClick={() => updateTeacherEditState(row.id)}
                  />
                )}
              </Space>
            )}
          </>
        );
      },
    },
  ];

  const updateServeEditState = (id) => {
    // 更新 editableRowKeys
    const index = editServeTableRowKeys.findIndex((t) => t === id);
    editServeTableRowKeys.splice(index, 1);
    setEditServeTableRowKeys([...editServeTableRowKeys]);
  };

  const handleEditServe = (id) => {
    if (!editServeTableRowKeys.includes(id)) {
      setEditServeTableRowKeys((prev) => [...prev, id]);
    }
  };

  const handleDeleteServe = (id) => {
    const list = serveList.filter((item) => item.id !== id);

    setServeList([...list]);

    delete editServeMap[id.toString()];
  };

  const handleAddServe = (id, isAdd) => {
    serveTableRef.current?.validateRowData(id).then((params) => {
      if (params.result.length) return;

      const current = editServeMap[id.toString()];

      serveList.splice(current.rowIndex, 1, current.editedRow);

      if (isAdd) {
        serveList.push({
          id: id + 1,
          serveName: '',
          serveTitle: [],
          servePrice: '',
        });
      }

      setServeList([...serveList]);

      updateServeEditState(id);

      if (isAdd) {
        handleEditServe(id + 1);
      }

      MessagePlugin.success('服务添加成功');
    });
  };

  // 行数据编辑时触发，返回最新输入结果
  const onServeRowEdit: TableProps['onRowEdit'] = (params) => {
    const { row, col, value } = params;
    const oldRowData = editServeMap[row.id.toString()]?.editedRow || row;
    const editedRow = { ...oldRowData, [col.colKey]: value };
    editServeMap[row.id.toString()] = {
      ...params,
      editedRow,
    };
    setEditServeMap(editServeMap);
  };

  const serveColumns: TableProps['columns'] = [
    {
      colKey: 'serveName',
      title: '服务名称',
      width: '28%',
      edit: {
        component: InputTD,
        // 透传至component中
        props: {
          clearable: true,
          placeholder: '请输入服务名称',
        },
        showEditIcon: false,
        // 校验规则
        rules: [{ required: true, message: '服务名称不能为空' }],
      },
    },
    {
      colKey: 'serveTitle',
      title: '服务标签',
      width: '28%',
      cell({ row }) {
        return row.serveTitle && row.serveTitle.join(' | ');
      },
      edit: {
        component: CustomTag,
        // 透传至component中
        props: {
          placeholder: '请输入服务标签',
        },
        showEditIcon: false,
        // 校验规则
        rules: [{ required: true, message: '服务标签不能为空' }],
      },
    },
    {
      colKey: 'servePrice',
      title: '服务价格',
      width: '28%',
      edit: {
        component: InputNumber,
        // 透传至component中
        props: {
          placeholder: '请输入服务价格',
          theme: 'normal',
          min: 0,
          decimalPlaces: 0,
          allowInputOverLimit: false,
          suffix: <span>元</span>,
        },
        showEditIcon: false,
        // 校验规则
        rules: [{ required: true, message: '服务价格不能为空' }],
      },
    },
    {
      title: '操作栏',
      colKey: 'operate',
      cell: ({ row }) => {
        const editServeTable = editServeTableRowKeys.includes(row.id);

        return (
          <>
            {!editServeTable && (
              <Space>
                <Button theme='default' shape='circle' icon={<EditIcon />} onClick={() => handleEditServe(row.id)} />
                <Button
                  theme='default'
                  shape='circle'
                  icon={<DeleteIcon />}
                  onClick={() => handleDeleteServe(row.id)}
                />
              </Space>
            )}

            {editServeTable && (
              <Space>
                <Button
                  theme='default'
                  shape='circle'
                  icon={<SaveIcon />}
                  data-id={row.id}
                  onClick={() =>
                    handleAddServe(
                      row.id,
                      strIsNull(row.serveName) || strIsNull(row.serveTitle) || strIsNull(row.servePrice),
                    )
                  }
                />
                {!strIsNull(row.serveName) && !strIsNull(row.serveTitle) && !strIsNull(row.servePrice) && (
                  <Button
                    theme='default'
                    shape='circle'
                    icon={<MapCancelIcon />}
                    onClick={() => updateServeEditState(row.id)}
                  />
                )}
              </Space>
            )}
          </>
        );
      },
    },
  ];

  const updateAddressEditState = (id) => {
    // 更新 editableRowKeys
    const index = editAddressTableRowKeys.findIndex((t) => t === id);
    editAddressTableRowKeys.splice(index, 1);
    setEditAddressTableRowKeys([...editAddressTableRowKeys]);
  };

  const handleEditAddress = (id) => {
    if (!editAddressTableRowKeys.includes(id)) {
      setEditAddressTableRowKeys((prev) => [...prev, id]);
    }
  };

  const handleDeleteAddress = (id) => {
    const list = addressList.filter((item) => item.id !== id);

    setAddressList([...list]);

    delete editAddressMap[id.toString()];
  };

  const handleAddAddress = (id, isAdd) => {
    addressTableRef.current?.validateRowData(id).then((params) => {
      if (params.result.length) return;

      const current = editAddressMap[id.toString()];

      addressList.splice(current.rowIndex, 1, current.editedRow);

      if (isAdd) {
        addressList.push({
          id: id + 1,
          abbrName: '',
          addressDetails: undefined,
        });
      }

      setAddressList([...addressList]);

      updateAddressEditState(id);

      if (isAdd) {
        handleEditAddress(id + 1);
      }

      MessagePlugin.success('校区添加成功');
    });
  };

  // 行数据编辑时触发，返回最新输入结果
  const onAddressRowEdit: TableProps['onRowEdit'] = (params) => {
    const { row, col, value } = params;
    const oldRowData = editAddressMap[row.id.toString()]?.editedRow || row;
    const editedRow = { ...oldRowData, [col.colKey]: value };
    editAddressMap[row.id.toString()] = {
      ...params,
      editedRow,
    };
    setEditAddressMap(editAddressMap);
  };

  const addressColumns: TableProps['columns'] = [
    {
      colKey: 'abbrName',
      title: '校区简称',
      width: '28%',
      edit: {
        component: InputTD,
        // 透传至component中
        props: {
          clearable: true,
          placeholder: '请输入校区简称',
        },
        showEditIcon: false,
        // 校验规则
        rules: [{ required: true, message: '校区简称不能为空' }],
      },
    },
    {
      colKey: 'addressDetails',
      title: '校区地址',
      cell: ({ row }) => <span>{row?.addressDetails?.addressDetails}</span>,
      edit: {
        component: CustomAddressMap,
        // 透传至component中
        props: ({ row }) => ({
          id: row.id,
        }),
        showEditIcon: false,
        // 校验规则
        rules: [{ required: true, message: '校区地址不能为空' }],
      },
    },
    {
      title: '操作栏',
      colKey: 'operate',
      width: '15.6%',
      cell: ({ row }) => {
        const editAddressTable = editAddressTableRowKeys.includes(row.id);

        return (
          <>
            {!editAddressTable && (
              <Space>
                <Button theme='default' shape='circle' icon={<EditIcon />} onClick={() => handleEditAddress(row.id)} />
                <Button
                  theme='default'
                  shape='circle'
                  icon={<DeleteIcon />}
                  onClick={() => handleDeleteAddress(row.id)}
                />
              </Space>
            )}

            {editAddressTable && (
              <Space>
                <Button
                  theme='default'
                  shape='circle'
                  icon={<SaveIcon />}
                  data-id={row.id}
                  onClick={() => handleAddAddress(row.id, strIsNull(row.abbrName) || strIsNull(row.addressDetails))}
                />
                {!strIsNull(row.abbrName) && !strIsNull(row.addressDetails) && (
                  <Button
                    theme='default'
                    shape='circle'
                    icon={<MapCancelIcon />}
                    onClick={() => updateAddressEditState(row.id)}
                  />
                )}
              </Space>
            )}
          </>
        );
      },
    },
  ];

  const handleChangeAgencyDesc = (val: string) => {
    formRef.current?.setFieldsValue?.({ agencyDesc: val });

    setAgencyDesc(val);
  };

  const handleChangeHonors = (val: string) => {
    formRef.current?.setFieldsValue?.({ honors: val });

    setHonors(val);
  };

  const handleUploadPicList = ({
    file: {
      response: {
        data: { id, fileUrl },
      },
    },
  }: {
    file: { response: { data: { id: number; fileUrl: string } } };
  }) => {
    const list = JSON.parse(JSON.stringify(picList));
    list.push({ url: fileUrl, id });
    setPicList(list);
  };

  const handleRemovePicList = ({ id }) => {
    const list = picList.filter((item) => item.id !== id);

    setPicList(list);
  };

  useEffect(() => {
    dispatch(getMechanismInfo(id));

    return () => {
      dispatch(clearPageState());
    };
  }, []);

  useEffect(() => {
    if (!strIsNull(mechanismInfo)) {
      const info = JSON.parse(JSON.stringify(mechanismInfo));

      info.picVoList = info.picVoList.map((item) => ({
        id: item.backImgId,
        url: item.backImgUrl,
      }));

      info.teacherVoList = info.teacherVoList.map((item) => ({
        id: item.id,
        teacherName: item.teacherName,
        teacherTitle: JSON.parse(item.teacherTitle),
        headImgId: {
          id: item.headImgId,
          url: item.headImgUrl,
        },
      }));

      const maxTeacherId = info.teacherVoList.reduce((max, item) => (item.id > max ? item.id : max), 0) + 1;

      info.teacherVoList.push({
        id: maxTeacherId,
        teacherName: '',
        teacherTitle: [],
        headImgId: {},
      });

      handleEditTeacher(maxTeacherId);

      info.serveVoList = info.serveVoList.map((item) => ({
        id: item.id,
        serveName: item.serveName,
        serveTitle: JSON.parse(item.serveTitle),
        servePrice: item.servePrice,
      }));

      const maxServeId = info.serveVoList.reduce((max, item) => (item.id > max ? item.id : max), 0) + 1;

      info.serveVoList.push({
        id: maxServeId,
        serveName: '',
        serveTitle: [],
        servePrice: '',
      });

      handleEditServe(maxServeId);

      info.addressVoList = info.addressVoList.map((item) => ({
        id: item.id,
        abbrName: item.abbrName,
        addressDetails: {
          province: item.province,
          city: item.city,
          area: item.district,
          country: item.township,
          village: item.village,
          addressDetails: item.addressDetails,
          longitude: item.longitude.toString(),
          latitude: item.latitude.toString(),
          addressName: item.addressName,
        },
      }));

      const maxAddressId = info.addressVoList.reduce((max, item) => (item.id > max ? item.id : max), 0) + 1;

      info.addressVoList.push({
        id: maxAddressId,
        abbrName: '',
        addressDetails: undefined,
      });

      handleEditAddress(maxAddressId);

      formRef.current?.setFieldsValue?.(info);

      setAgencyDesc(info.agencyDesc);
      setHonors(info.honors);
      setPicList(info.picVoList);
      setTeacherList(info.teacherVoList);
      setServeList(info.serveVoList);
      setAddressList(info.addressVoList);
    }
  }, [mechanismInfo]);

  useEffect(() => {
    formRef.current?.setFieldsValue?.({ picList: picList.map(({ id }) => id) });
  }, [picList]);

  useEffect(() => {
    const newList = teacherList.slice(0, -1).map((item) => ({
      teacherName: item.teacherName,
      teacherTitle: JSON.stringify(item.teacherTitle),
      headImgId: item.headImgId.id,
    }));

    formRef.current?.setFieldsValue?.({ teacherList: newList });
  }, [teacherList]);

  useEffect(() => {
    const newList = serveList.slice(0, -1).map((item) => ({
      serveName: item.serveName,
      serveTitle: JSON.stringify(item.serveTitle),
      servePrice: item.servePrice,
    }));

    formRef.current?.setFieldsValue?.({ serveList: newList });
  }, [serveList]);

  useEffect(() => {
    const newList = addressList.slice(0, -1).map((item) => {
      const obj = JSON.parse(JSON.stringify(item));

      return {
        abbrName: item.abbrName,
        ...obj.addressDetails,
      };
    });

    formRef.current?.setFieldsValue?.({ addressList: newList });
  }, [addressList]);

  return (
    <Loading
      loading={mechanismInfoLoading}
      showOverlay
      style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      <Back path='/srRanking/mechanism' header='单招榜-机构榜' />

      <div className={classnames(CommonStyle.pageWithColor)}>
        <Form
          ref={formRef}
          form={form}
          initialData={INITIAL_DATA}
          resetType='initial'
          rules={rules}
          colon
          className={classnames(CommonStyle.commonSubjectForm)}
          onSubmit={handleSubmit}
          onReset={handleReset}
        >
          <Row justify='space-between' gutter={48}>
            <Col span={6}>
              <Space direction='vertical'>
                <Row justify='space-between' gutter={48}>
                  <Col span={6}>
                    <Input label='机构名称' name='name' clearable />
                  </Col>
                  <Col span={6}>
                    <Input label='机构简称' name='shortName' clearable />
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <FormItem label='机构简介' name='agencyDesc'>
                      <Editor
                        fixedToolbar='hide'
                        border
                        text={agencyDesc}
                        maxLength={2000}
                        uploadVideoConfig={{ allowedFileTypes: ['mp4'], maxFileSize: 30 }}
                        uploadImgConfig={{ allowedFileTypes: ['jpg', 'jpeg', 'png'], maxFileSize: 5 }}
                        onChange={handleChangeAgencyDesc}
                      />
                    </FormItem>
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <FormItem label='师资团队' name='teacherList'>
                      <Table
                        ref={teacherTableRef}
                        data={teacherList}
                        columns={teacherColumns}
                        rowKey='id'
                        editableRowKeys={editTeacherTableRowKeys}
                        lazyLoad
                        onRowEdit={onTeacherRowEdit}
                      />
                    </FormItem>
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <Textarea label='场地校舍描述' name='venueDesc' clearable />
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <FormItem label='场地校舍图片' name='picList'>
                      <Upload
                        url='/oss/uploadFile'
                        theme='image'
                        tips='请上传 .jpg/.jpeg/.png 格式的图片 文件大小5M'
                        accept='.jpg, .jpeg, .png'
                        maxFileSize={5}
                        draggable
                        success={handleUploadPicList}
                        remove={handleRemovePicList}
                        files={picList}
                        params={{
                          businessType: 16,
                        }}
                      />
                    </FormItem>
                  </Col>
                </Row>
              </Space>
            </Col>
            <Col span={6}>
              <Space direction='vertical'>
                <Row>
                  <Col span={12}>
                    <Input label='联系电话' name='phoneNumber' clearable />
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <FormItem label='荣誉资质' name='honors'>
                      <Editor
                        fixedToolbar='hide'
                        border
                        text={honors}
                        maxLength={2000}
                        uploadVideoConfig={{ allowedFileTypes: ['mp4'], maxFileSize: 30 }}
                        uploadImgConfig={{ allowedFileTypes: ['jpg', 'jpeg', 'png'], maxFileSize: 5 }}
                        onChange={handleChangeHonors}
                      />
                    </FormItem>
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <FormItem label='精选服务' name='serveList'>
                      <Table
                        ref={serveTableRef}
                        data={serveList}
                        columns={serveColumns}
                        rowKey='id'
                        editableRowKeys={editServeTableRowKeys}
                        lazyLoad
                        onRowEdit={onServeRowEdit}
                      />
                    </FormItem>
                  </Col>
                </Row>
                <Row>
                  <Col span={12}>
                    <FormItem label='机构校区' name='addressList'>
                      <Table
                        ref={addressTableRef}
                        data={addressList}
                        columns={addressColumns}
                        rowKey='id'
                        editableRowKeys={editAddressTableRowKeys}
                        lazyLoad
                        onRowEdit={onAddressRowEdit}
                      />
                    </FormItem>
                  </Col>
                </Row>
              </Space>
            </Col>
          </Row>
          <FormItem className={classnames(CommonStyle.commonSubjectFormBtn)}>
            <Space>
              <Button loading={addEditMechanismLoading} type='submit' theme='primary'>
                提交
              </Button>
              <Button theme='default' onClick={handleCancel}>
                取消
              </Button>
              <Button type='reset' theme='warning'>
                重置
              </Button>
            </Space>
          </FormItem>
        </Form>
      </div>
    </Loading>
  );
};

export default EditMechanism;
