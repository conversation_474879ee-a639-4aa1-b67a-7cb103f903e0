import { TdTabPanelProps } from 'tdesign-react';
import ScaleTable from './ScaleTable';
import EnrollmentTable from './EnrollmentTable';
import EmployTable from './EmployTable';

export const TAB_LIST: Array<TdTabPanelProps> = [
  {
    lazy: true,
    value: 1,
    label: '规模榜',
    // eslint-disable-next-line react/react-in-jsx-scope
    panel: <ScaleTable />,
  },
  {
    lazy: true,
    value: 2,
    label: '招生榜',
    // eslint-disable-next-line react/react-in-jsx-scope
    panel: <EnrollmentTable />,
  },
  {
    lazy: true,
    value: 3,
    label: '就业榜',
    // eslint-disable-next-line react/react-in-jsx-scope
    panel: <EmployTable />,
  },
];
