import React from 'react';
import { List, Button } from 'tdesign-react';
import { EditIcon, DownloadIcon } from 'tdesign-icons-react';
import './index.less';

const { ListItem, ListItemMeta } = List;

interface IListItemData<T = any> {
  listProps?: object;
  style?: object;
  title?: string;
  description?: string;
  image?: string;
  action?: React.ReactElement;
  listItemProps?: T;
}

interface IListData {
  listProps?: object;
  IListItemData: IListItemData[];
}

interface IData extends React.HTMLAttributes<HTMLElement> {
  listData: IListData;
}

const BasicList: React.FC<IData> = ({ listData }) => (
  <List {...listData.listProps}>
    {listData.IListItemData.map((item, index) => (
      <ListItem
        key={index}
        {...item.listItemProps}
        action={
          <>
            <Button variant='text' shape='square'>
              <EditIcon />
            </Button>
            <Button variant='text' shape='square'>
              <DownloadIcon />
            </Button>
          </>
        }
      >
        <ListItemMeta {...item} />
      </ListItem>
    ))}
  </List>
);

export default React.memo(BasicList);
