import { IDragSort } from 'types';
import {
  IAddHotBo,
  IEditHotBo,
  IGetEnrollmentListBo,
  IGetSalaryListBo,
  IGetHotListBo,
  IGetInstitutionListBo,
} from 'types/srRanking';
import request from 'utils/request/index';

/**
 * 获取招生榜列表
 * @param params
 * @returns
 */
export const getEnrollmentListApi = (params: IGetEnrollmentListBo) =>
  request.get({
    url: '/admBoard/getMajorsEnrollmentList',
    params,
  });

/**
 * 获取薪资榜列表
 * @param params
 * @returns
 */
export const getSalaryListApi = (params: IGetSalaryListBo) =>
  request.get({
    url: '/admBoard/getMajorsSalaryList',
    params,
  });

/**
 * 获取热门推荐榜列表
 * @param params
 * @returns
 */
export const getHotListApi = (params: IGetHotListBo) =>
  request.get({
    url: '/admBoard/getMajorsHotList',
    params,
  });

/**
 * 获取热门推荐磅信息
 * @param id
 * @returns
 */
export const getHotInfoApi = (id: number) =>
  request.get({
    url: `/majorsHot/getMajorsHotDetail/${id}`,
  });

/**
 * 根据考试ID查询专业下拉框
 * @param id
 * @returns
 */
export const getMajorsListByExamIdApi = (id: number) =>
  request.get({
    url: `/majorsHot/getMajorsList/${id}`,
  });

/**
 * 根据专业ID和考试ID查询院校信息
 * @param params
 * @returns
 */
export const getInstitutionListApi = (params: IGetInstitutionListBo) =>
  request.get({
    url: '/admBoard/getEducationalList',
    params,
  });

/**
 * 热门推荐榜拖动排序
 * @param data
 * @returns
 */
export const dragSortHotApi = (data: IDragSort) =>
  request.put({
    url: '/majorsHot/majorsHotDragSort',
    data,
  });

/**
 * 添加热门推荐榜
 * @param data
 * @returns
 */
export const addHotApi = (data: IAddHotBo) =>
  request.post({
    url: '/majorsHot/save',
    data,
  });

/**
 * 编辑热门推荐榜
 * @param data
 * @returns
 */
export const editHotApi = (data: IEditHotBo) =>
  request.put({
    url: '/majorsHot/edit',
    data,
  });

/**
 * 删除热门推荐榜
 * @param data
 * @returns
 */
export const deleteHotApi = (id: number) =>
  request.delete({
    url: `/majorsHot/delete/${id}`,
  });
