import React from 'react';
import Style from './phone.module.less';
import { useAppSelector } from 'modules/store';
import { selectResourceManageQuestionSlice } from 'modules/resourceManage/question';
import { LaTeX } from 'components';

const QuizApp = () => {
  const { answer, stem, questionAnalysis, questionProvenance, optionBoList } =
    useAppSelector(selectResourceManageQuestionSlice);

  return (
    <div>
      <div className={Style['app-title']}>小程序前台预览</div>
      <div className={Style.app}>
        <div className={Style['exercise-container']}>
          <div className={Style.header}>
            <a href='#' className={Style['back-button']}>
              &larr;
            </a>
            <div className={Style.title}>英语-自由练习</div>
          </div>
          <div className={Style['question-container']}>
            <div className={Style.question}>
              <div className={Style['question-progress']}>
                <p>单项选择题</p>
                <p>1/10</p>
              </div>
              <p className={Style['question-text']}>
                <LaTeX content={stem ?? '暂无数据'} />
              </p>
              <p className={Style['question-source']}>{questionProvenance}</p>
            </div>
            <div className={Style.options}>
              {optionBoList.map((t, i) => (
                <div key={i} className={Style.option}>
                  <span>{t.opt}</span>
                  <span> | </span>
                  <span>
                    <LaTeX content={t.optionContent ?? '暂无数据'} />
                  </span>
                </div>
              ))}
            </div>
            <div className={Style.explanation}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <p className={Style['correct-answer']}>解析</p>
                <p className={Style['correct-answer']}>正确答案：{answer}</p>
              </div>
              <p className={Style['answer-analysis']}>
                <LaTeX content={questionAnalysis ?? '暂无数据'} />
              </p>
            </div>
          </div>
          <div className={Style.footer}>
            <button className={Style['collect-button']}>❤️ 收藏</button>
            <button className={Style['settings-button']}>⚙️ 设置</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizApp;
