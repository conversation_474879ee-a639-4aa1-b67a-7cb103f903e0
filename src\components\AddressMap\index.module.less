.search-input {
  position: relative;
  margin: 0;
  padding: 0 var(--td-comp-paddingLR-s);
  list-style: none;
  height: var(--td-comp-size-m);
  border-width: 1px;
  border-style: solid;
  border-radius: var(--td-radius-default);
  border-color: var(--td-border-level-2-color);
  background-color: var(--td-bg-color-specialcomponent);
  outline: none;
  color: var(--td-text-color-primary);
  font: var(--td-font-body-medium);
  width: 100%;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: border cubic-bezier(0.38, 0, 0.24, 1) 0.2s, background-color cubic-bezier(0.38, 0, 0.24, 1) 0.2s, -webkit-box-shadow cubic-bezier(0.38, 0, 0.24, 1) 0.2s;
  transition: border cubic-bezier(0.38, 0, 0.24, 1) 0.2s, background-color cubic-bezier(0.38, 0, 0.24, 1) 0.2s, -webkit-box-shadow cubic-bezier(0.38, 0, 0.24, 1) 0.2s;
  transition: border cubic-bezier(0.38, 0, 0.24, 1) 0.2s, box-shadow cubic-bezier(0.38, 0, 0.24, 1) 0.2s, background-color cubic-bezier(0.38, 0, 0.24, 1) 0.2s;
  transition: border cubic-bezier(0.38, 0, 0.24, 1) 0.2s, box-shadow cubic-bezier(0.38, 0, 0.24, 1) 0.2s, background-color cubic-bezier(0.38, 0, 0.24, 1) 0.2s, -webkit-box-shadow cubic-bezier(0.38, 0, 0.24, 1) 0.2s;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
}

.search-input:focus {
  z-index: 1;
  border-color: var(--td-brand-color);
  -webkit-box-shadow: 0 0 0 2px var(--td-brand-color-focus);
  box-shadow: 0 0 0 2px var(--td-brand-color-focus);
}

.AMapComponent {
  flex: auto;

  :global {
    .amap-logo {
      display: none !important;
      visibility: hidden !important;
    }

    .amap-copyright {
      display: none !important;
      visibility: hidden !important;
    }
  }
}
