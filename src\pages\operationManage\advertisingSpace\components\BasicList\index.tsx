import React, { useState, useEffect } from 'react';
import { Menu } from 'tdesign-react';
import { useAppDispatch } from 'modules/store';
import { setActive } from 'modules/directory/association';
import Style from './index.module.less';
import './index.less';

interface IListItemData<T = any> {
  listProps?: object;
  style?: object;
  title?: string;
  description?: string;
  image?: string;
  action?: React.ReactElement;
  listItemProps?: T;
  id?: T;
  name?: string;
  educationalId: number;
  educationalName: string;
  educationalDisciplines: number;
}

interface IListData<T = any> {
  active?: T;
  broadsideList?: IListItemData[];
}

interface IData extends React.HTMLAttributes<HTMLElement> {
  broadsideList?: IListData;
}

const { MenuItem } = Menu;

const BasicList: React.FC<IData> = ({ broadsideList }) => {
  const dispatch = useAppDispatch();

  const [listItemData, setListItemData] = useState<IListItemData[]>([
    {
      educationalId: 2,
      educationalName: '首页-Banner',
      educationalDisciplines: 1,
    },
  ]);

  useEffect(() => {
    setListItemData([
      {
        educationalId: 2,
        educationalName: '首页-Banner',
        educationalDisciplines: 1,
      },
    ]);
  }, [broadsideList]);

  return (
    <React.Fragment>
      <Menu
        defaultValue={2}
        value={2}
        onChange={(v) => {
          dispatch(setActive(v as number));
        }}
        style={{ marginRight: 20 }}
      >
        {listItemData.length !== 0 &&
          listItemData.map((item) => (
            <MenuItem style={{ width: '100%' }} value={item.educationalId} key={item.educationalId}>
              <div className={Style.content}>
                <span>{item.educationalName}</span>
              </div>
            </MenuItem>
          ))}
        {listItemData.length === 0 && <div style={{ textAlign: 'center' }}>暂无数据</div>}
      </Menu>
    </React.Fragment>
  );
};

export default BasicList;
