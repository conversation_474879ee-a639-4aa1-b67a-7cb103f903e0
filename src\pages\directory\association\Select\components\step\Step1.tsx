import React, { useState, memo, useEffect } from 'react';
import { Table, Input, Row, InputAdornment, Col, type InputValue } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectAssociation, getUnrelatedMajorsList } from 'modules/directory/association';
import { usePagingV2 } from 'hooks/usePagingV2';
import { throttle } from 'lodash';
import { MessagePlugin } from 'tdesign-react/es/message/Message';

interface Step1Props {
  specialityId: (string | number)[];
  onSelectChange: (val: object[]) => void;
}

const Step1: React.FC<Step1Props> = ({ onSelectChange, specialityId }) => {
  const dispatch = useAppDispatch();
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>(specialityId);
  const {
    active,
    step1: { loading, list, pageNum, pageSize, total },
  } = useAppSelector(selectAssociation);
  const pagination = usePagingV2(getUnrelatedMajorsList, { educationalId: active }, { pageNum, pageSize, total });

  const getList = (val: InputValue = '') => {
    dispatch(
      getUnrelatedMajorsList({
        pageSize,
        pageNum,
        educationalId: active,
        majorsName: val,
      }),
    );
  };
  useEffect(() => {
    getList();
  }, [active]);

  const throttledDispatch = throttle((value) => {
    getList(value);
  }, 1500);

  const onChange = (value: InputValue) => {
    throttledDispatch(value);
  };

  return (
    <>
      <Row style={{ marginTop: '20px' }} gutter={8} align='middle'>
        <Col>
          <InputAdornment prepend='专业名称'>
            <Input
              clearable
              placeholder='请输入专业名称'
              onChange={(value) => {
                onChange(value);
              }}
            />
          </InputAdornment>
        </Col>
      </Row>
      <Table
        maxHeight={370}
        verticalAlign='middle'
        style={{ marginTop: '20px' }}
        loading={loading}
        data={list}
        columns={[
          {
            align: 'center',
            colKey: 'row-select',
            type: 'single',
            width: 72,
          },
          {
            title: '专业名称',
            fixed: 'left',
            align: 'left',
            ellipsis: true,
            colKey: 'majorsName',
          },
          {
            title: '所属大类',
            colKey: 'categoryParentName',
          },
          {
            title: '所属二类',
            ellipsis: true,
            colKey: 'categoryChildrenName',
          },
        ]}
        selectOnRowClick={true}
        selectedRowKeys={selectedRowKeys}
        rowKey='majorsId'
        hover
        bordered
        onSelectChange={(val) => {
          if (list.length > 0) {
            const copyVal = JSON.parse(JSON.stringify(val));

            onSelectChange(copyVal.map((value: number) => list.find((item) => item.majorsId === value)));

            setSelectedRowKeys(val);
          } else {
            MessagePlugin.error('请先选择专业');
          }
        }}
        pagination={pagination}
      />
    </>
  );
};

export default memo(Step1);
