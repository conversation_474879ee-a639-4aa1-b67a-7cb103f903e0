import { IQuesCodeEntity, IQuesCodeQueryBo, IQrCode } from 'types/resourceManage';
import request from 'utils/request/index';

/**
 * 二维码查询列表
 * @param params
 * @returns
 */
export const quesCodeGetList = async (params: IQuesCodeQueryBo) => {
  const result = request.get({
    url: '/quesCode/getList',
    params,
  });
  return result;
};

/**
 * 生成视频二维码
 * @param params
 * @returns
 */
export const quesCodeCrateQrCode = async (params: IQuesCodeEntity) => {
  const result = request.post({
    url: '/quesCode/crateQrCode',
    params,
  });
  return result;
};

/**
 * 保存二维码
 * @param params
 * @returns
 */
export const uploadQrCode = async (params: IQrCode) => {
  const result = request.post({
    url: '/quesCode/uploadQrCode',
    params,
  });
  return result;
};
/**
 * 新增视频二维码信息
 * @param params
 * @returns
 */
export const quesCodeSave = async (params: IQuesCodeEntity) => {
  const result = request.post({
    url: '/quesCode/save',
    params,
  });
  return result;
};
/**
 * 编辑视频二维码信息
 * @param params
 * @returns
 */
export const quesCodeEdit = async (params: IQuesCodeEntity) => {
  const result = request.put({
    url: '/quesCode/edit',
    params,
  });
  return result;
};
/**
 * 根据ID删除信息
 * /quesCode/delete/1
 * @param params
 * @returns
 */
export const quesCodeDelete = async (qrCodeId: number) => {
  const result = request.delete({
    url: `/quesCode/delete/${qrCodeId}`,
  });
  return result;
};
/**
 * 根据ID查询
 * /quesCode/getById/1
 * @param params
 * @returns
 */
export const quesCodeGetById = async (qrCodeId: string) => {
  const result = request.get({
    url: `/quesCode/getById/${qrCodeId}`,
  });
  return result;
};
/**
 * 根据ID查询
 * /quesCode/getById/1
 * @param params
 * @returns
 */

// "appId": "string",
// "fileId": "string"

export const quesCodePvwVideo = async (params: string) => {
  const result = request.post({
    url: '/quesCode/pvwVideo',
    params,
  });
  return result;
};
export const quesCodeDeleteQrCode = async (qrCodeId: string) => {
  const result = request.delete({
    url: `/quesCode/deleteQrCode/${qrCodeId}`,
  });
  return result;
};
