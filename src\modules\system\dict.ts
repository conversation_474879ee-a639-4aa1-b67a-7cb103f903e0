import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getSystemDictListApi,
  delSystemDictApi,
  addSystemDictApi,
  editSystemDict<PERSON>pi,
  getSystemDictDetailsInfoApi,
  refreshSystemDictCache<PERSON>pi,
  getSystemDictDataList<PERSON>pi,
  delSystemDictData<PERSON>pi,
  addSystemDictDataApi,
  editSystemDictDataApi,
  getSystemDictDataDetailsInfoApi,
  getSystemDictAllListApi,
} from 'api';
import { MessagePlugin } from 'tdesign-react/es/message/Message';
import {
  IAddDictBo,
  IEditDictBo,
  IGetDictListBo,
  IAddDictDataBo,
  IEditDictDataBo,
  IGetDictDataListBo,
} from 'types/system';

const namespace = 'system/dict';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  addEditLoading: boolean;
  delLoading: boolean;
  infoLoading: boolean;
  info: any;
  refreCacheLoading: boolean;
  allList: Array<any>;
}

const initialState: IInitialState = {
  loading: true,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  addEditLoading: false,
  delLoading: false,
  infoLoading: false,
  info: {},
  refreCacheLoading: false,
  allList: [],
};

export const getSystemDictList = createAsyncThunk(`${namespace}/getSystemDictList`, async (bo: IGetDictListBo) => {
  const data = await getSystemDictListApi(bo);

  return {
    list: data.rows,
    total: data.total,
    pageNum: bo.pageNum,
    pageSize: bo.pageSize,
  };
});

export const addSystemDict = createAsyncThunk(`${namespace}/addSystemDict`, async (bo: IAddDictBo) => {
  const { data } = await addSystemDictApi(bo);

  return data;
});

export const editSystemDict = createAsyncThunk(`${namespace}/editSystemDict`, async (bo: IEditDictBo) => {
  await editSystemDictApi(bo);
});

export const delSystemDict = createAsyncThunk(`${namespace}/delSystemDict`, async (dictId: number | string) => {
  await delSystemDictApi(dictId);
});

export const getSystemDictDetailsInfo = createAsyncThunk(
  `${namespace}/getSystemDictDetailsInfo`,
  async (dictId: number) => {
    const { data } = await getSystemDictDetailsInfoApi(dictId);

    return data;
  },
);

export const refreshSystemDictCache = createAsyncThunk(`${namespace}/refreshSystemDictCache`, async () => {
  await refreshSystemDictCacheApi();
});

export const getSystemDictDataList = createAsyncThunk(
  `${namespace}/getSystemDictDataList`,
  async (bo: IGetDictDataListBo) => {
    const data = await getSystemDictDataListApi(bo);

    return {
      list: data.rows,
      total: data.total,
      pageNum: bo.pageNum,
      pageSize: bo.pageSize,
    };
  },
);

export const addSystemDictData = createAsyncThunk(`${namespace}/addSystemDictData`, async (bo: IAddDictDataBo) => {
  const { data } = await addSystemDictDataApi(bo);

  return data;
});

export const editSystemDictData = createAsyncThunk(`${namespace}/editSystemDictData`, async (bo: IEditDictDataBo) => {
  await editSystemDictDataApi(bo);
});

export const delSystemDictData = createAsyncThunk(
  `${namespace}/delSystemDictData`,
  async (dictCode: number | string) => {
    await delSystemDictDataApi(dictCode);
  },
);

export const getSystemDictDataDetailsInfo = createAsyncThunk(
  `${namespace}/getSystemDictDataDetailsInfo`,
  async (dictCode: number) => {
    const { data } = await getSystemDictDataDetailsInfoApi(dictCode);

    return data;
  },
);

export const getSystemDictAllList = createAsyncThunk(`${namespace}/getSystemDictAllList`, async () => {
  const { data } = await getSystemDictAllListApi();

  return data;
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSystemDictList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSystemDictList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getSystemDictList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(addSystemDict.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(addSystemDict.fulfilled, (state) => {
        MessagePlugin.success('添加成功');
        state.addEditLoading = false;
      })
      .addCase(addSystemDict.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(editSystemDict.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(editSystemDict.fulfilled, (state) => {
        MessagePlugin.success('编辑成功');
        state.addEditLoading = false;
      })
      .addCase(editSystemDict.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(delSystemDict.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(delSystemDict.fulfilled, (state) => {
        MessagePlugin.success('删除成功');
        state.delLoading = false;
      })
      .addCase(delSystemDict.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(getSystemDictDetailsInfo.pending, (state) => {
        state.infoLoading = true;
      })
      .addCase(getSystemDictDetailsInfo.fulfilled, (state, action) => {
        state.info = action.payload;
        state.infoLoading = false;
      })
      .addCase(getSystemDictDetailsInfo.rejected, (state) => {
        state.infoLoading = false;
      })

      .addCase(refreshSystemDictCache.pending, (state) => {
        state.refreCacheLoading = true;
      })
      .addCase(refreshSystemDictCache.fulfilled, (state) => {
        state.refreCacheLoading = false;
        MessagePlugin.success('刷新成功');
      })
      .addCase(refreshSystemDictCache.rejected, (state) => {
        state.refreCacheLoading = false;
      })

      .addCase(getSystemDictDataList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSystemDictDataList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getSystemDictDataList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(addSystemDictData.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(addSystemDictData.fulfilled, (state) => {
        MessagePlugin.success('添加成功');
        state.addEditLoading = false;
      })
      .addCase(addSystemDictData.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(editSystemDictData.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(editSystemDictData.fulfilled, (state) => {
        MessagePlugin.success('编辑成功');
        state.addEditLoading = false;
      })
      .addCase(editSystemDictData.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(delSystemDictData.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(delSystemDictData.fulfilled, (state) => {
        MessagePlugin.success('删除成功');
        state.delLoading = false;
      })
      .addCase(delSystemDictData.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(getSystemDictDataDetailsInfo.pending, (state) => {
        state.infoLoading = true;
      })
      .addCase(getSystemDictDataDetailsInfo.fulfilled, (state, action) => {
        state.info = action.payload;
        state.infoLoading = false;
      })
      .addCase(getSystemDictDataDetailsInfo.rejected, (state) => {
        state.infoLoading = false;
      })

      .addCase(getSystemDictAllList.fulfilled, (state, action) => {
        state.allList = action.payload;
      });
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectSystemDict = (state: RootState) => state.systemDict;

export default listBaseSlice.reducer;
