import React, { memo, useRef } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back } from 'components';
import { Button, Form, FormInstanceFunctions, FormProps, Input, Loading, Space, Radio, Textarea } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectSystemDict, addSystemDict } from 'modules/system/dict';
import { useNavigate } from 'react-router-dom';
import { IAddDictBo } from 'types/system';

const { FormItem } = Form;

const AddDict: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { infoLoading, addEditLoading } = useAppSelector(selectSystemDict);
  const formRef = useRef<FormInstanceFunctions>();

  const INITIAL_DATA = {
    dictName: '',
    dictType: '',
    status: '0',
    remark: '',
  };

  const rules: FormProps['rules'] = {
    dictName: [
      {
        required: true,
        message: '字典名称不能为空',
        trigger: 'all',
      },
    ],
    dictType: [
      {
        required: true,
        message: '字典类型不能为空',
        trigger: 'all',
      },
      {
        pattern: /^[a-z][a-z0-9_]*$/,
        message: '字典类型必须以字母开头，且只能为（小写字母，数字，下滑线）',
        trigger: 'all',
      },
    ],
  };

  const handleSubmit: FormProps['onSubmit'] = async ({ validateResult, fields }) => {
    if (validateResult === true) {
      const bo: IAddDictBo = {
        ...fields,
      };

      await dispatch(addSystemDict(bo));
    }
  };

  const handleCancel = () => {
    navigate('/system/dict');
  };

  return (
    <Loading loading={infoLoading} showOverlay style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Back path='/system/dict' header='系统管理-字典管理' />
      <div className={classnames(CommonStyle.pageWithColor)}>
        <Form
          ref={formRef}
          initialData={INITIAL_DATA}
          resetType='initial'
          rules={rules}
          colon
          className={classnames(CommonStyle.commonSubjectForm)}
          onSubmit={handleSubmit}
        >
          <FormItem label='字典名称' name='dictName'>
            <Input placeholder='请输入字典名称' clearable />
          </FormItem>
          <FormItem label='字典类型' name='dictType'>
            <Input placeholder='请输入字典类型' clearable />
          </FormItem>
          <FormItem label='字典状态' name='status'>
            <Radio.Group>
              <Radio value='0'>正常</Radio>
              <Radio value='1'>停用</Radio>
            </Radio.Group>
          </FormItem>
          <FormItem label='备注' name='remark'>
            <Textarea placeholder='请输入备注' />
          </FormItem>
          <FormItem className={classnames(CommonStyle.commonSubjectFormBtn)}>
            <Space>
              <Button loading={addEditLoading} type='submit' theme='primary'>
                提交
              </Button>
              <Button theme='default' onClick={handleCancel}>
                取消
              </Button>
              <Button type='reset' theme='warning'>
                重置
              </Button>
            </Space>
          </FormItem>
        </Form>
      </div>
    </Loading>
  );
};

export default memo(AddDict);
