import React from 'react';
import { SettingIcon } from 'tdesign-icons-react';
import { Button } from 'tdesign-react';

interface CustomColumnsProps {
  visible: boolean;
  onChangeVisible: (visible: boolean) => void;
}

const CustomColumns: React.FC<CustomColumnsProps> = ({ visible, onChangeVisible }) => (
  <Button variant='base' theme='default' icon={<SettingIcon />} onClick={() => onChangeVisible(!visible)}>
    列配置
  </Button>
);
export default CustomColumns;
