import React, { useEffect, useRef } from 'react';
import { Input, type InputValue } from 'tdesign-react';
import { useLocation } from 'react-router-dom';
import { SearchIcon } from 'tdesign-icons-react';
import classnames from 'classnames';
import { SelectTable } from './Select';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import BasicList from './components/SideList';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectResourceManageQuestionSlice, getList, clearPageState } from 'modules/resourceManage/question';
import { throttle } from 'lodash';

const TreeTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const pageState = useAppSelector(selectResourceManageQuestionSlice);
  const { broadsideList } = pageState;
  const location = useLocation();
  const prevLocationRef = useRef(location);

  useEffect(() => {
    dispatch(getList({ subjectId: location?.state?.subjectId }));

    return () => {
      dispatch(clearPageState());
    };
  }, []);

  useEffect(() => {
    // 监听路由变化
    if (prevLocationRef.current.pathname === '/resource/question') {
      dispatch(clearPageState());
    }

    // 更新上一个路由
    prevLocationRef.current = location;
  }, [location, dispatch]);

  const throttledDispatch = throttle((subjectName) => {
    dispatch(getList({ subjectName }));
  }, 1500);

  const onChange = (value: InputValue) => {
    throttledDispatch(value);
  };

  return (
    <div className={classnames(CommonStyle.pageWithColor, Style.content)}>
      <div className={Style.treeContent}>
        <Input className={Style.search} suffixIcon={<SearchIcon />} onChange={onChange} placeholder='请输入关键词' />
        <BasicList broadsideList={broadsideList} />
      </div>
      <div className={Style.tableContent}>
        <SelectTable />
      </div>
    </div>
  );
};

export default TreeTable;
