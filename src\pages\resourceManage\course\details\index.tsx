import React, { memo, useState } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import '../index.less';
import { Tabs, TabValue } from 'tdesign-react';
import BasicInfo from './BasicInfo';
import DirInfo from './DirInfo';
import { Back } from 'components';

const { TabPanel } = Tabs;

const AddCourse: React.FC = () => {
  const [tabValue, setTabValue] = useState<TabValue>(1);

  const handleChange = (val: TabValue) => {
    setTabValue(val);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Back path='/resource/course' header='资源管理-课程管理' />
      <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)} style={{ height: '100%' }}>
        <Tabs className='addEditTabsContainer' value={tabValue} onChange={handleChange}>
          <TabPanel label='基本信息' value={1}>
            {tabValue === 1 && <BasicInfo />}
          </TabPanel>
          <TabPanel label='目录信息' value={2}>
            {tabValue === 2 && <DirInfo />}
          </TabPanel>
        </Tabs>
      </div>
    </div>
  );
};

export default memo(AddCourse);
