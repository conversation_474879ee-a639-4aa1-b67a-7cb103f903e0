import { useAppDispatch, useAppSelector } from 'modules/store';

export const usePaging = (method, params, selectData) => {
  const { pageNum, pageSize, total } = useAppSelector(selectData);
  const dispatch = useAppDispatch();

  const pagination = {
    current: pageNum,
    pageSize,
    total,
    showJumper: true,
    onChange: (pageInfo) => {
      dispatch(
        method({
          pageSize: pageInfo.pageSize,
          pageNum: pageInfo.current,
          ...params,
        }),
      );
    },
    onCurrentChange(_, pageInfo) {
      dispatch(
        method({
          pageSize: pageInfo.pageSize,
          pageNum: pageInfo.current,
          ...params,
        }),
      );
    },
    onPageSizeChange(size) {
      dispatch(
        method({
          pageSize: size,
          pageNum: 1,
          ...params,
        }),
      );
    },
  };

  return pagination;
};
