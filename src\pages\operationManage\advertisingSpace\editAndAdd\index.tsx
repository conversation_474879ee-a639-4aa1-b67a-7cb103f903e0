import React, { memo, useRef, useEffect, useState } from 'react';
import { Form, Row, Col, Button, MessagePlugin, Loading, Radio, type RadioValue } from 'tdesign-react';
import classnames from 'classnames';
import { SubmitContext, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { addOrUpdateAdvert, selectAdvertisingSpace } from 'modules/operationManage/advertisingSpace';
import { useLocation, useNavigate } from 'react-router-dom';
import { Back, Upload, Editor, Input } from 'components';
import { MENU_CONFIG, EDITOR_STYLE } from '../consts';
import { removeTagsAndSpaces } from 'utils/tool';
import { asyncValidate } from 'hooks';

const { FormItem } = Form;

const backPath = '/operationManage/advertisingSpace';

export default memo(() => {
  const navigate = useNavigate();
  const { state } = useLocation();

  const INITIAL_DATA =
    state.type === 'add'
      ? {
        advertName: '',
        advertContent: '',
        advertType: 1,
        advertCover: '',
      }
      : { ...state.row };

  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>();
  const { formLoading } = useAppSelector(selectAdvertisingSpace);
  const [advertType, setAdvertType] = useState<RadioValue>(1);
  const [text, setText] = useState('');
  const [advertCoverUrl, setAdvertCoverUrl] = useState(
    state.row?.advertCoverUrl ? [{ url: state.row?.advertCoverUrl }] : [],
  );

  useEffect(() => {
    if (state.type === 'edit') {
      formRef.current?.setFieldsValue?.(state.row);
      setAdvertType(INITIAL_DATA.advertType);
    }
  }, [useLocation]);

  useEffect(() => {
    if (advertType === 1) {
      setText(INITIAL_DATA.advertContent);
    }
  }, [advertType]);

  const onSubmit = async (e: SubmitContext) => {
    const params = {
      advertId: state.type === 'add' ? undefined : state.row.id,
      advertContent: '',
      ...formRef.current?.getFieldsValue?.(true),
    };
    if (!removeTagsAndSpaces(params.advertContent)) {
      formRef.current?.setFieldsValue?.({
        advertContent: '',
      });
    }
    if (e.validateResult === true) {
      await dispatch(addOrUpdateAdvert(params));
      MessagePlugin.success(`${state.type === 'add' ? '新增' : '修改'}成功`);
      navigate(backPath);
    }
  };

  const onSuccess1 = ({
    file: {
      response: {
        data: { id },
      },
    },
  }: {
    file: { response: { data: { id: number } } };
  }) => {
    formRef.current?.setFieldsValue?.({ advertCover: id });
  };

  const formProps = {
    label: advertType === 1 ? '站内广告' : '站外广告URL',
    name: 'advertContent',
    initialData: INITIAL_DATA.advertContent,
    rules: [
      {
        required: true,
        message: `${advertType === 1 ? '站内广告' : '站外广告URL'}必填`,
        type: 'error' as const,
      },
      {
        validator: asyncValidate,
        message: `${advertType === 1 ? '站内广告' : '站外广告URL'}必填`,
        type: 'error',
        trigger: 'blur',
      },
    ],
  };

  const handleReset = () => {
    setText('');
    setAdvertCoverUrl([]);
  };

  const radioChange = (v: RadioValue) => {
    setAdvertType(v);
    formRef.current?.setFieldsValue({ advertContent: undefined });
  };

  const editorOnChange = (e: string) => {
    if (!removeTagsAndSpaces(e)) {
      setText(INITIAL_DATA.advertContent);
    } else {
      setText(e);
    }
  };

  return (
    <>
      <Back path={backPath} header={`${state.type === 'edit' ? '编辑' : '新增'}广告`}></Back>
      <div className={classnames(CommonStyle.pageWithColor)}>
        <div className={Style.formContainer}>
          <Loading loading={formLoading} showOverlay>
            <Form
              // resetType='empty'
              ref={formRef}
              onSubmit={onSubmit}
              onReset={handleReset}
              labelWidth={100}
              labelAlign='top'
            >
              <div className={Style.titleBox}></div>
              <Row gutter={[32, 44]}>
                <Col span={12}>
                  <Input
                    label='广告名称'
                    name='advertName'
                    initialData={INITIAL_DATA.advertName}
                    rules={[{ required: true, message: '广告名称必填', type: 'error' }]}
                    maxlength={20}
                  />
                </Col>
                <Col span={12}>
                  <FormItem
                    label='广告封面'
                    name='advertCover'
                    initialData={INITIAL_DATA.advertCover}
                    rules={[{ required: true, message: '广告封面必填', type: 'error' }]}
                  >
                    <Upload
                      params={{ businessType: 8 }}
                      theme='image'
                      tips='请上传 .jpg/.png 文件大小3M'
                      max={1}
                      draggable
                      files={advertCoverUrl}
                      maxFileSize={3}
                      accept='.jpg, .png'
                      success={onSuccess1}
                    />
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label='广告类型'
                    name='advertType'
                    initialData={INITIAL_DATA.advertType}
                    rules={[{ required: true, message: '广告类型必选', type: 'error' }]}
                  >
                    <Radio.Group onChange={radioChange}>
                      <Radio value={1}>站内广告</Radio>
                      <Radio value={2}>站外广告</Radio>
                    </Radio.Group>
                  </FormItem>
                </Col>
                <Col span={12} className={Style.dateCol}>
                  {advertType === 1 && (
                    <FormItem {...formProps}>
                      <Editor
                        uploadVideoConfig={{ allowedFileTypes: ['mp4'], maxFileSize: 30 }}
                        uploadImgConfig={{ allowedFileTypes: ['jpg'], maxFileSize: 2 }}
                        toolbar={MENU_CONFIG}
                        editorStyle={EDITOR_STYLE}
                        fixedToolbar='hide'
                        border
                        text={text}
                        onChange={editorOnChange}
                      ></Editor>
                    </FormItem>
                  )}
                  {advertType === 2 && <Input maxlength={100} {...formProps} placeholder='请输入站外广告URL' />}
                </Col>
                <Col span={12}>
                  <FormItem>
                    <Button type='submit' theme='primary'>
                      提交
                    </Button>
                    <Button type='reset' style={{ marginLeft: 12 }}>
                      重置
                    </Button>
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </Loading>
        </div>
      </div>
    </>
  );
});
