/* eslint-disable no-unused-expressions */
import React, { useState, memo, useEffect, useRef } from 'react';
import { Row, Col, Popconfirm, TableProps, Tag, Button, EnhancedTable } from 'tdesign-react';
import { ChevronDownIcon, ChevronRightIcon } from 'tdesign-icons-react';
import classnames from 'classnames';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectSystemDept, getSystemDeptList, clearPageState, delSystemDept } from 'modules/system/dept';
import CommonStyle from 'styles/common.module.less';
import { Search } from 'components';
import { useNavigate } from 'react-router-dom';
import { IRef } from 'components/Form';
import CustomColumns from 'components/CustomColumns';
import PermissionButton from 'components/PermissionButton';
import { strIsNull } from 'utils/tool';

export default memo(() => {
  const dispatch = useAppDispatch();
  const { loading, list, delLoading } = useAppSelector(selectSystemDept);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams = {};
  const navigate = useNavigate();
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = ['deptName', 'orderNum', 'status', 'createBy', 'createTime', 'op'];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['deptName', 'orderNum', 'status', 'updateBy', 'updateTime', 'createTime', 'createBy'];
  const [expandAll, setExpandAll] = useState(false);

  useEffect(() => {
    dispatch(getSystemDeptList(searchParams));

    return () => {
      dispatch(clearPageState());
    };
  }, []);

  const handleClickDelete = async (row: any) => {
    const data = await dispatch(delSystemDept(row.deptId));

    if (data) {
      dispatch(getSystemDeptList(searchParams));
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'deptName',
      title: '部门名称',
      ellipsis: true,
      align: 'left',
    },
    {
      colKey: 'orderNum',
      title: '排序',
      width: 60,
      ellipsis: true,
      align: 'left',
    },
    {
      colKey: 'status',
      title: '状态',
      width: 100,
      ellipsis: true,
      align: 'left',
      cell({ row }) {
        return (
          <Tag theme={row.status === '1' ? 'danger' : 'primary'} variant='light'>
            {row.status === '1' ? '停用' : '正常'}
          </Tag>
        );
      },
    },
    {
      colKey: 'updateTime',
      title: '修改时间',
      ellipsis: true,
      align: 'left',
    },
    {
      colKey: 'updateBy',
      title: '修改人',
      ellipsis: true,
      align: 'left',
    },
    {
      colKey: 'createTime',
      title: '创建时间',
      ellipsis: true,
      align: 'left',
    },
    {
      colKey: 'createBy',
      title: '创建人',
      ellipsis: true,
      align: 'left',
    },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      fixed: 'right',
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['system:dept:add']}
              onClick={() => navigate('/system/dept/add', { state: { deptId: row.deptId } })}
            >
              新增
            </PermissionButton>
            <PermissionButton
              permissions={['system:dept:edit']}
              onClick={() => navigate('/system/dept/edit', { state: { deptId: row.deptId } })}
            >
              编辑
            </PermissionButton>
            {strIsNull(row.children) && (
              <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleClickDelete(row)}>
                <>
                  <PermissionButton permissions={['system:dept:remove']} theme='danger' loading={delLoading}>
                    删除
                  </PermissionButton>
                </>
              </Popconfirm>
            )}
          </>
        );
      },
    },
  ];

  const renderTreeExpandAndFoldIcon = ({ type }: { type: string }) =>
    type === 'expand' ? <ChevronRightIcon /> : <ChevronDownIcon />;

  const onExpandAllToggle = () => {
    setExpandAll(!expandAll);
    !expandAll ? tableRef.current?.expandAll() : tableRef.current?.foldAll();
  };

  return (
    <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getSystemDeptList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '部门名称',
            field: 'deptName',
          },
          {
            type: 'select',
            label: '状态',
            field: 'status',
            options: [
              {
                label: '正常',
                value: '0',
              },
              {
                label: '停用',
                value: '1',
              },
            ],
          },
        ]}
      />
      <Row align='middle' justify='space-between'>
        <Row gutter={8} align='middle'>
          <Col>
            <PermissionButton
              permissions={['system:dept:add']}
              variant='outline'
              onClick={() => navigate('/system/dept/add')}
            >
              新增部门
            </PermissionButton>
          </Col>
          <Col>
            <Button variant='outline' theme='default' onClick={onExpandAllToggle}>
              展开/折叠
            </Button>
          </Col>
        </Row>
        <Row gutter={8} align='middle'>
          <Col>
            {
              <CustomColumns
                visible={columnControllerVisible}
                onChangeVisible={(val) => setColumnControllerVisible(val)}
              />
            }
          </Col>
        </Row>
      </Row>

      <EnhancedTable
        ref={tableRef}
        loading={loading}
        rowKey='deptId'
        columns={columns}
        data={list ?? []}
        displayColumns={displayColumns}
        onDisplayColumnsChange={setDisplayColumns}
        columnController={{
          dialogProps: { preventScrollThrough: true },
          hideTriggerButton: true,
          fields: optionalColumns,
        }}
        columnControllerVisible={columnControllerVisible}
        onColumnControllerVisibleChange={setColumnControllerVisible}
        tree={{ treeNodeColumnIndex: 0 }}
        treeExpandAndFoldIcon={renderTreeExpandAndFoldIcon}
      />
    </div>
  );
});
