import React, { memo, useEffect, useRef, useState } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back, Upload } from 'components';
import { Button, Form, FormInstanceFunctions, FormProps, Input, Loading, Space, Tag, Textarea } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectResourceManageLecturer, editLecturer, getLecturerInfo } from 'modules/resourceManage/lecturer';
import { IEditResourceLecturerBo } from 'types/resourceManage';
import { useLocation, useNavigate } from 'react-router-dom';
import { AddIcon } from 'tdesign-icons-react';
import { strIsNull } from 'utils/tool';

const { FormItem } = Form;

const DetailsLecturer: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = location.state || {};
  const dispatch = useAppDispatch();
  const { lecturerInfoLoading, lecturerInfo, addEditLecturerLoading } = useAppSelector(selectResourceManageLecturer);
  const formRef = useRef<FormInstanceFunctions>();
  const [form] = Form.useForm();
  const [fileUrl, setFileUrl] = useState('');
  const [inputVisible, toggleInputVisible] = useState(false);
  const [tagList, setTagList] = useState([]);

  const INITIAL_DATA = {
    teacherName: '',
    phoneNumber: '',
    technicalTitle: '',
    brief: '',
    teacherImgId: '',
  };

  const rules: FormProps['rules'] = {
    teacherName: [
      {
        required: true,
        message: '讲师姓名不能为空',
        trigger: 'all',
      },
    ],
    technicalTitle: [
      {
        required: true,
        message: '职称不能为空',
        trigger: 'all',
      },
    ],
    brief: [
      {
        required: true,
        message: '讲师简介不能为空',
        trigger: 'all',
      },
    ],
  };

  useEffect(() => {
    dispatch(getLecturerInfo(id));
  }, []);

  useEffect(() => {
    if (!strIsNull(lecturerInfo)) {
      formRef.current?.setFieldsValue?.(lecturerInfo);

      setFileUrl(lecturerInfo.teacherImgUrl);
      setTagList(
        lecturerInfo.technicalTitle.indexOf(',') > -1
          ? lecturerInfo.technicalTitle.split(',').map((item) => ({
            name: item,
          }))
          : [
            {
              name: lecturerInfo.technicalTitle,
            },
          ],
      );
    }
  }, [lecturerInfo]);

  useEffect(() => {
    formRef.current?.setFieldsValue?.({ technicalTitle: tagList.map(({ name }) => name).join(',') });
  }, [tagList]);

  const handleSubmit: FormProps['onSubmit'] = async ({ validateResult, fields }) => {
    if (validateResult === true) {
      const bo: IEditResourceLecturerBo = {
        ...fields,
        id,
      };

      await dispatch(editLecturer(bo));
    }
  };

  const handleReset: FormProps['onReset'] = () => {
    dispatch(getLecturerInfo(id));
  };

  const handleUploadImg = ({
    file: {
      response: {
        data: { id, fileUrl },
      },
    },
  }: {
    file: { response: { data: { id: number; fileUrl: string } } };
  }) => {
    formRef.current?.setFieldsValue?.({ teacherImgId: id });
    setFileUrl(fileUrl);
  };

  const handleCancel = () => {
    navigate('/resource/lecturer');
  };

  const deleteTag = (i: number) => {
    const newtagList = [...tagList];
    newtagList.splice(i, 1);
    setTagList(newtagList);
  };

  const handleClickAdd = () => {
    toggleInputVisible(true);
  };

  const handleInputEnter = (value: string) => {
    toggleInputVisible(false);
    if (value) setTagList((currentList) => currentList.concat([{ name: value, showClose: true }]));
  };

  return (
    <Loading
      loading={lecturerInfoLoading}
      showOverlay
      style={{ display: 'flex', flexDirection: 'column', height: '100%' }}
    >
      <Back path='/resource/lecturer' header='资源管理-讲师管理' />
      <div className={classnames(CommonStyle.pageWithColor)}>
        <Form
          disabled
          ref={formRef}
          form={form}
          initialData={INITIAL_DATA}
          resetType='initial'
          rules={rules}
          colon
          className={classnames(CommonStyle.commonSubjectForm)}
          onSubmit={handleSubmit}
          onReset={handleReset}
        >
          <FormItem label='讲师姓名' name='teacherName'>
            <Input placeholder='请输入讲师姓名' clearable />
          </FormItem>
          <FormItem label='联系电话' name='phoneNumber'>
            <Input placeholder='请输入联系电话' clearable />
          </FormItem>
          <FormItem label='职称' name='technicalTitle'>
            <Space>
              {tagList.map((tag, i) => (
                <Tag
                  shape='round'
                  size='medium'
                  key={i}
                  closable
                  onClose={() => {
                    deleteTag(i);
                  }}
                  icon={tag.icon}
                  disabled
                >
                  {tag.name}
                </Tag>
              ))}
            </Space>
          </FormItem>
          <FormItem label='讲师简介' name='brief'>
            <Textarea placeholder='请输入讲师简介' />
          </FormItem>
          <FormItem label='讲师照片' name='teacherImgId'>
            <Upload
              url='/oss/uploadFile'
              theme='image'
              tips='请上传 .jpg/.jpeg/.png 格式的图片 文件大小5M'
              accept='.jpg, .jpeg, .png'
              maxFileSize={5}
              max={1}
              draggable
              success={handleUploadImg}
              files={fileUrl ? [{ url: fileUrl }] : []}
              params={{
                businessType: 12,
              }}
              disabled
            />
          </FormItem>
          <FormItem className={classnames(CommonStyle.commonSubjectFormBtn)}>
            <Space>
              <Button theme='default' onClick={handleCancel}>
                返回
              </Button>
              <Button type='reset' theme='warning'>
                刷新
              </Button>
            </Space>
          </FormItem>
        </Form>
      </div>
    </Loading>
  );
};

export default memo(DetailsLecturer);
