import React, { memo, useRef, useState, useEffect } from 'react';
import {
  Form,
  Row,
  Col,
  Radio,
  Button,
  InputAdornment,
  Loading,
  Tag,
  Input as InputTD,
  Space,
  InputNumber,
} from 'tdesign-react';
import classnames from 'classnames';
import { SubmitContext, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import { AddIcon } from 'tdesign-icons-react';
import { Back, Upload, Input, Editor } from 'components';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { addSchoolInfo, selectSchool } from 'modules/directory/school';
import { MAJOR_TYPE, SCHOOL_TYPE } from 'enum';
import { strIsNull } from 'utils/tool';
import AddressMap from 'components/AddressMap';

const { FormItem } = Form;

interface Tag {
  name: string;
}

export default memo(() => {
  const addFormData = {
    educationalId: '',
    educationalName: '',
    educationalLogoImgId: undefined,
    educationalBackgroundImgId: undefined,
    educationalIntroduce: '',
    educationalDisciplines: '',
    educationalTitle: '',
    educationalType: '',
    educationalQrCodeImgId: '',
    educationalQrCodeImgUrl: '',
    educationalCrowdDesc: '',
    majorsType: '',
    maleRatio: '',
    femaleRatio: '',
    employmentRate: '',
    addressBoList: '',
    stuNum: '',
  };
  const navigate = useNavigate();
  const { state } = useLocation();
  const { row, type } = state || { row: {} };
  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>();
  const [inputVisible, toggleInputVisible] = useState(false);
  const [tagList, setTagList] = useState<Tag[]>([]);
  const [formMapData, setFormMapData] = useState<any>([]);
  const { formLoading } = useAppSelector(selectSchool);
  const mapRef1 = useRef<{ resetMap: () => void }>(null);
  const mapRef2 = useRef<{ resetMap: () => void }>(null);
  const mapRef3 = useRef<{ resetMap: () => void }>(null);
  const mapRef4 = useRef<{ resetMap: () => void }>(null);

  const {
    logoUrl,
    backgroundImgUrl,
    educationalQrCodeImgUrl,
    addressVoList,
    educationalTitle,
    logoId,
    backgroundImgId,
    ...rest
  } = row || {};

  const editFormData = {
    educationalBackgroundImgId: backgroundImgId,
    educationalLogoImgId: logoId,
    addressBoList: addressVoList,
    educationalTitle,
    ...rest,
  };

  const INITIAL_DATA = type === 'edit' ? editFormData : addFormData;

  useEffect(() => {
    if (type === 'edit') {
      setFormMapData(() => {
        if (addressVoList && Array.isArray(addressVoList) && addressVoList.length > 0) {
          const addressFormData = addressVoList.map((t, i) => {
            if (t.isDefaultAddress === 1) return { ...t, addressId: 1 };
            return { ...t, addressId: i + 1 };
          });
          return addressFormData;
        }
        return [];
      });
      const resultArray = educationalTitle && educationalTitle.split(/[,，]/);
      const tagsArray: Tag[] = resultArray.map((item: any) => ({ name: item.trim() }));
      setTagList(tagsArray as any);
    } else {
      setFormMapData([]);
    }
  }, []);

  useEffect(() => {
    if (!strIsNull(tagList)) {
      formRef.current?.setFieldsValue?.({
        educationalTitle: tagList.map((i) => i.name).join(','),
      });
    }
  }, [tagList]);

  const deleteTag = (i: number) => {
    const newTagList = [...tagList];
    newTagList.splice(i, 1);
    setTagList(newTagList);
  };

  const handleClickAdd = () => {
    toggleInputVisible(true);
  };

  const handleInputEnter = (value: string) => {
    toggleInputVisible(false);
    if (value) setTagList((currentList) => currentList.concat([{ name: value }]));
  };

  const formatData = async (i: any, l: any, addressId: number) => {
    const { formattedAddress, addressComponent } = i || {};

    const { name } = l;

    const infoItem = {
      addressId,
      province: addressComponent.province, // 省
      city: addressComponent.city, // 市
      area: addressComponent.district, // 县（区）
      country: addressComponent.township, // 乡（镇/街道）
      village: '', // 村
      addressDetails: formattedAddress, // 详细地址
      isDefaultAddress: addressId === 1 ? 1 : 0, // 是否默认地址
      addressName: name, // 地址名称
      longitude: l.location.lng, // 经度
      latitude: l.location.lat, // 维度
    };

    const copyInfoItem = { ...infoItem };
    setFormMapData((prevFormMapData: any) => {
      if (!prevFormMapData || prevFormMapData.length === 0) {
        formRef.current?.setFieldsValue?.({ addressBoList: [{ ...copyInfoItem }] });
        return [{ ...copyInfoItem }];
      }
      const updatedList = prevFormMapData.filter((item: any) => item.addressId !== addressId);
      updatedList.push({ ...infoItem });
      formRef.current?.setFieldsValue?.({ addressBoList: [...updatedList] });
      return updatedList;
    });
  };

  const handleAddressInfo1 = (info: any, l: any) => {
    formatData(info, l, 1);
  };
  const handleAddressInfo2 = (info: any, l: any) => {
    formatData(info, l, 2);
  };
  const handleAddressInfo3 = (info: any, l: any) => {
    formatData(info, l, 3);
  };
  const handleAddressInfo4 = (info: any, l: any) => {
    formatData(info, l, 4);
  };

  const onSuccess = (
    {
      file: {
        response: {
          data: { id },
        },
      },
    }: {
      file: { response: { data: { id: number } } };
    },
    field: string,
  ) => {
    formRef.current?.setFieldsValue?.({ [field]: id });
  };

  const onSubmit = async (e: SubmitContext) => {
    console.log(e.validateResult);

    if (e.validateResult === true) {
      const InfoData = formRef.current?.getFieldsValue?.(true);

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { addressBoList, ...rest } = { ...InfoData };

      await dispatch(
        addSchoolInfo({
          educationalId: type === 'edit' ? editFormData.educationalId : undefined,
          ...rest,
          addressBoList: formMapData,
        }),
      );
      navigate('/directory/school');
    }
  };

  const reset = () => {
    INITIAL_DATA.addressBoList = [];
    if (mapRef1.current) {
      mapRef1.current.resetMap();
    }
    if (mapRef2.current) {
      mapRef2.current.resetMap();
    }
    if (mapRef3.current) {
      mapRef3.current.resetMap();
    }
    if (mapRef4.current) {
      mapRef4.current.resetMap();
    }
  };
  return (
    <Loading loading={formLoading}>
      <Back path='/directory/school' header='院校目录'></Back>
      <div className={classnames(CommonStyle.pageWithColor)}>
        <div className={Style.formContainer}>
          <Form ref={formRef} onSubmit={onSubmit} onReset={reset} labelWidth={100} labelAlign='top'>
            <Row gutter={[42, 34]}>
              <Col span={6}>
                <Input
                  label='院校名称'
                  name='educationalName'
                  initialData={INITIAL_DATA.educationalName}
                  rules={[{ required: true, message: '院校名称必填', type: 'error' }]}
                  clearable
                />
              </Col>
              <Col span={6}>
                <FormItem
                  label='专业类型'
                  name='majorsType'
                  initialData={INITIAL_DATA.majorsType}
                  rules={[{ required: true }]}
                >
                  <Radio.Group>
                    {MAJOR_TYPE.map((t, index) => (
                      <Radio key={index} value={t.value}>
                        {t.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem
                  label='院校类型'
                  name='educationalType'
                  initialData={INITIAL_DATA.educationalType}
                  rules={[{ required: true }]}
                >
                  <Radio.Group>
                    {SCHOOL_TYPE.map((t, index) => (
                      <Radio key={index} value={t.value}>
                        {t.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem
                  label='本科/专科'
                  name='educationalDisciplines'
                  initialData={INITIAL_DATA.educationalDisciplines}
                  rules={[{ required: true }]}
                >
                  <Radio.Group>
                    <Radio value={1}>本科院校</Radio>
                    <Radio value={2}>专科院校</Radio>
                    <Radio value={3}>本科/专科院校</Radio>
                  </Radio.Group>
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem
                  label='院校标签'
                  name='educationalTitle'
                  initialData={INITIAL_DATA.educationalTitle}
                  rules={[{ required: true }]}
                >
                  <Space direction='vertical'>
                    <Space>
                      {tagList.map((tag: any, i) => (
                        <Tag key={i} closable onClose={() => deleteTag(i)}>
                          {tag.name}
                        </Tag>
                      ))}
                    </Space>
                    <div style={{ display: 'flex', cursor: 'pointer' }}>
                      {inputVisible ? (
                        <InputTD onBlur={handleInputEnter} onEnter={handleInputEnter} style={{ width: '94px' }} />
                      ) : (
                        <Tag onClick={handleClickAdd} icon={<AddIcon />}>
                          添加
                        </Tag>
                      )}
                    </div>
                  </Space>
                </FormItem>
              </Col>
              <Col span={6} className={Style.dateCol}>
                <FormItem
                  label='LOGO'
                  name='educationalLogoImgId'
                  initialData={INITIAL_DATA.educationalLogoImgId}
                  rules={[{ required: true }]}
                >
                  <Upload
                    params={{ businessType: 2 }}
                    theme='image'
                    tips='请上传 .jpg/.png 格式的图片 文件大小512kb'
                    accept='.jpg, .png'
                    maxFileSize={512}
                    fileUnit={'kb'}
                    max={1}
                    draggable
                    success={(valueFromChild: any) => onSuccess(valueFromChild, 'educationalLogoImgId')}
                    files={type === 'edit' && logoUrl ? [{ url: logoUrl }] : []}
                  />
                </FormItem>
              </Col>
              <Col span={6} className={Style.update}>
                <FormItem
                  label='详情背景'
                  name='educationalBackgroundImgId'
                  initialData={INITIAL_DATA.educationalBackgroundImgId}
                  rules={[{ required: true }]}
                >
                  <Upload
                    params={{ businessType: 3 }}
                    theme='image'
                    tips='请上传 .jpg/.png 格式的图片 文件大小512kb'
                    max={1}
                    draggable
                    maxFileSize={512}
                    fileUnit={'kb'}
                    accept='.jpg, .png'
                    success={(valueFromChild: any) => onSuccess(valueFromChild, 'educationalBackgroundImgId')}
                    files={type === 'edit' && backgroundImgUrl ? [{ url: backgroundImgUrl }] : []}
                  />
                </FormItem>
              </Col>
              <Col span={6}>
                <Input
                  label='交流群介绍'
                  name='educationalCrowdDesc'
                  initialData={INITIAL_DATA.educationalCrowdDesc}
                  clearable
                />
              </Col>
              <Col span={6} className={Style.qrCode}>
                <FormItem
                  label='交流群二维码'
                  name='educationalQrCodeImgId'
                  initialData={INITIAL_DATA.educationalQrCodeImgId}
                >
                  <Upload
                    params={{ businessType: 10 }}
                    theme='image'
                    tips='请上传 .jpg/.png 格式的图片 文件大小512kb'
                    max={1}
                    draggable
                    maxFileSize={512}
                    fileUnit={'kb'}
                    accept='.jpg, .png'
                    success={(valueFromChild: any) => onSuccess(valueFromChild, 'educationalQrCodeImgId')}
                    files={type === 'edit' && educationalQrCodeImgUrl ? [{ url: educationalQrCodeImgUrl }] : []}
                  />
                </FormItem>
              </Col>
              <Col span={12} className={Style.dateCol}>
                <FormItem
                  label='院校介绍'
                  name='educationalIntroduce'
                  initialData={INITIAL_DATA.educationalIntroduce}
                  rules={[{ required: true }]}
                >
                  <Editor
                    fixedToolbar='hide'
                    border
                    text={INITIAL_DATA.educationalIntroduce}
                    editorStyle={{ height: '600px', width: '100%' }}
                  ></Editor>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem
                  label='院校地址1（默认地址）'
                  name='addressBoList'
                  initialData={INITIAL_DATA.addressBoList[0]}
                  rules={[{ required: true }]}
                >
                  <div style={{ width: '100%', height: '350px', marginBottom: '20px' }}>
                    <AddressMap
                      initData={INITIAL_DATA.addressBoList[0]}
                      domId={'file1'}
                      tipinput={'tipinput1'}
                      onAddressInfo={handleAddressInfo1}
                      ref={mapRef1}
                    />
                  </div>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='院校地址2' name='addressBoList' initialData={INITIAL_DATA.addressBoList[1]}>
                  <div style={{ width: '100%', height: '350px', marginBottom: '20px' }}>
                    <AddressMap
                      initData={INITIAL_DATA.addressBoList[1]}
                      domId={'file2'}
                      tipinput={'tipinput2'}
                      onAddressInfo={handleAddressInfo2}
                      ref={mapRef2}
                    />
                  </div>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='院校地址3' name='addressBoList' initialData={INITIAL_DATA.addressBoList[2]}>
                  <div style={{ width: '100%', height: '350px', marginBottom: '20px' }}>
                    <AddressMap
                      initData={INITIAL_DATA.addressBoList[2]}
                      domId={'file3'}
                      tipinput={'tipinput3'}
                      onAddressInfo={handleAddressInfo3}
                      ref={mapRef3}
                    />
                  </div>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='院校地址4' name='addressBoList' initialData={INITIAL_DATA.addressBoList[3]}>
                  <div style={{ width: '100%', height: '350px', marginBottom: '20px' }}>
                    <AddressMap
                      initData={INITIAL_DATA.addressBoList[3]}
                      domId={'file4'}
                      tipinput={'tipinput4'}
                      onAddressInfo={handleAddressInfo4}
                      ref={mapRef4}
                    />
                  </div>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='男女比例' requiredMark>
                  <FormItem name='maleRatio' initialData={INITIAL_DATA.maleRatio} rules={[{ required: true }]}>
                    <InputAdornment prepend='男/女'>
                      <InputNumber
                        decimalPlaces={1}
                        allowInputOverLimit={false}
                        min={0}
                        theme='normal'
                        placeholder='输入男生比例'
                      />
                    </InputAdornment>
                  </FormItem>
                  <FormItem name='femaleRatio' initialData={INITIAL_DATA.femaleRatio} rules={[{ required: true }]}>
                    <InputAdornment prepend='女/男'>
                      <InputNumber
                        decimalPlaces={1}
                        allowInputOverLimit={false}
                        min={0}
                        theme='normal'
                        placeholder='输入女生比例'
                      />
                    </InputAdornment>
                  </FormItem>
                </FormItem>
              </Col>
              <Col span={6}>
                <Row justify='space-between'>
                  <Col span={6}>
                    <FormItem
                      label='就业率'
                      name='employmentRate'
                      initialData={INITIAL_DATA.employmentRate}
                      rules={[{ required: true }]}
                    >
                      <InputAdornment append='%'>
                        <InputNumber
                          decimalPlaces={1}
                          allowInputOverLimit={false}
                          max={100}
                          min={0}
                          theme='normal'
                          placeholder='输入就业率'
                        />
                      </InputAdornment>
                    </FormItem>
                  </Col>
                  <Col span={6}>
                    <FormItem
                      label='在校人数'
                      name='stuNum'
                      initialData={INITIAL_DATA.stuNum}
                      rules={[{ required: false }]}
                    >
                      <InputAdornment append='人'>
                        <InputNumber allowInputOverLimit={false} min={0} theme='normal' placeholder='输入在校人数' />
                      </InputAdornment>
                    </FormItem>
                  </Col>
                </Row>
              </Col>
            </Row>

            <FormItem>
              <Button type='submit' theme='primary'>
                提交
              </Button>
              <Button type='reset' style={{ marginLeft: 12 }}>
                重置
              </Button>
            </FormItem>
          </Form>
        </div>
      </div>
    </Loading>
  );
});
