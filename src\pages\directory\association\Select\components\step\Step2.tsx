import React, { useRef, useImperativeHandle, forwardRef, memo } from 'react';
import { Row, Form, Col, InputNumber } from 'tdesign-react';
import { FormInstanceFunctions } from 'tdesign-react/es/form/type';

const { FormItem } = Form;

interface FormInstance {
  schoolSystem?: string;
  tuition?: string;
}
interface Step2Props {
  ref: React.RefObject<any>;
  formData: FormInstance;
}

const Step2: React.FC<Step2Props> = forwardRef(({ formData }: Step2Props, ref) => {
  const formRef = useRef<FormInstanceFunctions>();
  const DATA = { schoolSystem: 1, tuition: 1 };
  const INITIAL_DATA = formData ?? DATA;

  useImperativeHandle(ref, () => ({
    validateForm: () =>
      formRef.current?.validate?.().then((valid) => {
        if (valid === true) return formRef.current?.getFieldsValue?.(true);
        return false;
      }),
  }));

  return (
    <div style={{ marginTop: '20px' }}>
      <Form ref={formRef} labelWidth={100} labelAlign='top'>
        <Row gutter={[42, 34]}>
          <Col span={12}>
            <FormItem
              label='学制'
              name='schoolSystem'
              initialData={INITIAL_DATA.schoolSystem}
              rules={[{ required: true, message: '学制必填', type: 'error' }]}
              tips='学制最小值为1，最大值为9'
            >
              <InputNumber
                style={{ width: '100%' }}
                allowInputOverLimit={false}
                max={9}
                min={1}
                theme='normal'
                placeholder='请输入学制'
              />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              label='学费'
              name='tuition'
              initialData={INITIAL_DATA.tuition}
              rules={[{ required: true, message: '学费必填', type: 'error' }]}
              tips='学制最小值为1，最大值为999999'
            >
              <InputNumber
                style={{ width: '100%' }}
                allowInputOverLimit={false}
                max={999999}
                min={1}
                theme='normal'
                placeholder='请输入学费'
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </div>
  );
});

export default memo(Step2);
