import { IAddDeptBo, IEditDeptBo, IGetDeptListBo } from 'types/system';
import request from 'utils/request/index';

/**
 * 查询部门列表
 * @returns
 */
export const getSystemDeptListApi = async (params: IGetDeptListBo) => {
  const result = await request.get({
    url: '/system/dept/list',
    params,
  });
  return result;
};

/**
 * 查询部门列表(排除指定部门)
 * @returns
 */
export const getSystemDeptExcludeListApi = async (deptId: number) => {
  const result = await request.get({
    url: `/system/dept/list/exclude/${deptId}`,
  });
  return result;
};

/**
 * 删除部门
 * @param deptId
 * @returns
 */
export const delSystemDeptApi = async (deptId: number) => {
  const result = await request.delete({
    url: `/system/dept/${deptId}`,
  });
  return result;
};

export const addSystemDeptApi = async (params: IAddDeptBo) => {
  const result = await request.post({
    url: `/system/dept`,
    params,
  });
  return result;
};

export const editSystemDeptApi = async (params: IEditDeptBo) => {
  const result = await request.put({
    url: `/system/dept`,
    params,
  });
  return result;
};

/**
 * 获取部门详细信息
 * @param deptId
 * @returns
 */
export const getSystemDeptDetailsInfoApi = async (deptId: number) => {
  const result = await request.get({
    url: `/system/dept/${deptId}`,
  });
  return result;
};
