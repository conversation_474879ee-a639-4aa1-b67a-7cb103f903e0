import { RootState } from '../store';
import {
  getMbtiListApi,
  getMbtiAdminExamListApi,
  getMbtiExamTypeDetailApi,
  openOrCloseMbtiExamApi,
  mbtiExamTypeRelatedMajorApi,
} from 'api';
import type {
  IGetMbtiListApi,
  IMbtiExamTypeDetailApi,
  IOpenOrCloseMbtiExamApi,
  IMbtiExamTypeRelatedMajorApi,
} from 'api';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';

const namespace = 'examMbti';

const initialState = {
  loading: true,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  active: undefined as number | undefined,
  activeName: undefined as string | undefined,
  broadsideList: [] as Array<any>,
  tablist: [] as Array<any>,
  objectList: {},
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);
//
export const getMbtiAdminExamList = useAsyncThunkWithStatus(`getMbtiAdminExamList`, async (_, { getState }) => {
  const {
    data: { rows },
  } = await getMbtiAdminExamListApi();
  const state = getState() as RootState;
  const currentActive = state.MbtiAdminExam.active;
  const currentActiveName = state.MbtiAdminExam.activeName;
  if (rows && rows.length > 0 && !currentActive)
    return {
      rows,
      active: rows[0].id,
      activeName: rows[0].examName
    };
  return { rows, active: currentActive, activeName: currentActiveName };
},
);

export const openOrCloseMbtiTop = useAsyncThunkWithStatus(`openOrCloseMbtiTop`, async (params, { dispatch }) => {
  await openOrCloseMbtiExamApi(params as IOpenOrCloseMbtiExamApi);
  await dispatch(getMbtiAdminExamList(''));
},
);
export const getMbtiExamTypeDetail = useAsyncThunkWithStatus(`getMbtiExamTypeDetail`, async (params) => {
  const { data } = await getMbtiExamTypeDetailApi(params as IMbtiExamTypeDetailApi);
  return {
    list: data ?? {}
  }
},
);
export const mbtiExamTypeRelatedMajor = useAsyncThunkWithStatus(`mbtiExamTypeRelatedMajor`, async (params, { dispatch }) => {
  await mbtiExamTypeRelatedMajorApi(params as IMbtiExamTypeRelatedMajorApi);
  // await dispatch(getMbtiAdminExamList(params as IGetMbtiListApi));
}
);
export const getMbtiList = useAsyncThunkWithStatus('getMbtiListApi', async (params) => {
  const {
    data: { rows, total },
  } = await getMbtiListApi(params as IGetMbtiListApi);

  return {
    list: rows ?? [],
    total,
    pageNum: params.pageNum,
    pageSize: params.pageSize,
  };
});



const directorySlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setActive: (state, action) => {
      state.active = action.payload;
    },
    setActiveName: (state, action) => {
      state.activeName = action.payload;
    },
  },
  cases: [
    {
      thunk: getMbtiAdminExamList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.broadsideList = action.payload.rows;
        state.active = action.payload.active;
        state.activeName = action.payload.activeName;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getMbtiList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.tablist = action.payload?.list;
        state.pageNum = action.payload?.pageNum;
        state.pageSize = action.payload?.pageSize;
        state.total = action.payload?.total;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getMbtiExamTypeDetail,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        action.payload.list.majorsVoAllList = action.payload?.list.majorsVoAllList.map((item) => ({ value: item.majorsId, label: item.majorsName }));
        state.objectList = action.payload?.list;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
  ],
});

export const { clearPageState, setActive, setActiveName } = directorySlice.actions;

export const selectMbtiAdminExam = (state: RootState) => state.MbtiAdminExam;

export default directorySlice.reducer;
