import React, { memo, useEffect, useRef, useState } from 'react';
import { DialogPlugin, Popconfirm, Row, Space, TableProps, Tag } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectSystemDict, getSystemDictList, delSystemDict, refreshSystemDictCache } from 'modules/system/dict';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IRef } from 'components/Form';
import { IGetDictListBo } from 'types/system';
import PermissionButton from 'components/PermissionButton';
import { Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { useNavigate, Link } from 'react-router-dom';
import { downLoad } from 'utils/tool';

const DictTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total, delLoading, refreCacheLoading } = useAppSelector(selectSystemDict);
  const [exportLoading, setExportLoading] = useState(false);
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetDictListBo = { pageNum, pageSize };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'row-select',
    'dictId',
    'dictName',
    'dictType',
    'status',
    'remark',
    'createBy',
    'createTime',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['remark', 'createTime', 'createBy', 'updateTime', 'updateBy'];

  /**
   * 删除字典
   */
  const handleDelete = async (dictId: number) => {
    const { type } = await dispatch(delSystemDict(dictId));

    if (type.endsWith('fulfilled')) {
      dispatch(getSystemDictList(searchParams));
    }
  };

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  /**
   * 批量删除字典
   */
  const handleBatchDelete = async () => {
    const confirmDia = DialogPlugin.confirm({
      header: '批量删除提示',
      body: '确定批量删除该数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        const { type } = await dispatch(delSystemDict(selectedRowKeys.join(',')));

        if (type.endsWith('fulfilled')) {
          handleResetSelection();
          confirmDia.hide();
          dispatch(getSystemDictList(searchParams));
        }
      },
      onClose: () => {
        confirmDia.hide();
      },
    });

    return false;
  };

  /**
   * 导出字典
   */
  const handleExport = async () => {
    try {
      setExportLoading(true);

      const params = searchRef.current?.getFormParams();

      await downLoad(
        '/system/dict/type/export',
        {
          ...params,
        },
        `字典_${new Date().getTime()}`,
      );

      setExportLoading(false);
    } catch (error) {
      setExportLoading(false);
    }
  };

  /**
   * 刷新缓存
   */
  const handleRefreshCache = () => {
    dispatch(refreshSystemDictCache());
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    {
      colKey: 'dictId',
      width: 120,
      title: '字典编号',
    },
    {
      colKey: 'dictName',
      title: '字典名称',
    },
    {
      colKey: 'dictType',
      title: '字典类型',
      cell({ row }) {
        return (
          <Link to={`/system/dict/data/${row.dictId}`} style={{ textDecoration: 'none', color: '#337ab7' }}>
            {row.dictType}
          </Link>
        );
      },
    },
    {
      colKey: 'status',
      title: '状态',
      cell({ row }) {
        return (
          <Tag theme={row.status === '1' ? 'danger' : 'primary'} variant='light'>
            {row.status === '1' ? '停用' : '正常'}
          </Tag>
        );
      },
    },
    {
      colKey: 'remark',
      title: '备注',
    },
    {
      colKey: 'updateTime',
      title: '修改时间',
    },
    {
      colKey: 'updateBy',
      title: '修改人',
    },
    {
      colKey: 'createTime',
      title: '创建时间',
    },
    {
      colKey: 'createBy',
      title: '创建人',
    },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['system:dict:edit']}
              onClick={() => navigate('/system/dict/type/edit', { state: { dictId: row.dictId } })}
            >
              修改
            </PermissionButton>
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleDelete(row.dictId)}>
              <>
                <PermissionButton permissions={['system:dict:remove']} theme='danger' loading={delLoading}>
                  删除
                </PermissionButton>
              </>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    dispatch(getSystemDictList(searchParams));
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getSystemDictList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '字典名称',
            field: 'dictName',
          },
          {
            type: 'input',
            label: '字典类型',
            field: 'dictType',
          },
          {
            type: 'select',
            label: '状态',
            field: 'status',
            options: [
              {
                label: '正常',
                value: '0',
              },
              {
                label: '停用',
                value: '1',
              },
            ],
          },
          {
            type: 'datePicker',
            label: '创建时间',
            field: 'dateArr',
            isTime: false,
          },
        ]}
      />

      <Row justify='space-between'>
        <Space>
          <PermissionButton
            permissions={['system:dict:add']}
            content='添加字典'
            variant='outline'
            onClick={() => navigate('/system/dict/type/add')}
          />
          <PermissionButton
            disabled={selectedRowKeys.length === 0}
            permissions={['system:dict:remove']}
            content='批量删除'
            variant='outline'
            theme='danger'
            onClick={() => handleBatchDelete()}
          />
          <PermissionButton
            loading={exportLoading}
            permissions={['system:dict:export']}
            content='导出'
            variant='outline'
            theme='warning'
            onClick={() => handleExport()}
          />
          <PermissionButton
            loading={refreCacheLoading}
            permissions={['system:dict:remove']}
            content='刷新缓存'
            variant='outline'
            theme='warning'
            onClick={() => handleRefreshCache()}
          />
        </Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'dictId',
          selectedRowKeys,
          onSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getSystemDictList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(DictTable);
