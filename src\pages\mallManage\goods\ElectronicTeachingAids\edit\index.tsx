/* eslint-disable no-nested-ternary */
import React, { useEffect, useRef } from 'react';
import {
  selectMallManageGoods,
  clearPageState,
  getGoodsInfo,
  getSubjectList,
  getResourceList,
  editGoods,
} from 'modules/mallManage/goods';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { Button, Form, InputNumber, Loading, Radio, Space, Switch } from 'tdesign-react';
import type { FormInstanceFunctions, FormProps, RadioValue } from 'tdesign-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back } from 'components';
import { strIsNull } from 'utils/tool';
import { IEditGoodsBo, IGetGoodsInfoBo, IGetResourceListBo } from 'types/mallManage';
import { Icon } from 'tdesign-icons-react';
import { useLocation, useNavigate } from 'react-router-dom';

const { FormItem } = Form;
const { Group } = Radio;

const EditElectronicTeachingAidsGoods: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { goodsId } = location.state || {};
  const dispatch = useAppDispatch();
  const { goodsInfo, goodsInfoLoading, subjectList, resourceLoading, resourceList, addEditGoodsLoading } =
    useAppSelector(selectMallManageGoods);
  const formRef = useRef<FormInstanceFunctions>();
  const [form] = Form.useForm();
  const allowExchange = Form.useWatch('allowExchange', form);

  const INITIAL_DATA = {
    type: 2,
    price: '',
    buyNum: 0,
    subjectId: -1,
    recourseIdList: '',
    allowExchange: 0,
  };

  const rules: FormProps['rules'] = {
    price: [
      {
        required: true,
        message: '商品价格不能为空',
        trigger: 'all',
      },
    ],
    buyNum: [
      {
        required: true,
        message: '购买人数不能为空',
        trigger: 'all',
      },
    ],
    subjectId: [
      {
        required: true,
        message: '关联科目不能为空',
        trigger: 'all',
      },
    ],
    recourseIdList: [
      {
        required: true,
        message: '关联教辅不能为空',
        trigger: 'all',
      },
    ],
    allowExchange: [
      {
        required: true,
        message: '支持兑换不能为空',
        trigger: 'all',
      },
    ],
    pointsRequired: [
      {
        required: true,
        message: '兑换积分不能为空',
        trigger: 'all',
      },
    ],
  };

  const renderActiveContent = () => <Icon name='check' />;
  const renderInactiveContent = () => <Icon name='close' />;

  const handleSubmit: FormProps['onSubmit'] = ({ validateResult, fields }) => {
    if (validateResult === true) {
      const bo: IEditGoodsBo = {
        ...fields,
        type: INITIAL_DATA.type,
        recourseIdList: [fields.recourseIdList],
        goodsId,
      };

      dispatch(editGoods(bo));
    }
  };

  const handleReset: FormProps['onReset'] = () => {
    const bo: IGetGoodsInfoBo = {
      goodsId,
    };

    dispatch(getGoodsInfo(bo));
  };

  const handleCancel = () => {
    navigate('/mall/goods', {
      state: {
        type: INITIAL_DATA.type,
      },
    });
  };

  const handleSubjectIdChange = (subjectId: RadioValue) => {
    formRef.current?.setFieldsValue({ recourseIdList: '' });

    const bo: IGetResourceListBo = {
      type: INITIAL_DATA.type as 1 | 2,
      subjectId,
    };

    dispatch(getResourceList(bo));
  };

  const handlePreviewClick = (url: string) => {
    navigate('/mall/goods/preview', {
      state: {
        params: {
          path: '/mall/goods/edit/2',
          header: '编辑商品-电子教辅',
          params: {
            goodsId
          },
          url,
        },
      },
    });
  };

  useEffect(() => {
    const bo: IGetGoodsInfoBo = {
      goodsId,
    };

    dispatch(getGoodsInfo(bo));

    dispatch(getSubjectList());

    return () => {
      dispatch(clearPageState());
    };
  }, []);

  useEffect(() => {
    if (!strIsNull(goodsInfo)) {
      const info = JSON.parse(JSON.stringify(goodsInfo));

      if (Array.isArray(info.goodsResourceList) && info.goodsResourceList.length > 0) {
        info.goodsResourceList = info.goodsResourceList[0].resourceId;
      }

      info.recourseIdList = info.goodsResourceList;

      formRef.current?.setFieldsValue(info);

      const bo: IGetResourceListBo = {
        type: INITIAL_DATA.type as 1 | 2,
        subjectId: info.subjectId,
      };

      dispatch(getResourceList(bo));
    }
  }, [goodsInfo]);

  return (
    <Loading
      loading={goodsInfoLoading}
      showOverlay
      style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      <Back path='/mall/goods' header='商品管理-电子教辅' params={{ type: INITIAL_DATA.type }} />

      <div className={classnames(CommonStyle.pageWithColor)}>
        <Form
          ref={formRef}
          form={form}
          initialData={INITIAL_DATA}
          resetType='initial'
          rules={rules}
          colon
          className={classnames(CommonStyle.commonSubjectForm)}
          onSubmit={handleSubmit}
          onReset={handleReset}
        >
          <FormItem label='商品价格' name='price'>
            <InputNumber
              placeholder='请输入商品价格'
              theme='normal'
              min={0}
              decimalPlaces={2}
              allowInputOverLimit={false}
              suffix={<span>元</span>}
            />
          </FormItem>
          <FormItem label='购买人数' name='buyNum'>
            <InputNumber
              placeholder='请输入购买人数'
              theme='normal'
              min={0}
              decimalPlaces={0}
              allowInputOverLimit={false}
              suffix={<span>人</span>}
            />
          </FormItem>
          <FormItem label='关联科目' name='subjectId'>
            <Group onChange={handleSubjectIdChange}>
              {subjectList.map(({ subjectId, subjectName }) => (
                <Radio key={subjectId} value={subjectId} label={subjectName} />
              ))}
            </Group>
          </FormItem>
          <FormItem label='关联教辅' name='recourseIdList'>
            {resourceLoading ? (
              <Loading size='small' />
            ) : (
              <Group>
                {resourceList.map(({ resourceId, resourceName, url }) => (
                  <Radio
                    key={resourceId}
                    value={resourceId}
                    label={
                      <Space align='center'>
                        <span>{resourceName}</span>
                        <Button theme='primary' size='small' variant='text' onClick={() => handlePreviewClick(url)}>
                          预览
                        </Button>
                      </Space>
                    }
                  />
                ))}
              </Group>
            )}
          </FormItem>
          <FormItem label='支持兑换' name='allowExchange'>
            <Switch customValue={[1, 0]} label={[renderActiveContent(), renderInactiveContent()]} />
          </FormItem>
          {allowExchange === 1 && (
            <FormItem label='兑换积分' name='pointsRequired'>
              <InputNumber
                placeholder='请输入兑换积分'
                theme='normal'
                min={1}
                decimalPlaces={0}
                allowInputOverLimit={false}
                suffix={<span>分</span>}
              />
            </FormItem>
          )}
          <FormItem className={classnames(CommonStyle.commonSubjectFormBtn)}>
            <Space>
              <Button loading={addEditGoodsLoading} type='submit' theme='primary'>
                提交
              </Button>
              <Button theme='default' onClick={handleCancel}>
                取消
              </Button>
              <Button type='reset' theme='warning'>
                重置
              </Button>
            </Space>
          </FormItem>
        </Form>
      </div>
    </Loading>
  );
};

export default EditElectronicTeachingAidsGoods;
