import React, { memo, useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { MessagePlugin } from 'tdesign-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back } from 'components';
import VideoForm from '../components/VideoForm';
import { useAppSelector } from 'modules/store';
import { selectQuesCodeState } from 'modules/resourceManage/questionCodeRedux';

const EditQuestionCode: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = location.state || {};
  const { loading, error } = useAppSelector(selectQuesCodeState);
  const [lastLoadingState, setLastLoadingState] = useState(false);
  useEffect(() => {
    if (lastLoadingState && !loading) {
      if (!error) {
        MessagePlugin.success('二维码信息更新成功！');
        setTimeout(() => {
          navigate('/resource/questionCode');
        }, 1500);
      } else {
        // Error
        MessagePlugin.error(error || '更新失败，请重试');
      }
    }
    setLastLoadingState(loading);
  }, [loading, error, lastLoadingState, navigate]);

  return (
    <>
      <Back path='/resource/questionCode' header='资源管理-修改试题二维码' />
      <div className={classnames(CommonStyle.pageWithColor)}>
        <VideoForm id={id} onChangeVideo={() => {}} />
      </div>
    </>
  );
};

export default memo(EditQuestionCode);
