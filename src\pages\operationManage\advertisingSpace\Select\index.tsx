import React, { memo, useEffect } from 'react';
import { Button, Loading, Pagination, Space, MessagePlugin } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import {
  selectAdvertisingSpace,
  getAdvertList,
  clearPageState,
  deleteAdvert,
  openOrCloseAdvert,
} from 'modules/operationManage/advertisingSpace';
import Style from './index.module.less';
import { PageInfo } from 'tdesign-react/es/pagination/type';
import { CardImage, Empty } from 'components';
import { useNavigate } from 'react-router-dom';
import { formatDate } from 'utils/tool';

export const SelectTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { pageSize, total, list, loading } = useAppSelector(selectAdvertisingSpace);

  const pageInit = () => {
    dispatch(
      getAdvertList({
        pageSize,
        pageNum: 1,
      }),
    );
  };

  useEffect(() => {
    pageInit();
    return () => {
      clearPageState();
    };
  }, []);

  const onChange = async ({ current, pageSize }: PageInfo) => {
    await dispatch(
      getAdvertList({
        pageSize,
        pageNum: current,
      }),
    );
  };

  const handlePageSizeChange = async (pageSize: number, { current }: PageInfo) => {
    await dispatch(
      getAdvertList({
        pageSize,
        pageNum: current,
      }),
    );
  };

  return (
    <>
      <div style={{ margin: '0 auto' }}>
        <Loading loading={loading}>
          <h3>首页-banner管理</h3>
          <div>
            <Button onClick={() => navigate('/operationManage/advertisingSpace/add', { state: { type: 'add' } })}>
              新增广告
            </Button>
            <span style={{ marginLeft: '10px' }}>请至少添加1个推广内容，最多支持开启4个推广内容</span>
          </div>
          <Space style={{ marginTop: '20px' }} breakLine direction='horizontal' size={[30, 32]} align='center'>
            {list.map((item: any) =>
              CardImage({
                key: item.id,
                status: item.isOpen === 1,
                cover: item.advertCoverUrl,
                title: item.advertName,
                content: [item.advertType === 1 ? '站内推广' : '站外推广', formatDate(item.updateTime)],
                width: '380px',
                imgHeight: '200px',
                imgWidth: '378px',
                deleteTip: '是否确认删除此广告？',
                onDelete: () => dispatch(deleteAdvert(item.id)),
                onEdit: () =>
                  navigate('/operationManage/advertisingSpace/edit', { state: { row: item, type: 'edit' } }),
                onChange: async (val: boolean) => {
                  await dispatch(
                    openOrCloseAdvert({
                      advertId: item.id,
                      isOpen: val ? 1 : 0,
                    }),
                  );
                  MessagePlugin.success(`${item.isOpen === 1 ? '关闭' : '开启'}成功！`);
                },
              }),
            )}
          </Space>
          {list && list.length === 0 && <Empty />}
          <Pagination
            className={Style.pagination}
            total={total}
            pageSizeOptions={[10, 20, 50, 100]}
            pageSize={pageSize}
            onChange={onChange}
            onPageSizeChange={handlePageSizeChange}
          />
        </Loading>
      </div>
    </>
  );
};

export default memo(SelectTable);
