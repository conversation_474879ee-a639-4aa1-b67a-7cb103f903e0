import request from 'utils/request/index';
/**
 * 院校目录
 */

export interface IGetSchoolListApi {
  pageNum: number;
  pageSize: number;
  educationalName?: string;
}

export interface IAddSchoolAssociaExamInfoApi {
  educationalId: number;
  examId: number;
  relation: 0 | 1;
}

export interface IAddressBoList {
  province: string;
  country: string | null;
  village: string | null;
  city: string;
  area: string;
  addressDetails: string;
  isDefaultAddress: 0 | 1;
  addressName: string;
  longitude: string;
  latitude: string;
}

export interface IAddSchoolInfoApi {
  educationalId: number;
  educationalName: string;
  educationalLogoImgId: number;
  educationalBackgroundImgId: number;
  educationalIntroduce: string;
  educationalDisciplines: 1 | 2 | 3;
  educationalTitle: string;
  educationalType: 1 | 2;
  majorsType: 1 | 2 | 3 | 4 | 5 | 6;
  maleRatio: number;
  femaleRatio: number;
  employmentRate: number;
  addressBoList: IAddressBoList[];
}

export interface IAddReportDataApi {
  educationalReportedDataId: number;
  year: number;
  applyingNum: number;
  admissionsNum: number;
  fractionBar: number;
  educationalExamId: number;
}

export interface IDelSchoolInfoApi {
  educationalIdList: number[];
  educationalName?: string;
  // educationalId?: number;
}

export interface IOnOffAppExamInfoApi {
  educationalId: number;
  examId: number;
  isOpen: 0 | 1;
}

export interface ISchoolListSortApi {
  id: number;
  position: number;
}

/**
 * 分页条件查询院校列表
 * @param data
 * @returns
 */
export const getSchoolListApi = async (params: IGetSchoolListApi) => {
  const result = await request.get({
    url: '/educationalInstitutions/getEducationalInstitutionsList',
    params,
  });
  return result;
};

/**
 * 查询院校关联下考试的普高生报录数据列表
 * @param id
 * @returns
 */
export const getSchoolExamListApi = async (id: string | number) => {
  const result = await request.get({
    url: `/educationalInstitutions/getEducationalReportedDataList/${id}`,
  });
  return result;
};
/**
 * 查询院校关联下考试的三校生报录数据列表
 * @param id
 * @returns
 */
export const getEducationalReportedListApi = async (id: string | number) => {
  const result = await request.get({
    url: `/educationalInstitutions/getEducationalReportedSpecialDataList/${id}`,
  });
  return result;
};
/**
 * 查询院校关联的考试信息
 * @param id
 * @returns
 */
export const getSchoolExamInfoApi = async (id: string | number) => {
  const result = await request.get({
    url: `/educationalInstitutions/getEducationalInstitutionsExamList/${id}`,
  });
  return result;
};

/**
 * 院校关联/取消关联考试信息
 * @param params
 * @returns
 */
export const addSchoolAssociaExamInfoApi = async (params: IAddSchoolAssociaExamInfoApi) => {
  const result = await request.post({
    url: `/educationalInstitutions/addOrDeleteEducationalInstitutionsExam`,
    params,
  });
  return result;
};

/**
 *  新增/修改院校信息
 * @param params
 * @returns
 */
export const addSchoolInfoApi = async (params: IAddSchoolInfoApi) => {
  const result = await request.post({
    url: `/educationalInstitutions/addOrUpdateAdminEducationalInstitutions`,
    params,
  });
  return result;
};

/**
 *  新增/修改 普高生报录数据
 * @param params
 * @returns
 */
export const addReportDataApi = async (params: IAddReportDataApi) => {
  const result = await request.post({
    url: `/educationalInstitutions/addOrUpdateEducationalReportedData`,
    params,
  });
  return result;
};
/**
 *  新增/修改 三校生报录数据
 * @param params
 * @returns
 */
export const addReportSpecialDataApi = async (params: IAddReportDataApi) => {
  const result = await request.post({
    url: `/educationalInstitutions/addOrUpdateEducationalReportedSpecialData`,
    params,
  });
  return result;
};

/**
 *  删除/批量删除院校信息
 * @param params
 * @returns
 */
export const delSchoolInfoApi = async (params: IDelSchoolInfoApi) => {
  const result = await request.delete({
    url: `/educationalInstitutions/deleteEducationalInstitutions`,
    params,
  });
  return result;
};

/**
 * 根据报录数据 id 删除 普高生报录数据
 * @param id
 * @returns
 */
export const delReportDataApi = async (id: string | number) => {
  const result = await request.delete({
    url: `/educationalInstitutions/deleteEducationalReportedData/${id}`,
  });
  return result;
};
/**
 * 根据报录数据 id 删除 三校生报录数据
 * @param id
 * @returns
 */
export const delReportSpecialDataApi = async (id: string | number) => {
  const result = await request.delete({
    url: `/educationalInstitutions/deleteEducationalReportedSpecialData/${id}`,
  });
  return result;
};

/**
 * 开启/关闭( 小程序端回显院校关联的考试信息 )
 * @param id
 * @returns
 */
export const onOffAppExamInfoApi = async (params: IOnOffAppExamInfoApi) => {
  const result = await request.put({
    url: `/educationalInstitutions/updateEducationalInstitutionsExam`,
    params,
  });
  return result;
};

/**
 * 院校列表拖动排序
 * @param id
 * @returns
 */
export const schoolListSortApi = async (params: ISchoolListSortApi) => {
  const result = await request.put({
    url: `/educationalInstitutions/educationalDragSort`,
    params,
  });
  return result;
};

/**
 * 根据院校id获取院校详情
 * @param params
 * @returns
 */
export const getEducationalInfoApi = (id: number) =>
  request.get({
    url: `/educationalInstitutions/getEducationalDetailById/${id}`,
  });
