import { PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getEducationalInstitutionsNameAndIdApi,
  getEducationAlMajorsExamListApi,
  getEducationalMajorsListApi,
  getUnrelatedMajorsListApi,
  addEducationalMajorsApi,
  updateEducationalMajorsApi,
  addOrDeleteEducationalMajorsExamApi,
  deleteEducationalMajorsApi,
  educationalMajorsDragSortApi,
} from 'api';
import type {
  ExamAllListType,
  IGetEducationalMajorsListApi,
  IGetUnrelatedMajorsListApi,
  IAddEducationalMajorsApi,
  IEditEducationalMajorsApi,
  IAddOrDeleteEducationalMajorsExamApi,
  IDeleteEducationalMajorsApi,
  IEducationalMajorsDragSortApi,
} from 'api';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'association';

interface IQuestionAnalysis {
  oid: string;
  opt: string;
  optionContent: string;
}

interface IEducationalInstitutionBo {
  educationalName?: string | null | undefined;
  educationalId?: number | null | undefined;
}

const initialState = {
  loading: true,
  formLoading: false,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  list: [] as Array<any>,
  contractList: [] as ExamAllListType[],
  active: undefined as number | undefined,
  activeName: undefined as string | undefined,
  broadsideList: [] as Array<any>,
  tablist: [] as Array<any>,
  answer: '',
  answerId: '' as number | string,
  questionId: '',
  subjectId: 0,
  stem: '',
  optionBoList: [] as IQuestionAnalysis[],
  questionAnalysis: '',
  questionProvenance: '',
  step1: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: false,
  },
  step3: {
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
    loading: false,
  },
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getEducationalInstitutionsNameAndId = useAsyncThunkWithStatus(
  `getEducationalInstitutionsNameAndId`,
  async (educationalInstitutionBo: IEducationalInstitutionBo, { getState }) => {
    const {
      data: { rows },
    } = await getEducationalInstitutionsNameAndIdApi(educationalInstitutionBo.educationalName);

    const state = getState() as RootState;
    const currentActive = state.association.active;

    const obj: any = {
      rows,
    };

    if (educationalInstitutionBo.educationalId) {
      obj.active = educationalInstitutionBo.educationalId;
      obj.activeName = educationalInstitutionBo.educationalName;
    } else if (!currentActive) {
      obj.active = rows[0].educationalId;
      obj.activeName = rows[0].educationalName;
    }

    return obj;
  },
);

export const getEducationAlMajorsExamList = useAsyncThunkWithStatus(
  `getEducationAlMajorsExamList`,
  async (id?: unknown) => {
    const {
      data: { rows, total },
    } = await getEducationAlMajorsExamListApi(id as number);

    return {
      list: rows ?? [],
      total,
    };
  },
);

export const getEducationalMajorsList = useAsyncThunkWithStatus(
  'getEducationalMajorsList',
  async (params: IGetEducationalMajorsListApi) => {
    if (params.educationalId) {
      const {
        data: { rows, total },
      } = await getEducationalMajorsListApi(params);

      return {
        list: rows ?? [],
        total,
        pageNum: params.pageNum,
        pageSize: params.pageSize,
      };
    }
    return {
      list: [],
      total: 0,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
    };

    return false;
  },
);

export const getUnrelatedMajorsList = useAsyncThunkWithStatus('getUnrelatedMajorsList', async (params) => {
  const {
    data: { rows, total },
  } = await getUnrelatedMajorsListApi(params as IGetUnrelatedMajorsListApi);
  const { pageNum, pageSize } = params;
  return {
    list: rows ?? [],
    total,
    pageNum,
    pageSize,
  };
});

export const addEducationalMajors = useAsyncThunkWithStatus(
  'addEducationalMajors',
  async (params, { getState, dispatch }) => {
    const { type, ...rest } = params;
    const state = getState() as RootState;
    const { pageNum, pageSize, active } = state.association;
    if (type === 'edit') {
      await updateEducationalMajorsApi({ ...rest } as IEditEducationalMajorsApi);
    } else if (type === 'add') {
      await addEducationalMajorsApi({ ...rest } as IAddEducationalMajorsApi);
    }
    await MessagePlugin.success('提交成功');
    await dispatch(
      getEducationalMajorsList({
        pageNum,
        pageSize,
        educationalId: active,
      }),
    );
  },
);

export const addOrDeleteEducationalMajorsExam = useAsyncThunkWithStatus(
  'addOrDeleteEducationalMajorsExam',
  async (params, { dispatch }) => {
    await addOrDeleteEducationalMajorsExamApi(params as IAddOrDeleteEducationalMajorsExamApi);
    await MessagePlugin.success(`${params.isRelated === 0 ? '取消关联' : '关联'}成功`);
    await dispatch(getEducationAlMajorsExamList(params.educationalMajorsId));
  },
);

export const deleteEducationalMajors = useAsyncThunkWithStatus(
  'deleteEducationalMajors',
  async (params, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { pageNum, pageSize, active } = state.association;
    await deleteEducationalMajorsApi(params as IDeleteEducationalMajorsApi);
    await MessagePlugin.success(`取消关联成功`);
    await dispatch(
      getEducationalMajorsList({
        pageNum,
        pageSize,
        educationalId: active,
      }),
    );
  },
);

export const educationalMajorsDragSort = useAsyncThunkWithStatus(
  'educationalMajorsDragSort',
  async (params, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { pageNum, pageSize, active } = state.association;
    await educationalMajorsDragSortApi(params as IEducationalMajorsDragSortApi);
    await dispatch(
      getEducationalMajorsList({
        pageNum,
        pageSize,
        educationalId: active,
      }),
    );
  },
);

const directorySlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setActive: (state, action: PayloadAction<number>) => {
      state.active = action.payload;
    },
    setActiveName: (state, action: PayloadAction<string>) => {
      state.activeName = action.payload;
    },
    setStem: (state, action: PayloadAction<string>) => {
      state.stem = action.payload;
    },
    setOption: (state, action: PayloadAction<IQuestionAnalysis[]>) => {
      state.optionBoList = action.payload;
    },
    setQuestionAnalysis: (state, action: PayloadAction<string>) => {
      state.questionAnalysis = action.payload;
    },
    setQuestionProvenance: (state, action: PayloadAction<string>) => {
      state.questionProvenance = action.payload;
    },
    setAnswer: (state, action: PayloadAction<{ oid: string; opt: string }>) => {
      state.answer = action.payload.opt;
      state.answerId = action.payload.oid;
    },
  },
  cases: [
    {
      thunk: getEducationalInstitutionsNameAndId,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.broadsideList = action.payload.rows;

        if (action.payload?.active) {
          state.active = action.payload?.active;
          state.activeName = action.payload.activeName;
        }
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getEducationalMajorsList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.loading = false;
        state.tablist = action.payload?.list;
        state.total = action.payload?.total;
        state.pageNum = action.payload?.pageNum;
        state.pageSize = action.payload?.pageSize;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getUnrelatedMajorsList,
      pending: (state) => {
        state.step1.loading = true;
      },
      fulfilled: (state, action) => {
        state.step1.loading = false;
        state.step1.list = action.payload?.list;
        state.step1.total = action.payload?.total;
        state.step1.pageNum = action.payload?.pageNum;
        state.step1.pageSize = action.payload?.pageSize;
      },
      rejected: (state) => {
        state.step1.loading = false;
      },
    },

    {
      thunk: getEducationAlMajorsExamList,
      pending: (state) => {
        state.step3.loading = true;
      },
      fulfilled: (state, action) => {
        state.step3.loading = false;
        state.step3.list = action.payload?.list;
        state.step3.total = action.payload?.total;
      },
      rejected: (state) => {
        state.step3.loading = false;
      },
    },
  ],
});

export const {
  clearPageState,
  setActive,
  setActiveName,
  setAnswer,
  setStem,
  setOption,
  setQuestionAnalysis,
  setQuestionProvenance,
} = directorySlice.actions;

export const selectAssociation = (state: RootState) => state.association;

export default directorySlice.reducer;
