import React, { memo, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { MessagePlugin } from 'tdesign-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back } from 'components';
import VideoForm from '../components/VideoForm';
import { useAppSelector } from 'modules/store';
import { selectQuesCodeState } from 'modules/resourceManage/questionCodeRedux';

const AddQuestionCode: React.FC = () => {
  const navigate = useNavigate();
  const { loading, error } = useAppSelector(selectQuesCodeState);
  const [lastLoadingState, setLastLoadingState] = useState(false);

  // Monitor save operation result
  useEffect(() => {
    // If loading changed from true to false, it means an operation completed
    if (lastLoadingState && !loading) {
      if (!error) {
        // Success
        MessagePlugin.success('信息保存成功！');
        // Navigate back to list page after a short delay
        setTimeout(() => {
          navigate('/resource/questionCode');
        }, 1500);
      } else {
        // Error
        MessagePlugin.error(error || '保存失败，请重试');
      }
    }
    setLastLoadingState(loading);
  }, [loading, error, lastLoadingState, navigate]);

  return (
    <>
      <Back path='/resource/questionCode' header='资源管理-试题管理' />
      <div className={classnames(CommonStyle.pageWithColor)}>
        <VideoForm id={0} onChangeVideo={() => {}} />
      </div>
    </>
  );
};

export default memo(AddQuestionCode);
