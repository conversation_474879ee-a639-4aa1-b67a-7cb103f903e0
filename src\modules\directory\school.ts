import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { MessagePlugin } from 'tdesign-react';

import {
  getSchoolListApi,
  getSchoolExamInfoApi,
  onOffAppExamInfoApi,
  addSchoolAssociaExamInfoApi,
  getSchoolExamListApi,
  getEducationalReportedListApi,
  addReportDataApi,
  addReportSpecialDataApi,
  delReportDataApi,
  delReportSpecialDataApi,
  delSchoolInfoApi,
  addSchoolInfoApi,
  schoolListSortApi,
  getEducationalInfoApi,
} from 'api';

import type {
  IGetSchoolListApi,
  IOnOffAppExamInfoApi,
  IAddSchoolAssociaExamInfoApi,
  IAddReportDataApi,
  IDelSchoolInfoApi,
  ISchoolListSortApi,
} from 'api';

const namespace = 'school';

interface IInitialState {
  loading: boolean;
  pageNum: number;
  pageSize: number;
  total: number;
  formLoading: boolean;
  schoolList: Array<any>;
  educationalInfo: any;
  EXAM: {
    loading: boolean;
    switchLoading: boolean;
    list: Array<any>;
    listLoading: boolean;
    listChild: Array<any>;
    reportedlistChild: Array<any>;
    educationalId: number;
  };
}

const initialState: IInitialState = {
  loading: true,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  schoolList: [],
  formLoading: false,
  educationalInfo: {},
  EXAM: {
    educationalId: 0,
    loading: true,
    switchLoading: false,
    list: [],
    listLoading: true,
    listChild: [],
    reportedlistChild: [],
  },
};

export const getSchoolList = createAsyncThunk(`${namespace}/getSchoolList`, async (params: IGetSchoolListApi) => {
  const {
    data: { rows, total },
  } = await getSchoolListApi(params);
  return {
    list: rows ?? [],
    total,
    pageSize: params.pageSize,
    pageNum: params.pageNum,
  };
});

export const getEducationalInfo = createAsyncThunk(`${namespace}/getEducationalInfo`, async (id: number) => {
  const { data } = await getEducationalInfoApi(id);

  return data;
});

export const getSchoolExamInfo = createAsyncThunk(`${namespace}/getSchoolExamInfo`, async (id: number) => {
  const {
    data: { rows },
  } = await getSchoolExamInfoApi(id);
  return rows ?? [];
});

export const onOffAppExamInfo = createAsyncThunk(
  `${namespace}/onOffAppExamInfo`,
  async (params: IOnOffAppExamInfoApi, { getState, dispatch }) => {
    await onOffAppExamInfoApi(params);
    const state = getState() as RootState;
    await dispatch(getSchoolExamInfo(state.school.EXAM.educationalId));
  },
);

export const addSchoolAssociaExamInfo = createAsyncThunk(
  `${namespace}/addSchoolAssociaExamInfo`,
  async (params: IAddSchoolAssociaExamInfoApi, { getState, dispatch }) => {
    await addSchoolAssociaExamInfoApi(params);
    const state = getState() as RootState;
    await dispatch(getSchoolExamInfo(state.school.EXAM.educationalId));
  },
);

export const getSchoolExamList = createAsyncThunk(`${namespace}/getSchoolExamList`, async (id: number) => {
  const {
    data: { rows },
  } = await getSchoolExamListApi(id);
  return rows ?? [];
});
export const getEducationalReportedList = createAsyncThunk(
  `${namespace}/getEducationalReportedList`,
  async (id: number) => {
    const {
      data: { rows },
    } = await getEducationalReportedListApi(id);
    return rows ?? [];
  },
);

export const addReportData = createAsyncThunk(
  `${namespace}/addReportData`,
  async (params: IAddReportDataApi, { getState }) => {
    const state = getState() as RootState;
    await addReportDataApi(params);
    // await dispatch(getEducationalReportedList(state.school.EXAM.educationalId));
  },
);
export const addReportSpecialData = createAsyncThunk(
  `${namespace}/addReportSpecialData`,
  async (params: IAddReportDataApi, { getState }) => {
    const state = getState() as RootState;

    await addReportSpecialDataApi(params);
    // await dispatch(getEducationalReportedList(state.school.EXAM.educationalId));
  },
);

export const delReportData = createAsyncThunk(`${namespace}/delReportData`, async (id: number) => {
  delReportDataApi(id);
  MessagePlugin.success('删除成功');
});
export const delReportSpecialData = createAsyncThunk(`${namespace}/delReportSpecialData`, async (id: number) => {
  delReportSpecialDataApi(id);
  MessagePlugin.success('删除成功');
});

export const delSchoolInfo = createAsyncThunk(`${namespace}/delSchoolInfo`, async (params: IDelSchoolInfoApi) => {
  await delSchoolInfoApi(params);
  MessagePlugin.success('删除成功');
});

export const addSchoolInfo = createAsyncThunk(`${namespace}/addSchoolInfo`, async (params: any) => {
  await addSchoolInfoApi(params);
  MessagePlugin.success('添加成功');
});

export const schoolListSort = createAsyncThunk(`${namespace}/schoolListSort`, async (params: ISchoolListSortApi) => {
  await schoolListSortApi(params);
});

const schoolSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setEducationalId: (state, action) => {
      state.EXAM.educationalId = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSchoolList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSchoolList.fulfilled, (state, action) => {
        state.loading = false;
        state.schoolList = action.payload?.list;
        state.pageNum = action.payload?.pageNum;
        state.pageSize = action.payload?.pageSize;
        state.total = action.payload?.total;
      })
      .addCase(getSchoolList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getEducationalInfo.pending, (state) => {
        state.formLoading = true;
      })
      .addCase(getEducationalInfo.fulfilled, (state, action) => {
        state.educationalInfo = action.payload;
        state.formLoading = false;
      })
      .addCase(getEducationalInfo.rejected, (state) => {
        state.formLoading = false;
      })

      .addCase(getSchoolExamInfo.pending, (state) => {
        state.EXAM.listLoading = true;
      })
      .addCase(getSchoolExamInfo.fulfilled, (state, action) => {
        state.EXAM.listLoading = false;
        state.EXAM.list = action.payload;
      })
      .addCase(getSchoolExamInfo.rejected, (state) => {
        state.EXAM.listLoading = false;
      })

      .addCase(onOffAppExamInfo.pending, (state) => {
        state.EXAM.switchLoading = true;
      })
      .addCase(onOffAppExamInfo.fulfilled, (state) => {
        state.EXAM.switchLoading = false;
      })
      .addCase(onOffAppExamInfo.rejected, (state) => {
        state.EXAM.switchLoading = false;
      })

      .addCase(getSchoolExamList.pending, (state) => {
        state.EXAM.listLoading = true;
      })
      .addCase(getSchoolExamList.fulfilled, (state, action) => {
        state.EXAM.listChild = action.payload;
        state.EXAM.listLoading = false;
      })
      .addCase(getSchoolExamList.rejected, (state) => {
        state.EXAM.listLoading = false;
      })
      .addCase(getEducationalReportedList.pending, (state) => {
        state.EXAM.listLoading = true;
      })
      .addCase(getEducationalReportedList.fulfilled, (state, action) => {
        state.EXAM.reportedlistChild = action.payload;
        state.EXAM.listLoading = false;
      })
      .addCase(getEducationalReportedList.rejected, (state) => {
        state.EXAM.listLoading = false;
      })
      .addCase(addSchoolInfo.pending, (state) => {
        state.formLoading = true;
      })
      .addCase(addSchoolInfo.fulfilled, (state) => {
        state.formLoading = false;
      })
      .addCase(addSchoolInfo.rejected, (state) => {
        state.formLoading = false;
      });
  },
});

export const { clearPageState, setEducationalId } = schoolSlice.actions;

export const selectSchool = (state: RootState) => state.school;

export default schoolSlice.reducer;
