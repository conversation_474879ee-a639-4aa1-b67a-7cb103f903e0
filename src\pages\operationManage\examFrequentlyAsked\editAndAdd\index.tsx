import React, { memo, useRef, useEffect, useState } from 'react';
import { Form, Row, Col, Button, MessagePlugin, Loading } from 'tdesign-react';
import classnames from 'classnames';
import { SubmitContext, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { addOrUpdateExamQuestion, selectExamFrequentlyAsked } from 'modules/operationManage/examFrequentlyAsked';
import { useLocation, useNavigate } from 'react-router-dom';
import { Back, Editor, Input } from 'components';
import { MENU_CONFIG, EDITOR_STYLE } from '../consts';
import { asyncValidate } from 'hooks';

const { FormItem } = Form;

const backPath = '/operationManage/examFrequentlyAsked';

const EditAndAdd: React.FC = () => {
  const navigate = useNavigate();
  const { state } = useLocation();

  const INITIAL_DATA =
    state.type === 'add'
      ? {
        questionContent: '',
        answerContent: '',
      }
      : { ...state.row };
  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>(null);
  const { formLoading } = useAppSelector(selectExamFrequentlyAsked);
  const [text, setText] = useState('');

  useEffect(() => {
    if (state.type === 'edit') {
      formRef.current?.setFieldsValue?.(state.row);
    }
  }, [useLocation]);

  const onSubmit = async (e: SubmitContext) => {
    const params = {
      examId: state.active,
      examQuestionId: state.type === 'add' ? undefined : state.row.examQuestionId,
      ...formRef.current?.getFieldsValue?.(true),
    };

    if (e.validateResult === true) {
      await dispatch(addOrUpdateExamQuestion(params));
      MessagePlugin.success(`${state.type === 'add' ? '新增' : '修改'}成功`);
      navigate(backPath);
    }
  };

  const handleReset = () => {
    setText(INITIAL_DATA.answerContent);
  };

  return (
    <>
      <Back path={backPath} header={state.pageTitle} />
      <div className={classnames(CommonStyle.pageWithColor)}>
        <div className={Style.formContainer}>
          <Loading loading={formLoading} showOverlay>
            <Form ref={formRef} onSubmit={onSubmit} onReset={handleReset} labelWidth={100} labelAlign='top'>
              <div className={Style.titleBox}></div>
              <Row gutter={[32, 44]}>
                <Col span={12}>
                  <Input
                    label='问题名称'
                    name='questionContent'
                    initialData={INITIAL_DATA.questionContent}
                    rules={[{ required: true, message: '问题名称必填', type: 'error' }]}
                    maxlength={20}
                  />
                </Col>
                <Col span={12} className={Style.dateCol}>
                  <FormItem
                    label='问题解答'
                    name='answerContent'
                    initialData={INITIAL_DATA.answerContent}
                    rules={[
                      { required: true, message: '问题解答必填', type: 'error' },
                      {
                        validator: asyncValidate,
                        message: '问题解答必填',
                        type: 'error',
                        trigger: 'blur',
                      },
                    ]}
                  >
                    <Editor
                      fixedToolbar='hide'
                      border
                      text={INITIAL_DATA.answerContent}
                      editorStyle={{ height: '600px', width: '100%' }}
                      maxLength={2000}
                      uploadVideoConfig={{ allowedFileTypes: ['mp4'], maxFileSize: 30 }}
                      uploadImgConfig={{ allowedFileTypes: ['jpg'], maxFileSize: 2 }}
                      onChange={(e: string) => setText(e)}
                    ></Editor>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem>
                    <Button type='submit' theme='primary'>
                      提交
                    </Button>
                    <Button type='reset' style={{ marginLeft: 12 }}>
                      重置
                    </Button>
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </Loading>
        </div>
      </div>
    </>
  );
};

export default memo(EditAndAdd);
