import React, { memo, useEffect, useRef } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back } from 'components';
import { Button, Form, FormInstanceFunctions, FormProps, Input, Loading, Space, Radio, Textarea } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectSystemConfig, editSystemConfig, getSystemConfigDetailsInfo } from 'modules/system/config';
import { useLocation, useNavigate } from 'react-router-dom';
import { IEditConfigBo } from 'types/system';
import { strIsNull } from 'utils/tool';

const { FormItem } = Form;

const EditConfig: React.FC = () => {
  const location = useLocation();
  const { configId } = location.state || {};
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { infoLoading, addEditLoading, info } = useAppSelector(selectSystemConfig);
  const formRef = useRef<FormInstanceFunctions>();

  const INITIAL_DATA = {
    configName: '',
    configKey: '',
    configValue: '',
    configType: 'Y',
    remark: '',
  };

  const rules: FormProps['rules'] = {
    configName: [
      {
        required: true,
        message: '参数名称不能为空',
        trigger: 'all',
      },
    ],
    configKey: [
      {
        required: true,
        message: '参数键名不能为空',
        trigger: 'all',
      },
    ],
    configValue: [
      {
        required: true,
        message: '参数键值不能为空',
        trigger: 'all',
      },
    ],
  };

  useEffect(() => {
    if (!strIsNull(configId)) {
      dispatch(getSystemConfigDetailsInfo(configId));
    }
  }, [configId]);

  useEffect(() => {
    if (!strIsNull(info)) {
      formRef.current?.setFieldsValue(info);
    }
  }, [info]);

  const handleReset: FormProps['onReset'] = () => {
    if (!strIsNull(configId)) {
      dispatch(getSystemConfigDetailsInfo(configId));
    }
  };

  const handleSubmit: FormProps['onSubmit'] = async ({ validateResult, fields }) => {
    if (validateResult === true) {
      const bo: IEditConfigBo = {
        ...fields,
        configId,
      };

      await dispatch(editSystemConfig(bo));
    }
  };

  const handleCancel = () => {
    navigate('/system/config');
  };

  return (
    <Loading loading={infoLoading} showOverlay style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Back path='/system/config' header='系统管理-参数管理' />
      <div className={classnames(CommonStyle.pageWithColor)}>
        <Form
          ref={formRef}
          initialData={INITIAL_DATA}
          resetType='initial'
          rules={rules}
          colon
          className={classnames(CommonStyle.commonSubjectForm)}
          onSubmit={handleSubmit}
          onReset={handleReset}
        >
          <FormItem label='参数名称' name='configName'>
            <Input placeholder='请输入参数名称' clearable />
          </FormItem>
          <FormItem label='参数键名' name='configKey'>
            <Input placeholder='请输入参数键名' clearable />
          </FormItem>
          <FormItem label='参数键值' name='configValue'>
            <Input placeholder='请输入参数键值' clearable />
          </FormItem>
          <FormItem label='系统内置' name='configType'>
            <Radio.Group>
              <Radio value='Y'>是</Radio>
              <Radio value='N'>否</Radio>
            </Radio.Group>
          </FormItem>
          <FormItem label='备注' name='remark'>
            <Textarea placeholder='请输入备注' />
          </FormItem>
          <FormItem className={classnames(CommonStyle.commonSubjectFormBtn)}>
            <Space>
              <Button loading={addEditLoading} type='submit' theme='primary'>
                提交
              </Button>
              <Button theme='default' onClick={handleCancel}>
                取消
              </Button>
              <Button type='reset' theme='warning'>
                重置
              </Button>
            </Space>
          </FormItem>
        </Form>
      </div>
    </Loading>
  );
};

export default memo(EditConfig);
