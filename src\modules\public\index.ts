import { RootState } from '../store';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import { publicGetExamListApi, publicAllSchoolListApi, publicAllLabelListApi, getDictsApi } from 'api';

const namespace = 'public';

const initialState = {
  loading: true,
  formLoading: false,
  total: 0,
  examList: [] as Array<any>,
  allSchoolList: [] as Array<any>,
  allLabelList: [] as Array<any>,
  dictList: [] as Array<any>,
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace);

export const publicGetExamList = useAsyncThunkWithStatus(`publicGetExamList`, async () => {
  const {
    data: { rows, total },
  } = await publicGetExamListApi();
  return {
    total,
    rows,
  };
});

export const publicAllSchoolList = useAsyncThunkWithStatus(`publicAllSchoolList`, async (educationalName) => {
  const {
    data: { rows, total },
  } = await publicAllSchoolListApi(educationalName as string);
  return {
    total,
    rows,
  };
});
export const publicAllLabelList = useAsyncThunkWithStatus(`publicAllLabelList`, async () => {
  const {
    data: { rows, total },
  } = await publicAllLabelListApi();
  return {
    rows,
    total,
  };
});

export const getDicts = useAsyncThunkWithStatus(`getDicts`, async (dictType: string) => {
  const { data } = await getDictsApi(dictType);

  return data;
});

const publicSliceSlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  cases: [
    {
      thunk: publicGetExamList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.total = action.payload.total;
        state.examList = action.payload.rows;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: publicAllSchoolList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.total = action.payload.total;
        state.allSchoolList = action.payload.rows;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: publicAllLabelList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.allLabelList = action.payload.rows.map((item: any) => {
          return {
            name: item,
            checked: false,
          };
        });
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getDicts,
      pending: (state) => {},
      fulfilled: (state, action) => {
        state.dictList = action.payload;
      },
      rejected: (state) => {},
    },
  ],
});

export const { clearPageState } = publicSliceSlice.actions;

export const selectPublic = (state: RootState) => state.publicStore;

export default publicSliceSlice.reducer;
