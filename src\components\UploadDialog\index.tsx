import React from 'react';
import { DialogPlugin, Progress, Alert } from 'tdesign-react';
import { CloseIcon } from 'tdesign-icons-react';

const dialogContainer = {
  myDialog: null as any,
  abortController: null as AbortController | null,
};

export const closeSignal = () => {
  dialogContainer.abortController = new AbortController();
  return dialogContainer.abortController.signal;
};

const stateColor = {
  active: 'var(--td-brand-color)',
  success: 'var(--td-success-color)',
  error: 'var(--td-error-color)',
};

const DialogBody = (result: any, uploadState: 'active' | 'success' | 'error', progress = 0) => (
  <>
    <div style={{ margin: '0 31%' }}>
      <Progress
        theme={'circle'}
        status={uploadState}
        label={
          <div>
            {uploadState !== 'error' && <p style={{ color: `${stateColor[uploadState]}` }}>{`${progress}%`}</p>}
            {uploadState === 'active' && (
              <p style={{ fontSize: '14px', color: `${stateColor[uploadState]}`, marginTop: '10px' }}>正在上传...</p>
            )}
            {uploadState === 'success' && (
              <p style={{ fontSize: '14px', color: `${stateColor[uploadState]}`, marginTop: '10px' }}>上传成功</p>
            )}
            {uploadState === 'error' && (
              <>
                <CloseIcon size={'35px'} color='var(--td-error-color)' />
                <p style={{ fontSize: '14px', color: `${stateColor[uploadState]}`, marginTop: '10px' }}>上传失败</p>
              </>
            )}
          </div>
        }
        size={'large'}
        percentage={progress as number}
      ></Progress>
    </div>
    {uploadState === 'error' && (
      <Alert style={{ width: '100%', marginTop: '20px' }} theme='error' message={`失败原因：${result?.msg}`} />
    )}
  </>
);

export const showDialog = (
  result: any,
  uploadState: 'active' | 'success' | 'error',
  progress = 0,
  onClose: (abort: any) => void = () => {},
) => {
  const progressCun = () => {
    if (progress === 100 && result?.code === 200) return 99;
    if (progress === 100 && result === '_') return 99;
    return progress;
  };

  if (!dialogContainer.myDialog && uploadState !== 'error') {
    dialogContainer.myDialog = DialogPlugin({
      body: DialogBody(result, uploadState, progressCun()),
      theme: 'default',
      width: '500px',
      closeBtn: false,
      confirmBtn: false,
      closeOnOverlayClick: false,
      onClose: () => {
        if (dialogContainer.abortController) {
          dialogContainer.abortController.abort();
          onClose(dialogContainer.abortController.abort());
        }
        dialogContainer.myDialog.hide();
        dialogContainer.myDialog = null;
      },
    });
  } else {
    dialogContainer.myDialog.update({
      body: DialogBody(result, uploadState, progressCun()),
    });
  }
};
