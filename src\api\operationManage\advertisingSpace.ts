import request from 'utils/request/index';

export interface IGetAdvertListApi {
  pageNum: number;
  pageSize: number;
  advertName?: string;
}

/**
 * 分页条件查询广告列表信息
 * @returns
 */
export const getAdvertListApi = async (params: IGetAdvertListApi) => {
  const result = await request.get({
    url: '/advert/getAdvertList',
    params,
  });
  return result;
};

/**
 * 删除广告信息
 * @returns
 */
export const deleteAdvertApi = async (id: number) => {
  const result = await request.delete({
    url: `/advert/deleteAdvert/${id}`,
  });
  return result;
};

export interface IOpenOrCloseAdvertApi {
  advertId: number;
  isOpen: 0 | 1;
}

/**
 * 开启/关闭广告位
 * @param params
 * @returns
 */
export const openOrCloseAdvertApi = async (params: IOpenOrCloseAdvertApi) => {
  const result = await request.post({
    url: `/advert/openOrCloseAdvert`,
    params,
  });
  return result;
};

export interface IAddOrUpdateAdvertApi {
  /** 广告id */
  advertId?: number;

  /** 广告名称 */
  advertName: string;

  /** 广告内容(站内: 富文本, 站外:链接) */
  advertContent: string;

  /** 广告类型(1:站内广告，2:站外广告) */
  advertType: number;

  /** 广告封面(文件id) */
  advertCover: number;
}

export const addOrUpdateAdvertApi = async (params: IAddOrUpdateAdvertApi) => {
  const result = await request.post({
    url: `/advert/addOrUpdateAdvert`,
    params,
  });
  return result;
};
