import React, { memo, useEffect, useState, useRef } from 'react';
import { Button, TableProps, Switch, Popconfirm, DialogPlugin, Col, Row, MessagePlugin } from 'tdesign-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'modules/store';
import {
  getBulletinTrendsList,
  openOrCloseTrends,
  openOrCloseBulletinTrend,
  deleteTrends,
  selectDynamicPublishing,
  clearPageState,
} from 'modules/operationManage/dynamicPublishing';
import { publicGetExamList, selectPublic } from 'modules/public';

import { Tables, Search } from 'components';
import { IRef } from 'components/Form';

export default memo(() => {
  const dispatch = useAppDispatch();
  const pageState = useAppSelector(selectDynamicPublishing);
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const { loading, list, pageNum, pageSize, total } = pageState;
  const { examList } = useAppSelector(selectPublic);

  const navigate = useNavigate();

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  const searchParams = { pageNum, pageSize };

  useEffect(() => {
    dispatch(getBulletinTrendsList(searchParams));
    if (examList.length === 0) {
      dispatch(publicGetExamList(''));
    }
    return () => {
      dispatch(clearPageState());
    };
  }, [dispatch]);

  const columns: TableProps['columns'] = [
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    { colKey: 'serial-number', width: 60, title: '序号' },
    {
      colKey: 'title',
      title: '动态标题',
    },
    {
      colKey: 'examNameList',
      title: '可见范围',
      cell({ row }) {
        return row?.examNameList ? row.examNameList.join('，') : '';
      },
    },
    {
      colKey: 'topUp',
      title: '置顶',
      cell({ row }) {
        return (
          <Switch
            value={row.topUp === 1}
            onChange={async (val) => {
              await dispatch(
                openOrCloseBulletinTrend({
                  trendsId: row.trendsId,
                  isTop: val ? 1 : 0,
                }),
              );
              MessagePlugin.success(`${row.topUp === 1 ? '关闭' : '开启'}成功！`);
            }}
          />
        );
      },
    },
    {
      colKey: 'isOpen',
      title: '状态',
      cell({ row }) {
        return (
          <Switch
            value={row.isOpen === 1}
            onChange={async (val) => {
              await dispatch(
                openOrCloseTrends({
                  trendsId: row.trendsId,
                  isOpen: val ? 1 : 0,
                }),
              );
              MessagePlugin.success(`${row.isOpen === 1 ? '关闭' : '开启'}成功！`);
            }}
          />
        );
      },
    },
    {
      fixed: 'right',
      width: 180,
      colKey: 'op',
      title: '操作',
      cell({ row }) {
        return (
          <>
            <Button
              theme='primary'
              variant='text'
              onClick={() => navigate(`/operationManage/dynamicPublishing/edit`, { state: { row, type: 'edit' } })}
            >
              编辑
            </Button>

            <Popconfirm
              content='确定要删除该数据吗？'
              theme='danger'
              onConfirm={() => {
                dispatch(deleteTrends({ trendsIdList: [row.trendsId] }));
              }}
            >
              <Button theme='danger' variant='text'>
                删除
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  return (
    <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getBulletinTrendsList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '动态标题',
            field: 'title',
          },
          {
            type: 'select',
            label: '可见范围',
            field: 'examIdList',
            valueField: 'examId',
            nameField: 'examName',
            options: examList,
            multiple: true,
          },
        ]}
      />
      <div className='tabs-content' style={{ margin: 20 }}>
        <Row gutter={8} align='middle'>
          <Col>
            <Button
              variant='outline'
              theme='primary'
              onClick={() => navigate('/operationManage/dynamicPublishing/add', { state: { type: 'add' } })}
            >
              新增动态
            </Button>
          </Col>
          <Col>
            <Button
              variant='outline'
              theme='danger'
              onClick={() => {
                if (selectedRowKeys.length === 0) return MessagePlugin.warning('请先选择要批量删除的数据！');
                const confirmDia = DialogPlugin.confirm({
                  header: '批量删除提示',
                  body: '确定批量删除数据吗？',
                  theme: 'warning',
                  confirmBtn: '确定',
                  cancelBtn: '取消',
                  onConfirm: async () => {
                    await dispatch(deleteTrends({ trendsIdList: selectedRowKeys }));

                    handleResetSelection();
                    confirmDia.hide();
                  },
                  onClose: () => {
                    confirmDia.hide();
                  },
                });
                return false;
              }}
            >
              批量删除
            </Button>
          </Col>
        </Row>
      </div>
      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list,
          loading,
          rowKey: 'trendsId',
          selectedRowKeys,
          onSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getBulletinTrendsList}
        params={searchParams}
      />
    </div>
  );
});
