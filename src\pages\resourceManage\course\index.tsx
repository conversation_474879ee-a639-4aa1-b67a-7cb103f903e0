import React, { memo, useEffect } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import Tree from './Tree';
import Table from './Table';
import { useAppDispatch } from 'modules/store';
import { clearPageState } from 'modules/resourceManage/course';

const Course: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(
    () => () => {
      dispatch(clearPageState());
    },
    [],
  );

  return (
    <div className={classnames(CommonStyle.pageWithColor, CommonStyle.pageRow)}>
      <Tree />
      <Table />
    </div>
  );
};

export default memo(Course);
