import { RootState } from '../store';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import {
  getInformationReleaseListApi,
  addOrUpdateInformationReleaseApi,
  deletInformationReleaseApi,
  openOrCloseInfoApi,
} from 'api';
import type { IGetInformationReleaseListApi, IOpenOrCloseInfoApi, IAddOrUpdateInformationReleaseApi } from 'api';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'informationRelease';

const initialState = {
  loading: true,
  formLoading: false,
  pageNum: 1,
  pageSize: 12,
  total: 0,
  list: [] as Array<any>,
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getInformationReleaseList = useAsyncThunkWithStatus(`getInformationReleaseList`, async (params) => {
  const {
    data: { rows, total },
  } = await getInformationReleaseListApi(params as IGetInformationReleaseListApi);
  const { pageNum, pageSize } = params;
  return {
    total,
    rows,
    pageNum,
    pageSize,
  };
});

export const openOrCloseInfo = useAsyncThunkWithStatus(`openOrCloseInfo`, async (params, { dispatch, getState }) => {
  await openOrCloseInfoApi(params as IOpenOrCloseInfoApi);
  const {
    informationRelease: { pageNum, pageSize },
  } = getState() as RootState;
  await dispatch(
    getInformationReleaseList({
      pageNum,
      pageSize,
    }),
  );
});

export const addOrUpdateInformationRelease = useAsyncThunkWithStatus(
  `addOrUpdateInformationRelease`,
  async (params, { dispatch, getState }) => {
    const {
      informationRelease: { pageNum, pageSize },
    } = getState() as RootState;
    await addOrUpdateInformationReleaseApi(params as IAddOrUpdateInformationReleaseApi);
    await dispatch(
      getInformationReleaseList({
        pageNum,
        pageSize,
      }),
    );
  },
);

export const deletInformationRelease = useAsyncThunkWithStatus(
  `deletInformationRelease`,
  async (id, { dispatch, getState }) => {
    await deletInformationReleaseApi(id as number);
    const {
      informationRelease: { pageNum, pageSize },
    } = getState() as RootState;
    MessagePlugin.success('删除成功！');
    await dispatch(
      getInformationReleaseList({
        pageNum,
        pageSize,
      }),
    );
  },
);

const informationReleaseSliceSlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  cases: [
    {
      thunk: getInformationReleaseList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.total = action.payload.total;
        state.list = action.payload.rows;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: openOrCloseInfo,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state) => {
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: addOrUpdateInformationRelease,
      pending: (state) => {
        state.formLoading = true;
      },
      fulfilled: (state) => {
        state.formLoading = false;
      },
      rejected: (state) => {
        state.formLoading = false;
      },
    },
  ],
});

export const { clearPageState } = informationReleaseSliceSlice.actions;

export const selectInformationRelease = (state: RootState) => state.informationRelease;

export default informationReleaseSliceSlice.reducer;
