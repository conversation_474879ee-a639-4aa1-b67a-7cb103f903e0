import { RootState } from '../store';
import { getRankingListApi, getAdminRankingListApi, deleteVirtualUser<PERSON>pi, addVirtualUserApi } from 'api';
import type { IGetRankingListApi, IGetAdminRankingListApi, IDeleteVirtualUserApi, IAddVirtualUserApi } from 'api';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'theCharts';

const initialState = {
  loading: true,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  list: [] as Array<any>,
  details: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
    list: [] as Array<any>,
  },
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getRankingList = useAsyncThunkWithStatus(`getRankingList`, async (params) => {
  const {
    data: { rows, total },
  } = await getRankingList<PERSON>pi(params as IGetRankingListApi);
  const { pageNum, pageSize } = params;
  return {
    total,
    rows,
    pageNum,
    pageSize,
  };
});

export const getAdminRankingList = useAsyncThunkWithStatus(`getAdminRankingList`, async (params) => {
  const {
    data: { rows, total },
  } = await getAdminRankingListApi(params as IGetAdminRankingListApi);
  const { pageNum, pageSize } = params;
  return {
    total,
    rows,
    pageNum,
    pageSize,
  };
});

export const deleteVirtualUser = useAsyncThunkWithStatus('deleteVirtualUser', async (params) => {
  await deleteVirtualUserApi(params as IDeleteVirtualUserApi);
  await MessagePlugin.success(`删除成功！`);
});

export const addVirtualUser = useAsyncThunkWithStatus('addVirtualUser', async (params) => {
  await addVirtualUserApi(params as IAddVirtualUserApi);
  await MessagePlugin.success(`添加成功！`);
});

const theChartsSliceSlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  cases: [
    {
      thunk: getRankingList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.total = action.payload.total;
        state.list = action.payload.rows;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getAdminRankingList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.details.total = action.payload.total;
        state.details.list = action.payload.rows;
        state.details.pageNum = action.payload.pageNum;
        state.details.pageSize = action.payload.pageSize;
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
  ],
});

export const { clearPageState } = theChartsSliceSlice.actions;

export const selectTheCharts = (state: RootState) => state.theCharts;

export default theChartsSliceSlice.reducer;
