import React, { memo, useEffect } from 'react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import Tree from './Tree';
import Tab from './Tab';
import { useAppDispatch } from 'modules/store';
import { clearPageState } from 'modules/srRanking/majors';

const SRRankingMajors: React.FC = () => {
  const dispatch = useAppDispatch();

  useEffect(
    () => () => {
      dispatch(clearPageState());
    },
    [],
  );

  return (
    <div className={classnames(CommonStyle.pageWithColor, CommonStyle.pageRow)}>
      <Tree />
      <Tab />
    </div>
  );
};

export default memo(SRRankingMajors);
