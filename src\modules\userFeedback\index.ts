import { RootState } from '../store';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import { MessagePlugin } from 'tdesign-react';
import { getFeedbackDetailApi, updateFeedbackStatusApi, getFeedbackListApi } from 'api';
import type { IUpdateFeedbackStatusApi, IGetFeedbackListApi } from 'api';

const namespace = 'userFeedback';

const initialState = {
  loading: true,
  switchLoading: false,
  type: 1,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  list: [] as Array<any>,
  detailLoading: true,
  feedbackDetail: {},
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getFeedbackList = useAsyncThunkWithStatus(`getFeedbackList`, async (params) => {
  const {
    data: { rows, total },
  } = await getFeedbackListApi(params as IGetFeedbackListApi);
  const { pageNum, pageSize } = params as IGetFeedbackListApi;
  return {
    total,
    rows,
    pageNum,
    pageSize,
  };
});

export const getFeedbackDetail = useAsyncThunkWithStatus(`getFeedbackDetail`, async (id) => {
  const { data } = await getFeedbackDetailApi(id as number);
  return data;
});

export const updateFeedbackStatus = useAsyncThunkWithStatus(
  `updateFeedbackStatus`,
  async (params, { getState, dispatch }) => {
    const {
      userFeedback: { pageNum, pageSize, type: feedbackType },
    } = getState() as RootState;
    await updateFeedbackStatusApi(params as IUpdateFeedbackStatusApi);
    await dispatch(getFeedbackList({ pageNum, pageSize, feedbackType }));
    await MessagePlugin.success(`修改成功！`);
  },
);

const userFeedbackSliceSlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setType: (state, action) => {
      state.type = action.payload;
    },
  },
  cases: [
    {
      thunk: getFeedbackList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.total = action.payload.total;
        state.list = action.payload.rows;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: updateFeedbackStatus,
      pending: (state) => {
        state.switchLoading = true;
      },
      fulfilled: (state) => {
        state.switchLoading = false;
      },
      rejected: (state) => {
        state.switchLoading = false;
      },
    },
    {
      thunk: getFeedbackDetail,
      pending: (state) => {
        state.detailLoading = true;
      },
      fulfilled: (state, action) => {
        state.detailLoading = false;
        state.feedbackDetail = action.payload;
      },
      rejected: (state) => {
        state.detailLoading = false;
      },
    },
  ],
});

export const { clearPageState, setType } = userFeedbackSliceSlice.actions;

export const selectuserFeedback = (state: RootState) => state.userFeedback;

export default userFeedbackSliceSlice.reducer;
