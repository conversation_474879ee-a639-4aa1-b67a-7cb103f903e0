import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getGoodsListApi,
  getGoodsInfoApi,
  getResourceListApi,
  addGoodsApi,
  editGoodsApi,
  updateGoodsListed<PERSON>pi,
  updateGoodsTopped<PERSON>pi,
  deleteGoods<PERSON><PERSON>,
  previewGoods<PERSON>pi,
  getSubjectList<PERSON>pi,
} from 'api';
import {
  IAddGoodsBo,
  IEditGoodsBo,
  IGetGoodsInfoBo,
  IGetGoodsListBo,
  IGetResourceListBo,
  IPreviewGoodsBo,
  IUpdateGoodsListedBo,
  IUpdateGoodsToppedBo,
} from 'types/mallManage';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'mallManage/goods';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  updateGoodsToppedLoading: boolean;
  updateGoodsListedLoading: boolean;
  deleteGoodsLoading: boolean;
  previewGoodsLoading: boolean;
  fileUrl: string;
  subjectLoading: boolean;
  subjectList: Array<any>;
  resourceLoading: boolean;
  resourceList: Array<any>;
  goodsInfoLoading: boolean;
  goodsInfo: any;
  addEditGoodsLoading: boolean;
}

const initialState: IInitialState = {
  loading: false,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  updateGoodsToppedLoading: false,
  updateGoodsListedLoading: false,
  deleteGoodsLoading: false,
  previewGoodsLoading: false,
  fileUrl: '',
  subjectLoading: false,
  subjectList: [],
  resourceLoading: false,
  resourceList: [],
  goodsInfoLoading: false,
  goodsInfo: {},
  addEditGoodsLoading: false,
};

export const getGoodsList = createAsyncThunk(`${namespace}/getGoodsList`, async (bo: IGetGoodsListBo) => {
  const { data } = await getGoodsListApi(bo);

  return {
    list: data.rows,
    total: data.total,
    pageNum: bo.pageNum,
    pageSize: bo.pageSize,
  };
});

export const getGoodsInfo = createAsyncThunk(`${namespace}/getGoodsInfo`, async (bo: IGetGoodsInfoBo) => {
  const { data } = await getGoodsInfoApi(bo);

  return data;
});

export const getSubjectList = createAsyncThunk(`${namespace}/getSubjectList`, async () => {
  const { data } = await getSubjectListApi(null);

  return data;
});

export const getResourceList = createAsyncThunk(`${namespace}/getResourceList`, async (bo: IGetResourceListBo) => {
  const { data } = await getResourceListApi(bo);

  return data;
});

export const updateGoodsTopped = createAsyncThunk(
  `${namespace}/updateGoodsTopped`,
  async (bo: IUpdateGoodsToppedBo) => {
    await updateGoodsToppedApi(bo);
  },
);

export const updateGoodsListed = createAsyncThunk(
  `${namespace}/updateGoodsListed`,
  async (bo: IUpdateGoodsListedBo) => {
    await updateGoodsListedApi(bo);
  },
);

export const addGoods = createAsyncThunk(`${namespace}/addGoods`, async (bo: IAddGoodsBo) => {
  await addGoodsApi(bo);
});

export const editGoods = createAsyncThunk(`${namespace}/editGoods`, async (bo: IEditGoodsBo) => {
  await editGoodsApi(bo);
});

export const deleteGoods = createAsyncThunk(`${namespace}/deleteGoods`, async (goodsId: number) => {
  await deleteGoodsApi(goodsId);
});

export const previewGoods = createAsyncThunk(`${namespace}/previewGoods`, async (bo: IPreviewGoodsBo) => {
  const { data } = await previewGoodsApi(bo);

  return data;
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
    setFileUrl: (state, { payload }) => {
      state.fileUrl = payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getGoodsList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getGoodsList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getGoodsList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getGoodsInfo.pending, (state) => {
        state.goodsInfoLoading = true;
      })
      .addCase(getGoodsInfo.fulfilled, (state, action) => {
        state.goodsInfo = action.payload;
        state.goodsInfoLoading = false;
      })
      .addCase(getGoodsInfo.rejected, (state) => {
        state.goodsInfoLoading = false;
      })

      .addCase(getSubjectList.pending, (state) => {
        state.subjectLoading = true;
      })
      .addCase(getSubjectList.fulfilled, (state, action) => {
        state.subjectList = [
          {
            subjectId: -1,
            subjectName: '全部',
          },
          ...action.payload.rows,
        ];
        state.subjectLoading = false;
      })
      .addCase(getSubjectList.rejected, (state) => {
        state.subjectLoading = false;
      })

      .addCase(getResourceList.pending, (state) => {
        state.resourceLoading = true;
      })
      .addCase(getResourceList.fulfilled, (state, action) => {
        state.resourceList = action.payload;
        state.resourceLoading = false;
      })
      .addCase(getResourceList.rejected, (state) => {
        state.resourceLoading = false;
      })

      .addCase(updateGoodsTopped.pending, (state) => {
        state.updateGoodsToppedLoading = true;
      })
      .addCase(updateGoodsTopped.fulfilled, (state) => {
        state.updateGoodsToppedLoading = false;
      })
      .addCase(updateGoodsTopped.rejected, (state) => {
        state.updateGoodsToppedLoading = false;
      })

      .addCase(updateGoodsListed.pending, (state) => {
        state.updateGoodsListedLoading = true;
      })
      .addCase(updateGoodsListed.fulfilled, (state) => {
        state.updateGoodsListedLoading = false;
      })
      .addCase(updateGoodsListed.rejected, (state) => {
        state.updateGoodsListedLoading = false;
      })

      .addCase(addGoods.pending, (state) => {
        state.addEditGoodsLoading = true;
      })
      .addCase(addGoods.fulfilled, (state) => {
        state.addEditGoodsLoading = false;
        MessagePlugin.success('添加成功！');
      })
      .addCase(addGoods.rejected, (state) => {
        state.addEditGoodsLoading = false;
      })

      .addCase(editGoods.pending, (state) => {
        state.addEditGoodsLoading = true;
      })
      .addCase(editGoods.fulfilled, (state) => {
        state.addEditGoodsLoading = false;
        MessagePlugin.success('编辑成功！');
      })
      .addCase(editGoods.rejected, (state) => {
        state.addEditGoodsLoading = false;
      })

      .addCase(deleteGoods.pending, (state) => {
        state.deleteGoodsLoading = true;
      })
      .addCase(deleteGoods.fulfilled, (state) => {
        state.deleteGoodsLoading = false;

        const isPrePage = (state.total - (state.pageNum - 1) * state.pageSize) % state.pageSize === 1;

        if (isPrePage) {
          // eslint-disable-next-line no-plusplus
          --state.pageNum;
        }
      })
      .addCase(deleteGoods.rejected, (state) => {
        state.deleteGoodsLoading = false;
      })

      .addCase(previewGoods.pending, (state) => {
        state.previewGoodsLoading = true;
      })
      .addCase(previewGoods.fulfilled, (state, action) => {
        state.fileUrl = action.payload.fileUrl;
        state.previewGoodsLoading = false;
      })
      .addCase(previewGoods.rejected, (state) => {
        state.previewGoodsLoading = false;
      });
  },
});

export const { clearPageState, setFileUrl } = listBaseSlice.actions;

export const selectMallManageGoods = (state: RootState) => state.mallManageGoods;

export default listBaseSlice.reducer;
