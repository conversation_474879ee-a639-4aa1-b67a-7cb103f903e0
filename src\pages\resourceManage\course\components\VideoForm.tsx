/* eslint-disable no-nested-ternary */
import React, { Fragment, memo, useEffect, useMemo, useRef, useState } from 'react';
import { Form, Loading, type FormInstanceFunctions, FormProps, InputNumber, Space, Button } from 'tdesign-react';
import {
  selectResourceManageCourse,
  previewVideo,
  getChapterInfo,
  editResourceCourseChapter,
} from 'modules/resourceManage/course';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { strIsNull } from 'utils/tool';
import { IEditResourceCourseChapterBo, IPreviewVideoBo } from 'types/resourceManage';

const { FormItem } = Form;

interface VideoFormProps {
  disabled?: boolean;
  chapterId: number;
  onChangeVideo: (val) => void;
}

const VideoForm: React.FC<VideoFormProps> = ({ disabled, chapterId, onChangeVideo }) => {
  const dispatch = useAppDispatch();
  const { chapterInfoLoading, chapterInfo, previewVideoLoading, previewVideoInfo } =
    useAppSelector(selectResourceManageCourse);
  const formRef = useRef<FormInstanceFunctions>();
  const [form] = Form.useForm();
  const fileId = Form.useWatch('fileId', form);
  const [videoInfo, setVideoInfo] = useState({});

  const INITIAL_DATA = {
    appId: '',
    fileId: '',
  };

  const rules: FormProps['rules'] = {
    appId: [
      {
        required: true,
        message: `应用ID不能为空`,
        trigger: 'all',
      },
    ],
    fileId: [
      {
        required: true,
        message: `FileID不能为空`,
        trigger: 'all',
      },
    ],
  };

  useEffect(() => {
    dispatch(getChapterInfo(chapterId));
  }, [chapterId]);

  useEffect(() => {
    if (!strIsNull(chapterInfo) && !strIsNull(chapterInfo.pvwVideoVo)) {
      const info = {
        appId: chapterInfo.pvwVideoVo.appID,
        fileId: chapterInfo.pvwVideoVo.fileID,
      };

      setVideoInfo(chapterInfo.pvwVideoVo);

      onChangeVideo(chapterInfo.pvwVideoVo);

      formRef.current?.setFieldsValue(info);
    } else {
      formRef.current?.setFieldsValue(INITIAL_DATA);
      setVideoInfo({});
    }
  }, [chapterInfo]);

  useEffect(() => {
    if (!strIsNull(previewVideoInfo)) {
      setVideoInfo(previewVideoInfo);
      onChangeVideo(previewVideoInfo);
    }
  }, [previewVideoInfo]);

  const handleSubmit: FormProps['onSubmit'] = ({ validateResult, fields }) => {
    if (validateResult === true) {
      const form: IPreviewVideoBo = fields;

      dispatch(previewVideo(form));
    }
  };

  const handleReset: FormProps['onReset'] = () => {
    console.log(123);
  };

  const fileSize = useMemo(() => {
    if (!strIsNull(videoInfo)) {
      const temp = videoInfo.fileSize / 1024 / 1024;
      const value = temp >= 1024 ? `${(temp / 1024).toFixed(2)}GB` : `${temp.toFixed(2)}MB`;
      return value;
    }

    return '';
  }, [videoInfo]);

  const handleSaveVideo = () => {
    const bo: IEditResourceCourseChapterBo = {
      id: chapterId,
      filesId: videoInfo.id,
    };

    dispatch(editResourceCourseChapter(bo));
  };

  return (
    <Loading loading={chapterInfoLoading}>
      <Form
        disabled={disabled === true}
        ref={formRef}
        form={form}
        initialData={INITIAL_DATA}
        resetType='initial'
        rules={rules}
        colon
        onSubmit={handleSubmit}
        onReset={handleReset}
      >
        <FormItem label='应用ID' name='appId'>
          <InputNumber
            style={{ width: '260px' }}
            placeholder='请输入应用ID'
            theme='normal'
            decimalPlaces={0}
            allowInputOverLimit={false}
            largeNumber
          />
        </FormItem>
        <FormItem label='FileID' name='fileId'>
          <InputNumber
            style={{ width: '260px' }}
            placeholder='请输入FileID'
            theme='normal'
            decimalPlaces={0}
            allowInputOverLimit={false}
            largeNumber
          />
        </FormItem>
        {!strIsNull(videoInfo) && videoInfo.fileID === fileId && (
          <Fragment>
            <FormItem label='视频名称'>
              <span>{videoInfo?.fileName}</span>
            </FormItem>
            <FormItem label='视频大小'>
              <span>{fileSize}</span>
            </FormItem>
            <FormItem label='视频时长'>
              <span>{videoInfo?.timelen} S</span>
            </FormItem>
          </Fragment>
        )}
        <FormItem>
          <Space>
            {strIsNull(videoInfo) || videoInfo.fileID !== fileId ? (
              <Fragment>
                <Button disabled={disabled === true} type='submit' theme='primary' loading={previewVideoLoading}>
                  预览
                </Button>
                <Button disabled={disabled === true} type='reset' theme='warning'>
                  重置
                </Button>
              </Fragment>
            ) : videoInfo.fileID !== fileId || videoInfo.id !== chapterInfo.filesId ? (
              <Button
                disabled={disabled === true}
                theme='primary'
                loading={previewVideoLoading}
                onClick={handleSaveVideo}
              >
                提交
              </Button>
            ) : null}
          </Space>
        </FormItem>
      </Form>
    </Loading>
  );
};

export default memo(VideoForm);
