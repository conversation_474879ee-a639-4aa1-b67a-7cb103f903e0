import React, { useState, useEffect, useImperativeHandle, forwardRef } from 'react';

import '@wangeditor/editor/dist/css/style.css';

import { Editor, Toolbar } from '@wangeditor/editor-for-react';
import { Boot } from '@wangeditor/editor';
// import formulaModule from '@wangeditor/plugin-formula';

import { MENU_CONFIG, HIDE_MENU_CONFIG, EDITOR_CONFIG, EDITOR_STYLE } from './config';
import type { IRichTextEditor } from './config';
import Style from './index.module.less';

let isFormulaRegistered = false;

async function loadFormulaModule() {
  if (!isFormulaRegistered) {
    const { default: formulaModule } = await import('@wangeditor/plugin-formula');
    Boot.registerModule(formulaModule);
    isFormulaRegistered = true;
  }
}

loadFormulaModule();

const RichTextEditor: React.FC<IRichTextEditor> = forwardRef(
  (
    {
      placeholder,
      uploadImgConfig,
      uploadVideoConfig,
      border,
      editorStyle,
      toolbar,
      text,
      hideToolbar,
      fixedToolbar = 'default',
      onChange,
      onChangeHtml,
      onChangeText,
      maxLength,
      readOnly,
    }: IRichTextEditor,
    ref,
  ) => {
    const configProps = {
      placeholder,
      uploadImgConfig,
      uploadVideoConfig,
      border,
      editorStyle,
      toolbar,
      text,
      hideToolbar,
      fixedToolbar,
      onChange,
      maxLength,
      readOnly,
    };
    const toolbarConfig = {
      toolbarKeys: toolbar ?? MENU_CONFIG,
      excludeKeys: HIDE_MENU_CONFIG.concat(hideToolbar),
    };

    const [editor, setEditor] = useState(null);
    const [showToolbar, setShowToolbar] = useState(fixedToolbar === 'hide');
    const [html, setHtml] = useState(text ?? '');

    const editorStyles = editorStyle ?? EDITOR_STYLE;

    const config = {
      ...EDITOR_CONFIG(configProps),
      readOnly: configProps.readOnly,
    };

    useEffect(() => {
      setHtml(text as string);
    }, [text]);

    const editorConfigs = {
      ...config,
      onFocus: () => {
        if (fixedToolbar === 'default') {
          setShowToolbar(true);
        }
      },
      onBlur: () => {
        if (fixedToolbar === 'default') {
          setShowToolbar(false);
        }
      },
    };

    const getCreated = (item: any) => {
      setEditor(item);
    };

    useEffect(
      () => () => {
        if (editor == null) return;
        editor.destroy();
        setEditor(null);
      },
      [editor],
    );

    const editorOnChange = (editor: any) => {
      if (onChangeText) {
        onChangeText(editor.getText());
      }
      if (onChange) {
        // 获取编辑器的 HTML 内容
        const content = editor.getHtml();
        // 去除空行
        const cleanedContent = content.replace(/<p><br><\/p>/g, '');

        setHtml(cleanedContent);
        onChange(cleanedContent);
      }
      if (onChangeHtml) {
        // 获取编辑器的 HTML 内容
        const content = editor.getHtml();
        // 去除空行
        const cleanedContent = content.replace(/<p><br><\/p>/g, '');

        onChangeHtml(cleanedContent);
      }
    };

    // 使用 useImperativeHandle 向父组件暴露方法
    useImperativeHandle(ref, () => ({
      // 清空编辑器内容
      clearContent: (html: string = '') => {
        if (editor) {
          if (html !== '') {
            editor.setHtml(html);
          } else {
            // 将编辑器的内容设为空
            editor.children = [{ type: 'paragraph', children: [{ text: '' }] }];
            // 触发编辑器更新
            editor.onChange();
          }
        }
      },
    }));

    return (
      <>
        <div
          className={Style.Editor}
          style={{ border: border ? '1px solid #ccc' : '', zIndex: 100, width: !showToolbar ? '100%' : '' }}
        >
          {showToolbar && (
            <Toolbar
              editor={editor}
              defaultConfig={toolbarConfig}
              mode='default'
              style={{ borderBottom: '1px solid #ccc' }}
            />
          )}
          <Editor
            defaultConfig={editorConfigs}
            value={html}
            onCreated={getCreated}
            onChange={editorOnChange}
            mode='default'
            style={editorStyles}
          />
        </div>
      </>
    );
  },
);

export default RichTextEditor;
