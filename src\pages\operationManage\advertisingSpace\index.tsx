import React from 'react';
import classnames from 'classnames';
import { SelectTable } from './Select';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import BasicList from './components/BasicList';
import Right from './components/Right';

const TreeTable: React.FC = () => (
  <div className={classnames(CommonStyle.pageWithColor, Style.content)}>
    <div className={Style.treeContent}>
      <BasicList></BasicList>
    </div>
    <div className={Style.tableContent}>
      <SelectTable />
    </div>
    <div className={Style.treeContentRight}>
      <Right></Right>
    </div>
  </div>
);

export default TreeTable;
