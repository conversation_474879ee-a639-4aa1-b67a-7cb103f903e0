import { IAddConfigBo, IEditConfigBo, IGetConfigListBo } from 'types/system';
import request from 'utils/request/index';

/**
 * 查询参数列表
 * @returns
 */
export const getSystemConfigListApi = async (params: IGetConfigListBo) => {
  const result = await request.get({
    url: '/system/config/list',
    params,
  });
  return result;
};

/**
 * 删除参数
 * @param configId
 * @returns
 */
export const delSystemConfigApi = async (configId: number | string) => {
  const result = await request.delete({
    url: `/system/config/${configId}`,
  });
  return result;
};

export const addSystemConfigApi = async (params: IAddConfigBo) => {
  const result = await request.post({
    url: `/system/config`,
    params,
  });
  return result;
};

export const editSystemConfigApi = async (params: IEditConfigBo) => {
  const result = await request.put({
    url: `/system/config`,
    params,
  });
  return result;
};

/**
 * 刷新参数缓存
 */
export const refreshSystemConfigCacheApi = async () => {
  const result = await request.delete({
    url: '/system/config/refreshCache',
  });
  return result;
};

/**
 * 获取参数详细信息
 * @param configId
 * @returns
 */
export const getSystemConfigDetailsInfoApi = async (configId: number) => {
  const result = await request.get({
    url: `/system/config/${configId}`,
  });
  return result;
};
