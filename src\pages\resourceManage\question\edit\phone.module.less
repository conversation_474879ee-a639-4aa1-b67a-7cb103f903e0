.app-title {
  font-size: 16px;
  text-align: center;
  font-weight: bold;
  padding-bottom: 10px;
}
.app {
  width: 337.2px;
  height: 730.8px;
  background-image: url('assets/image/iPhone13.png');
  top: 0;
  left: 0;
  background-size: 100% 100%;
  position: relative;
}

@bg-color: #f5f5f5;
@text-color: #333;
@primary-color: #1e90ff;
@border-color: #ccc;
@font-family: 'Arial', sans-serif;

.exercise-container {
  width: 297px;
  margin: 0 auto;
  padding: 44px 0 10px 0;
  font-family: @font-family;
  .question-container {
    height: 546px;
    overflow-y: scroll;
  }
  ::-webkit-scrollbar {
    display: none;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    margin-bottom: 20px;

    .back-button {
      font-size: 18px;
      text-decoration: none;
      color: #c9c9c9;
    }

    .title {
      flex: 1;
      text-align: center;
    }
  }

  .question {
    p {
      margin: 5px 0;
    }
    .question-progress {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: bold;
    }

    .question-text {
      font-size: 16px;
      img {
        max-width: 296px;
      }
    }

    .question-source {
      font-size: 12px;
      color: @text-color;
      text-align: right;
    }
  }

  .options {
    margin-top: 20px;

    .option {
      padding: 10px;
      border: 1px solid #ccc;
      border-radius: 5px;
      margin: 5px 0;
      cursor: pointer;
      display: flex;
      align-items: flex-start;
      font-size: 14px;
      gap: 5px;

      &:hover {
        background-color: @primary-color;
        color: #fff;
      }
    }
  }

  .explanation {
    margin-top: 20px;
    padding: 10px;
    border-radius: 5px;

    .correct-answer {
      font-weight: bold;
    }

    .answer-analysis {
      font-size: 14px;
      color: @text-color;
    }
  }

  .footer {
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-around;
    position: absolute;
    bottom: 54px;
    width: 297px;
    padding-top: 10px;

    .collect-button,
    .settings-button {
      background: none;
      border: none;
      font-size: 16px;
      cursor: pointer;
      color: @primary-color;
    }

    .collect-button {
      &:before {
        margin-right: 5px;
      }
    }

    .settings-button {
      &:before {
        margin-right: 5px;
      }
    }
  }
}
