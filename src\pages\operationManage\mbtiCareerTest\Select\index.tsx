import React, { useState, memo, useEffect, useRef } from 'react';
import { Button, TableProps, Dialog, type DialogProps } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';

import { getMbtiList, selectMbtiAdminExam } from 'modules/operationManage/mbti';
import { Tables, Search } from 'components';
import RelatedMajor from './relatedMajor';
import Style from './index.module.less';
import { IRef } from 'components/Form';

export const SelectTable: React.FC<''> = () => {
  const dispatch = useAppDispatch();
  const [visible, setVisible] = useState(false);
  const [type, setType] = useState('');
  const [rowData, setRowData] = useState({});
  const [selectedRowKeys] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);

  const { active, activeName, loading, tablist, pageNum, pageSize, total } = useAppSelector(selectMbtiAdminExam);
  const searchParams = { pageNum, pageSize };
  const onClickCloseBtn: DialogProps['onCloseBtnClick'] = () => {
    setVisible(false);
  };

  useEffect(() => {
    if (active) {
      searchRef.current?.resetForm();
    }
  }, [active]);

  const columns: TableProps['columns'] = [
    {
      colKey: 'serial-number',
      width: 80,
      title: '序号',
    },
    {
      title: '人格类型',
      align: 'center',
      colKey: 'name',
      cell({ row }) {
        return (
          <>
            {row.name} - {row.type}
          </>
        );
      },
    },
    {
      align: 'center',
      fixed: 'right',
      colKey: 'op',
      title: '操作',
      width: 120,
      cell({ row }) {
        return (
          <>
            <Button
              theme='primary'
              variant='text'
              onClick={() => {
                setVisible(true);
                setType('add');
                setRowData(row);
              }}
            >
              关联专业
            </Button>
          </>
        );
      },
    },
  ];

  const formList = {
    method: getMbtiList,
    params: { pageNum, pageSize },
    list: [
      {
        type: 'input',
        label: '人格类型',
        field: 'name',
      },
    ],
  };

  return (
    <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getMbtiList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '人格类型',
            field: 'name',
          },
        ]}
      />

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: tablist,
          loading,
          rowKey: 'examQuestionId',
          selectedRowKeys,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getMbtiList}
        params={searchParams}
      />

      {visible && (
        <Dialog
          className={Style.dialog}
          header={`${activeName} - ${rowData.name} - ${rowData.type}`}
          visible={visible}
          onCloseBtnClick={onClickCloseBtn}
          width={800}
          confirmBtn={null}
          cancelBtn={null}
          onClose={() => {
            setVisible(false);
          }}
        >
          <RelatedMajor
            visibleProps={rowData}
            cancellation={() => setVisible(false)}
            visible={visible}
            examId={active}
          ></RelatedMajor>
        </Dialog>
      )}
    </div>
  );
};

export default memo(SelectTable);
