import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Table, Input, MessagePlugin, Link, DatePicker, <PERSON><PERSON>, Popconfirm } from 'tdesign-react';
import type { TableProps } from 'tdesign-react';

import { useAppDispatch } from 'modules/store';
import { addReportData, getSchoolExamList, delReportData, addReportSpecialData, delReportSpecialData, getEducationalReportedList } from 'modules/directory';

const classStyles = `
<style>
.t-table-demo__editable-row .table-operations > .t-link {
  padding-right: 20px;
  line-height: 22px;
  height: 22px;
}
.t-table-demo__editable-row .t-demo-col__datepicker .t-date-picker {
  width: 120px;
}
</style>
`;

export default function EditableRowTable({ list, educationalExamId, type }: any) {
  const tableRef = useRef(null);
  const [data, setData] = useState([...list]);
  const [editableRowKeys, setEditableRowKeys] = useState<number[]>([]);
  const dispatch = useAppDispatch();

  useEffect(() => {
    setData([...list]);
  }, [list]);
  // 保存变化过的行信息
  const editMap = {};

  const onEdit = (e: any) => {
    const { id } = e.currentTarget.dataset;
    if (!editableRowKeys.includes(id)) {
      setEditableRowKeys([Number(id)]);
    }
  };

  // 更新 editableRowKeys
  const updateEditState = (id: string) => {
    const index = editableRowKeys.findIndex((t) => t === id);
    editableRowKeys.splice(index, 1);
    setEditableRowKeys([...editableRowKeys]);
  };

  const onCancel = (e: any) => {
    const { id } = e.currentTarget.dataset;

    // 判断是否是新添加的数据（有特定前缀）
    const isNewRow = (id) => id.toString().startsWith('new_');

    if (isNewRow(id)) {
      setData((prevData) => prevData.filter((item) => item.educationalReportedDataId !== id));
    }

    updateEditState(id);
    tableRef.current.clearValidateData();
  };

  const submitTo = async (params) => {
    if (type === 1) {
      await dispatch(addReportData(params));
      await dispatch(getSchoolExamList(educationalExamId));
    } else {
      await dispatch(addReportSpecialData(params));
      await dispatch(getEducationalReportedList(educationalExamId));
    }
  };

  const onSave = (e) => {
    const { id } = e.currentTarget.dataset;

    const validateRowId = id.startsWith('new_') ? id : parseInt(id, 10);

    tableRef.current.validateRowData(validateRowId).then(async (params: any) => {
      if (params.result.length !== 0) return;
      if (params.trigger === 'parent' && params.result.length === 0 && validateRowId !== undefined) {
        const current = editMap[id];

        if (current) {
          const ID = !!id.startsWith('new_');
          if (ID) {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { educationalReportedDataId, ...rest } = current.editedRow;
            await submitTo({ ...rest, educationalExamId });
          } else {
            await submitTo({ ...current.editedRow, educationalExamId });
          }
          MessagePlugin.success('保存成功');
        }
        updateEditState(id);
      }
    });
  };

  // 行数据编辑时触发，返回最新输入结果
  const onRowEdit: TableProps['onRowEdit'] = (params) => {
    const { row, col, value } = params;
    const oldRowData = editMap[row.educationalReportedDataId]?.editedRow || row;
    const editedRow = { ...oldRowData, [col.colKey]: value };
    editMap[row.educationalReportedDataId] = {
      ...params,
      editedRow,
    };
  };
  const onDelete = async (row) => {
    if (type === 1) {
      await dispatch(delReportData(row.educationalReportedDataId));
      setTimeout(async () => {
        await dispatch(getSchoolExamList(educationalExamId));
      }, 1000)
    } else {
      await dispatch(delReportSpecialData(row.educationalReportedDataId));
      setTimeout(async () => {
        await dispatch(getEducationalReportedList(educationalExamId));
      }, 1000)
    }
  };

  useEffect(() => {
    // 添加示例代码所需样式
    document.head.insertAdjacentHTML('beforeend', classStyles);
  }, []);
  let columns = useMemo<TableProps['columns']>(
    () => [
      {
        title: '年',
        colKey: 'year',
        align: 'left',
        width: 220,
        edit: {
          component: DatePicker,
          props: {
            autofocus: true,
            mode: 'year',
          },
          rules: [{ required: true, message: '不能为空' }],
          showEditIcon: false,
        },
      },
      {
        title: '报考人数',
        colKey: 'applyingNum',
        edit: {
          component: Input,
          showEditIcon: false,
          rules: [
            { required: true, message: '报考人数不能为空' },
            { pattern: /^[1-9]\d{0,4}$/, message: '输入正整数且长度1-5之间' },
          ],
        },
      },
      {
        title: '录取人数',
        colKey: 'admissionsNum',
        edit: {
          component: Input,
          showEditIcon: false,
          rules: [
            { required: true, message: '录取人数不能为空' },
            { pattern: /^[1-9]\d{0,4}$/, message: '输入正整数且长度1-5之间' },
          ],
        },
      },
      {
        title: '普高生分数线',
        colKey: 'fractionBar',
        edit: {
          component: Input,
          showEditIcon: false,
          rules: [
            { required: true, message: '普高生分数线不能为空' },
            { pattern: /^[1-9]\d{0,3}$/, message: '输入正整数且长度1-4之间' },
          ],
        },
      },
      {
        title: '操作栏',
        colKey: 'operate',
        width: 140,
        cell: ({ row, row: { educationalReportedDataId: id } }) => {
          const editable = editableRowKeys.includes(id);
          return (
            <div className='table-operations'>
              {!editable && (
                <>
                  <Link theme='primary' hover='color' data-id={id} onClick={onEdit}>
                    编辑
                  </Link>
                  <Popconfirm
                    content='确定要删除该数据吗？'
                    theme='danger'
                    onConfirm={() => {
                      onDelete(row);
                    }}
                  >
                    <Link theme='danger' hover='color' data-id={id}>
                      删除
                    </Link>
                  </Popconfirm>
                </>
              )}
              {editable && (
                <Link theme='primary' hover='color' data-id={id} onClick={onSave}>
                  保存
                </Link>
              )}
              {editable && (
                <Link theme='primary' hover='color' data-id={id} onClick={onCancel}>
                  取消
                </Link>
              )}
            </div>
          );
        },
      },
    ],
    [data, editableRowKeys],
  );
  if (type === 1) {
    columns[3] =
    {
      title: '普高生分数线',
      colKey: 'fractionBar',
      edit: {
        component: Input,
        showEditIcon: false,
        rules: [
          { required: true, message: '普高生分数线不能为空' },
          { pattern: /^[1-9]\d{0,3}$/, message: '输入正整数且长度1-4之间' },
        ],
      },
    }
  } else {
    columns[3] = {
      title: '三校生分数线',
      colKey: 'fractionBar',
      edit: {
        component: Input,
        showEditIcon: false,
        rules: [
          { required: true, message: '三校生分数线不能为空' },
          { pattern: /^[1-9]\d{0,3}$/, message: '输入正整数且长度1-4之间' },
        ],
      },
    }
  }


  return (
    <div className='t-table-demo__editable-row'>
      <Button
        theme='primary'
        variant='outline'
        onClick={() => {
          const newRow = {
            educationalReportedDataId: `new_${Date.now()}`, // 假设ID是时间戳，实际应根据实际情况生成
            year: '',
            applyingNum: '',
            admissionsNum: '',
            fractionBar: '',
          };
          setData([newRow, ...data]);
          setEditableRowKeys([newRow.educationalReportedDataId, ...editableRowKeys]);
        }}
      >
        添 加
      </Button>
      <Table
        ref={tableRef}
        rowKey='educationalReportedDataId'
        columns={columns}
        data={data}
        editableRowKeys={editableRowKeys}
        onRowEdit={onRowEdit}
        table-layout='auto'
        bordered
        lazyLoad
      />
    </div>
  );
}

EditableRowTable.displayName = 'EditableRowTable';
