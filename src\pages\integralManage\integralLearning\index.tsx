import React, { useState, memo, useEffect, useRef } from 'react';
import { But<PERSON>, Row, Col, TableProps } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { downLoad } from 'utils/tool';
import {
  selectPointsManagement,
  getIntegralReceiptAndPaymentList,
  getIntegralReceiptAndPaymentTypeList,
} from 'modules/pointsManagement';
import { Tables, Search } from 'components';
import { IRef } from 'components/Form';

export const SelectTable = () => {
  const dispatch = useAppDispatch();
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const { loading, list, pageNum, pageSize, total, integralTypeList } = useAppSelector(selectPointsManagement);
  const searchParams = { pageNum, pageSize, integralType: 1 };

  useEffect(() => {
    dispatch(getIntegralReceiptAndPaymentList(searchParams));
    dispatch(getIntegralReceiptAndPaymentTypeList(''));
  }, [dispatch]);

  const sourceFormat = (value) => {
    let label = '';
    switch (value) {
      case 1:
        label = '自由练习';
        break;
      case 2:
        label = '模拟考试';
        break;
      case 3:
        label = '积分商城';
        break;
      case 4:
        label = '连续签到';
        break;
      case 5:
        label = '邀请新人';
        break;
      case 6:
        label = '首次注册';
        break;
      case 7:
        label = '兑换';
        break;
      default:
        label = '';
    }
    return label;
  };

  const columns: TableProps['columns'] = [
    { colKey: 'serial-number', width: 80, title: '序号' },
    {
      title: '用户昵称',
      fixed: 'left',
      colKey: 'nickName',
    },
    {
      title: '用户电话',
      colKey: 'phoneNumber',
    },
    {
      title: '收入方式',
      colKey: 'integralSource',
      cell({ row }) {
        return <div>{sourceFormat(row.integralSource)}</div>;
      },
    },
    {
      title: '收入积分',
      colKey: 'integralValue',
      cell({ row }) {
        return <div> +{row.integralValue}</div>;
      },
    },
    {
      title: '收入时间',
      colKey: 'updateTime',
    },
  ];

  return (
    <>
      <Search
        ref={searchRef}
        method={getIntegralReceiptAndPaymentList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '用户电话',
            field: 'phoneNumber',
          },
          {
            type: 'select',
            label: '收入方式',
            field: 'integralSource',
            valueField: 'code',
            nameField: 'description',
            options: integralTypeList,
          },
          {
            type: 'datePicker',
            label: '收入时间',
            field: 'timeArr',
            isTime: true,
          },
        ]}
      />
      <div className='tabs-content' style={{ margin: 20 }}>
        <Row gutter={8} align='middle'>
          <Col>
            <Button
              variant='outline'
              theme='primary'
              onClick={() => {
                downLoad(
                  '/integral/exportIntegralReceiptAndPaymentList',
                  {
                    ...searchParams,
                    pageNum: undefined,
                    pageSize: undefined,
                  },
                  '收入明细',
                );
              }}
            >
              导出表格
            </Button>
          </Col>
        </Row>
      </div>
      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list,
          loading,
          rowKey: 'index',
          selectedRowKeys,
          onSelectChange,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getIntegralReceiptAndPaymentList}
        params={searchParams}
      />

      {/* {ClearDialog({
        visibleProp: visible,
        rowProp: row,
        typeProp: type,
        onCancel: (val) => setVisible(val),
      })}
      {RuleDialog({
        visibleProp: ruleVisible,
        rowProp: row,
        typeProp: type,
        onCancel: (val) => setRuleVisible(val),
      })} */}
    </>
  );
};

const selectPage: React.FC = () => (
  <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
    <SelectTable />
  </div>
);

export default memo(selectPage);
