.t-list-item__meta-description {
  margin-right: 0 !important;
}

// p {
//   margin: 0 !important;
// }

.content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-14) !important;
  color: var(--text-color-secondary) !important;
}

.menuList {
  :global {
    .t-default-menu {
      width: 100% !important;
    }

    .t-default-menu__inner .t-menu {
      padding: 0;
    }

    .t-menu__content {
      width: 100%;
    }

    .t-list-item {
      padding: 0 10px;
    }

    .t-dialog__body {
      overflow: inherit;
    }
  }
}

.form {
  :global {
    .t-form__controls-content {
      justify-content: flex-end;
    }
  }
}

.dialog_body {
  :global {
    .t-dialog__body {
      overflow: visible;
    }

  }
}

.icon {
  :global {
    .t-icon {
      width: 16px !important;
      height: 16px !important;
    }
  }
}