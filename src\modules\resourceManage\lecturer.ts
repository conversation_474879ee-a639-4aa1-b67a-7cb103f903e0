import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getResourceLecturerListApi,
  deleteResourceLecturerApi,
  addResourceLecturerApi,
  getResourceLecturerInfoApi,
  editResourceLecturerApi,
} from 'api';
import { IAddResourceLecturerBo, IEditResourceLecturerBo, IGetResourceLecturerListBo } from 'types/resourceManage';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'resourceManage/lecturer';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  deleteLecturerLoading: boolean;
  lecturerInfoLoading: boolean;
  addEditLecturerLoading: boolean;
  lecturerInfo: any;
}

const initialState: IInitialState = {
  loading: false,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  deleteLecturerLoading: false,
  lecturerInfoLoading: false,
  addEditLecturerLoading: false,
  lecturerInfo: {},
};

export const getLecturerList = createAsyncThunk(
  `${namespace}/getLecturerList`,
  async (bo: IGetResourceLecturerListBo) => {
    const { data } = await getResourceLecturerListApi(bo);

    return {
      list: data.rows,
      total: data.total,
      pageNum: bo.pageNum,
      pageSize: bo.pageSize,
    };
  },
);

export const getLecturerInfo = createAsyncThunk(`${namespace}/getLecturerInfo`, async (id: number) => {
  const { data } = await getResourceLecturerInfoApi(id);

  return data;
});

export const addLecturer = createAsyncThunk(`${namespace}/addLecturer`, async (bo: IAddResourceLecturerBo) => {
  await addResourceLecturerApi(bo);
});

export const editLecturer = createAsyncThunk(`${namespace}/editLecturer`, async (bo: IEditResourceLecturerBo) => {
  await editResourceLecturerApi(bo);
});

export const deleteLecturer = createAsyncThunk(`${namespace}/deleteLecturer`, async (id: number) => {
  await deleteResourceLecturerApi(id);
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getLecturerList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getLecturerList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getLecturerList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getLecturerInfo.pending, (state) => {
        state.lecturerInfoLoading = true;
      })
      .addCase(getLecturerInfo.fulfilled, (state, action) => {
        state.lecturerInfoLoading = false;
        state.lecturerInfo = action.payload;
      })
      .addCase(getLecturerInfo.rejected, (state) => {
        state.lecturerInfoLoading = false;
      })

      .addCase(addLecturer.pending, (state) => {
        state.addEditLecturerLoading = true;
      })
      .addCase(addLecturer.fulfilled, (state) => {
        state.addEditLecturerLoading = false;
        MessagePlugin.success('添加成功');
      })
      .addCase(addLecturer.rejected, (state) => {
        state.addEditLecturerLoading = false;
      })

      .addCase(editLecturer.pending, (state) => {
        state.addEditLecturerLoading = true;
      })
      .addCase(editLecturer.fulfilled, (state) => {
        state.addEditLecturerLoading = false;
        MessagePlugin.success('编辑成功');
      })
      .addCase(editLecturer.rejected, (state) => {
        state.addEditLecturerLoading = false;
      })

      .addCase(deleteLecturer.pending, (state) => {
        state.deleteLecturerLoading = true;
      })
      .addCase(deleteLecturer.fulfilled, (state) => {
        state.deleteLecturerLoading = false;
        MessagePlugin.success('删除成功');
      })
      .addCase(deleteLecturer.rejected, (state) => {
        state.deleteLecturerLoading = false;
      });
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectResourceManageLecturer = (state: RootState) => state.resourceManageLecturer;

export default listBaseSlice.reducer;
