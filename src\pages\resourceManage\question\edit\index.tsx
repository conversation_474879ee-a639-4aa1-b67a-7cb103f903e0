import React, { memo, useRef, useEffect, useState } from 'react';
import { Form, Row, Col, Input, Button, MessagePlugin, Loading } from 'tdesign-react';
import type { FormRules, Data, CustomValidator } from 'tdesign-react';
import classnames from 'classnames';
import { SubmitContext, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { useLocation, useNavigate } from 'react-router-dom';
import QuizCard from './phone';
import { Back, Editor } from 'components';
import Option from './option';
import { processStringWithFormula, addSpansBackToFormulas, removeStyles } from 'utils/LaTeX';
import { STEM_MENU_CONFIG } from '../consts';

import {
  setStem,
  setOption,
  setAnswer,
  setQuestionAnalysis,
  setQuestionProvenance,
  selectResourceManageQuestionSlice,
  addOrUpdateQuestion,
} from 'modules/resourceManage/question';

const { FormItem } = Form;

interface Params {
  questionId: number;
  answerId: number;
  answer: string;
  subjectId: number;
  options: Option[];
  stem: string;
  [key: string]: any;
}

interface Option {
  optionContent: string;
  [key: string]: any;
}

type ResetType = 'empty' | 'initial';

export default memo(() => {
  const navigation = useNavigate();
  const location = useLocation();
  const { row, active } = location.state;
  const [resetType] = useState<ResetType>('initial');
  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>();
  const stemEditorRef = useRef(null);
  const optionRef = useRef(null);
  const analysisEditorRef = useRef(null);
  const { formLoading, answer, answerId, optionBoList } = useAppSelector(selectResourceManageQuestionSlice);

  // 表单状态
  const [form] = Form.useForm();

  const INITIAL_DATA = JSON.parse(JSON.stringify(row));

  useEffect(() => {
    dispatch(
      setAnswer({
        oid: row.answerId,
        opt: row.answer,
        optionContent: '',
        qid: '',
      }),
    );

    dispatch(setStem(addSpansBackToFormulas(row.stem)));
    dispatch(setOption(row.optionVoList));
    dispatch(setQuestionAnalysis(row.analysis));
    dispatch(setQuestionProvenance(row.provenance));
  }, [location.state]);

  const onSubmit = async (e: SubmitContext) => {
    if (e.validateResult === true) {
      if (answer === null || answer.length === 0) return MessagePlugin.error('请选择正确答案');

      const options = optionBoList.map((_i: any) => ({
        ..._i,
        optionContent: removeStyles(processStringWithFormula(_i.optionContent)),
      }));

      const formData = formRef.current?.getFieldsValue?.(true);

      const copyFormData = { ...formData };

      const params: Params = {
        questionProvenance: copyFormData.provenance,
        questionAnalysis: processStringWithFormula(copyFormData.analysis),
        stem: processStringWithFormula(copyFormData.stem),
        questionId: row.questionId,
        answerId,
        answer,
        subjectId: active,
        optionBoList: options,
      };

      await dispatch(addOrUpdateQuestion(params));
      MessagePlugin.success(`${row.questionId ? '修改' : '添加'}成功`);
      navigation('/resource/question', { state: { subjectId: active } });
    }

    return false;
  };

  const handleChangeStem = (res: string) => {
    dispatch(setStem(res));
  };

  const handleChange = (res: string) => {
    dispatch(setQuestionAnalysis(res));
  };

  const handleChangeOption = (opt: Option[]) => {
    form.setFieldsValue({ optionVoList: opt });
  };

  const handleClearEditor = (ref: any, html: any = '') => {
    // 调用子组件暴露的 clearContent 方法
    if (ref.current) {
      ref.current.clearContent(html);
    }
  };

  // 手动重置表单及所有字段的方法
  const handleReset = () => {
    // 1. 使用 Form 组件的 reset 重置表单内部字段
    formRef.current?.reset?.();

    dispatch(setStem(addSpansBackToFormulas(INITIAL_DATA.stem)));
    if (!row.questionId) {
      handleClearEditor(stemEditorRef);
      handleClearEditor(optionRef);
      handleClearEditor(analysisEditorRef);
      // 重置答案
      dispatch(setAnswer({ oid: '', opt: '', optionContent: '', qid: '' }));
    } else {
      handleClearEditor(stemEditorRef, INITIAL_DATA.stem);
      handleClearEditor(optionRef, INITIAL_DATA.optionVoList);
      handleClearEditor(analysisEditorRef, INITIAL_DATA.analysis);
    }

    dispatch(setOption(INITIAL_DATA.optionVoList));
    dispatch(setQuestionAnalysis(addSpansBackToFormulas(INITIAL_DATA.analysis)));

    // 重置出处
    dispatch(setQuestionProvenance(INITIAL_DATA.provenance));
  };

  // 自定义校验器
  const optionVoListValidator: CustomValidator = (val: any) => {
    if (!val || !Array.isArray(val)) return { result: false, message: '选项必填', type: 'error' };

    const isNotValid = val.some((item) => item.optionContent === '');

    if (isNotValid) return { result: false, message: '选项必须填写完整', type: 'warning' };

    return { result: true };
  };

  const rules: FormRules<Data> = {
    stem: [{ required: true, message: '题干必填', type: 'error' }],
    optionVoList: [{ validator: optionVoListValidator }],
    analysis: [{ required: true, message: '解析必填', type: 'error' }],
  };

  return (
    <>
      <Back path='/resource/question' header='题库管理' params={{ subjectId: active }}></Back>
      <div className={`${classnames(CommonStyle.pageWithColor)}`}>
        <div className={Style['page-content']}>
          <div className={`${Style.formContainer}`}>
            <Loading loading={formLoading} showOverlay>
              <Form
                form={form}
                resetType={resetType}
                rules={rules}
                ref={formRef}
                initialData={INITIAL_DATA}
                onSubmit={onSubmit}
                labelWidth={100}
                labelAlign='top'
              >
                <Row gutter={[32, 44]}>
                  <Col span={12} className={Style.dateCol}>
                    <FormItem label='题干' name='stem'>
                      <Editor
                        ref={stemEditorRef}
                        border
                        onChange={(res: string) => handleChangeStem(res)}
                        text={addSpansBackToFormulas(row.stem)}
                        toolbar={STEM_MENU_CONFIG}
                        uploadImgConfig={{ maxFileSize: 2 }}
                        maxLength={800}
                        editorStyle={{ height: '200px', width: '100%' }}
                      ></Editor>
                    </FormItem>
                  </Col>
                  <Col span={20} className={Style.dateCol}>
                    <FormItem label='选项配置' name='optionVoList'>
                      <Option ref={optionRef} options={row} onChangeOption={handleChangeOption} />
                    </FormItem>
                  </Col>
                  <Col span={12} className={Style.dateCol}>
                    <FormItem label='解析' name='analysis'>
                      <Editor
                        ref={analysisEditorRef}
                        border
                        onChange={(res: string) => handleChange(res)}
                        text={addSpansBackToFormulas(row.analysis)}
                        toolbar={STEM_MENU_CONFIG}
                        uploadImgConfig={{ maxFileSize: 2 }}
                        maxLength={800}
                        editorStyle={{ height: '400px', width: '100%' }}
                      ></Editor>
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem label='题目出处' name='provenance'>
                      <Input
                        onChange={(val: string) => dispatch(setQuestionProvenance(val))}
                        placeholder='请输入题目出处'
                      />
                    </FormItem>
                  </Col>
                  <Col span={12}>
                    <FormItem>
                      <Button type='submit' theme='primary'>
                        提交
                      </Button>
                      <Button type='button' style={{ marginLeft: 12 }} onClick={handleReset}>
                        重置
                      </Button>
                    </FormItem>
                  </Col>
                </Row>
              </Form>
            </Loading>
          </div>
          <div>
            <QuizCard></QuizCard>
          </div>
        </div>
      </div>
    </>
  );
});
