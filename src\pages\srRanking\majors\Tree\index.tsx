import React, { memo, useEffect } from 'react';
import { Loading, Menu, Empty } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectSRRankingMajors, getTreeList, setActive } from 'modules/srRanking/majors';
import Style from './index.module.less';
import './index.less';

const { MenuItem } = Menu;

const Header = () => <h3 className={Style.header}>所属考试</h3>;

const SRRankingMajorsTree: React.FC = () => {
  const dispatch = useAppDispatch();
  const { treeLoading, treeList, active } = useAppSelector(selectSRRankingMajors);

  useEffect(() => {
    dispatch(getTreeList());
  }, []);

  return (
    <div className={Style.container}>
      <Loading size='small' loading={treeLoading}>
        <Menu className='srRankingMajorsTree' value={active} onChange={(v) => dispatch(setActive(v))} logo={<Header />}>
          {treeList &&
            treeList.map(({ examId, examName }) => (
              <MenuItem value={examId} key={examId}>
                <span>{examName}</span>
              </MenuItem>
            ))}
          {treeList.length === 0 && <Empty />}
        </Menu>
      </Loading>
    </div>
  );
};

export default memo(SRRankingMajorsTree);
