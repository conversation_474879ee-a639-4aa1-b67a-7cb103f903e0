import { IAddNoticeBo, IEditNoticeBo, IGetNoticeListBo } from 'types/system';
import request from 'utils/request/index';

/**
 * 查询通知列表
 * @returns
 */
export const getSystemNoticeListApi = async (params: IGetNoticeListBo) => {
  const result = await request.get({
    url: '/system/notice/list',
    params,
  });
  return result;
};

/**
 * 删除通知
 * @param noticeId
 * @returns
 */
export const delSystemNoticeApi = async (noticeId: number | string) => {
  const result = await request.delete({
    url: `/system/notice/${noticeId}`,
  });
  return result;
};

export const addSystemNoticeApi = async (params: IAddNoticeBo) => {
  const result = await request.post({
    url: `/system/notice`,
    params,
  });
  return result;
};

export const editSystemNoticeApi = async (params: IEditNoticeBo) => {
  const result = await request.put({
    url: `/system/notice`,
    params,
  });
  return result;
};

/**
 * 获取通知详细信息
 * @param noticeId
 * @returns
 */
export const getSystemNoticeDetailsInfoApi = async (noticeId: number) => {
  const result = await request.get({
    url: `/system/notice/${noticeId}`,
  });
  return result;
};
