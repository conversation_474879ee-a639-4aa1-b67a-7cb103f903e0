import React, { useState, memo, useEffect, useCallback, useRef } from 'react';
import {
  MessagePlugin,
  DialogPlugin,
  Row,
  Col,
  Button,
  Popconfirm,
  Dialog,
  Link,
  ImageViewer,
  Image,
  TableProps,
} from 'tdesign-react';
import Exam from './components/Exam';

import classnames from 'classnames';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { getSchoolList, selectSchool, delSchoolInfo, schoolListSort } from 'modules/directory';
import CommonStyle from 'styles/common.module.less';
import style from './index.module.less';
import { useNavigate } from 'react-router-dom';

import { BrowseIcon, MoveIcon } from 'tdesign-icons-react';
import type { ImageViewerProps, DialogProps, TableRowData } from 'tdesign-react';
import { Tables, Search, Editor } from 'components';
import { IRef } from 'components/Form';

const SchoolManagement = () => {
  const dispatch = useAppDispatch();
  const { loading, schoolList, pageNum, pageSize, total } = useAppSelector(selectSchool);
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const [visible, setVisible] = useState<boolean>(false);
  const [visibleProps, setVisibleProps] = useState<TableRowData>();

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  const navigate = useNavigate();

  const searchParams = { pageNum, pageSize };

  const fetchSchoolList = () => {
    dispatch(getSchoolList(searchParams));
  };

  useEffect(() => {
    fetchSchoolList();
  }, [dispatch]);

  const onDeleteClick = async (row: any) => {
    await dispatch(delSchoolInfo({ educationalIdList: [row.educationalId] }));
    fetchSchoolList();
  };

  const onBatchDeleteClick = async () => {
    if (selectedRowKeys.length === 0) return MessagePlugin.warning('请选择数据！');

    const confirmDia = DialogPlugin.confirm({
      header: '批量删除提示',
      body: '确定批量删除该数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        await dispatch(delSchoolInfo({ educationalIdList: selectedRowKeys as number[] }));

        handleResetSelection();

        fetchSchoolList();

        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });

    return false;
  };

  const onClickCloseBtn: DialogProps['onCloseBtnClick'] = () => {
    setVisible(false);
  };

  const onDragSort = async (val: any) => {
    try {
      await dispatch(
        schoolListSort({
          position: pageNum === 1 ? val.targetIndex + 1 : (pageNum - 1) * pageSize + (val.targetIndex + 1),
          id: val.current.educationalId,
        }),
      );
      fetchSchoolList();
      return true;
    } catch (error) {
      throw new Error('排序失败');
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'drag',
      title: '排序',
      cell: () => <MoveIcon />,
      width: 46,
    },
    {
      colKey: 'row-select',
      fixed: 'left',
      type: 'multiple',
      width: 50,
    },
    { colKey: 'serial-number', width: 60, title: '序号' },
    {
      width: 200,
      colKey: 'educationalName',
      title: '院校名称',
    },
    {
      width: 150,
      colKey: 'logoUrl',
      title: 'Logo',
      cell: ({ row }) => {
        const imgArr = [row.logoUrl];
        return (
          <>
            {imgArr.map((imgSrc, index) => {
              const trigger: ImageViewerProps['trigger'] = ({ open }) => {
                const mask = row.logoUrl && (
                  <div
                    style={{
                      background: 'rgba(0,0,0,.6)',
                      color: '#fff',
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onClick={open}
                  >
                    <span>
                      <BrowseIcon size='16px' name={'browse'} />
                    </span>
                  </div>
                );

                return (
                  <Image
                    alt={'test'}
                    src={imgSrc}
                    overlayContent={mask}
                    overlayTrigger='hover'
                    fit='contain'
                    style={{
                      width: 50,
                      height: 50,
                      border: '4px solid var(--td-bg-color-secondarycontainer)',
                      borderRadius: 'var(--td-radius-medium)',
                      backgroundColor: '#fff',
                    }}
                  />
                );
              };
              return <ImageViewer key={imgSrc} trigger={trigger} images={imgArr} defaultIndex={index} />;
            })}
          </>
        );
      },
    },
    {
      width: 300,
      colKey: 'educationalIntroduce',
      title: '院校介绍',
      cell: ({ row: { educationalIntroduce } }) => (
        <Editor readOnly text={educationalIntroduce} editorStyle={{ height: '50px' }}></Editor>
      ),
    },
    {
      width: 200,
      colKey: 'createNickName',
      title: '创建人',
    },
    {
      width: 200,
      colKey: 'createTime',
      title: '创建时间',
    },
    {
      width: 200,
      colKey: 'updateNickName',
      title: '修改人',
    },
    {
      width: 200,
      colKey: 'updateTime',
      title: '修改时间',
    },
    {
      fixed: 'right',
      width: 180,
      colKey: 'op',
      title: '操作',
      cell: ({ row }) => (
        <>
          <Button
            theme='primary'
            variant='text'
            onClick={() => navigate(`/directory/school/add`, { state: { type: 'edit', row } })}
          >
            编辑
          </Button>
          <Button
            theme='primary'
            variant='text'
            onClick={() => {
              setVisible(true);
              setVisibleProps(row);
            }}
          >
            所属考试
          </Button>
          <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => onDeleteClick(row)}>
            <Link theme='danger' hover='color'>
              删除
            </Link>
          </Popconfirm>
        </>
      ),
    },
  ];

  return (
    <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getSchoolList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '院校名称',
            field: 'educationalName',
          },
        ]}
      />
      <Row gutter={8} align='middle' className={style.toolBar}>
        <Col>
          <Button
            variant='outline'
            theme='primary'
            onClick={() => navigate(`/directory/school/add`, { state: { type: 'add' } })}
          >
            新增院校
          </Button>
        </Col>
        <Col>
          <Button variant='outline' theme='danger' onClick={onBatchDeleteClick}>
            批量删除
          </Button>
        </Col>
      </Row>
      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: schoolList,
          loading,
          rowKey: 'educationalId',
          selectedRowKeys,
          onSelectChange,
          dragSort: 'row-handler',
          onDragSort,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getSchoolList}
        params={searchParams}
      />
      {visible && (
        <Dialog
          header={`${visibleProps?.educationalName} — 编辑关联信息`}
          visible={visible}
          cancelBtn={null}
          footer={false}
          width={1000}
          onCloseBtnClick={onClickCloseBtn}
        >
          <span>
            请关联此院校参加的考试类目并尽量填写近5年的报录人数与分数线，只有已关联并开启开关的院校会回显至小程序端
          </span>
          <Exam visibleProps={visibleProps}></Exam>
        </Dialog>
      )}
    </div>
  );
};

export default memo(SchoolManagement);
