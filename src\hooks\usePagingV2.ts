import { useAppDispatch } from 'modules/store';

export const usePagingV2 = (method, params, { pageNum, pageSize, total }) => {
  const dispatch = useAppDispatch();

  const pagination = {
    current: pageNum,
    pageSize,
    total,
    showJumper: true,
    // onChange: (pageInfo) => {
    //   dispatch(
    //     method({
    //       ...params,
    //       pageSize: pageInfo.pageSize,
    //       pageNum: pageInfo.current,
    //     }),
    //   );
    // },
    onCurrentChange(_, pageInfo) {
      dispatch(
        method({
          ...params,
          pageSize: pageInfo.pageSize,
          pageNum: pageInfo.current,
        }),
      );
    },
    onPageSizeChange(size) {
      dispatch(
        method({
          ...params,
          pageSize: size,
          pageNum: 1,
        }),
      );
    },
  };

  return pagination;
};
