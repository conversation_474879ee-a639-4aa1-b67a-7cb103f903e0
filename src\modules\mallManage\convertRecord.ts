import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import { getConvertRecordListApi } from 'api';
import { IGetConvertRecordListBo, IGetGoodsInfoBo } from 'types/mallManage';

const namespace = 'mallManage/convertRecord';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
}

const initialState: IInitialState = {
  loading: false,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export const getConvertRecordList = createAsyncThunk(
  `${namespace}/getConvertRecordList`,
  async (bo: IGetConvertRecordListBo) => {
    const { data } = await getConvertRecordListApi(bo);

    return {
      list: data.rows,
      total: data.total,
      pageNum: bo.pageNum,
      pageSize: bo.pageSize,
    };
  },
)

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getConvertRecordList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getConvertRecordList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getConvertRecordList.rejected, (state) => {
        state.loading = false;
      })
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectMallManageConvertRecord = (state: RootState) => state.mallManageConvertRecord;

export default listBaseSlice.reducer;
