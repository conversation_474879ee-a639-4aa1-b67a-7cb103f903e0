import { RootState } from '../store';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import {
  getIntegralListApi,
  clearIntegralCaptchaImageApi,
  clearIntegralApi,
  getIntegralRuleApi,
  addOrUpdateIntegralRuleApi,
  getIntegralLogListApi,
  updateContinuousSignInApiApi,
  getIntegralReceiptAndPaymentListApi,
  getIntegralReceiptAndPaymentTypeListApi
} from 'api';
import type {
  IGetIntegralListApi,
  IClearIntegralApi,
  IAddOrUpdateIntegralRuleApi,
  IGetIntegralLogListApi,
  IUpdateContinuousSignInApi,
  IntegralReceiptAndPaymentListApi
} from 'api';

const namespace = 'pointsManagement';

const initialState = {
  loading: true,
  imgLoading: true,
  formLoading: false,
  formDataLoading: true,
  formData: {},
  pageNum: 1,
  pageSize: 10,
  total: 0,
  list: [] as Array<any>,
  integralTypeList: [] as Array<any>,
  log: {
    pageNum: 1,
    pageSize: 10,
    total: 0,
    list: [] as Array<any>,
  },
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getIntegralList = useAsyncThunkWithStatus(`getIntegralList`, async (params) => {
  const {
    data: { rows, total },
  } = await getIntegralListApi(params as IGetIntegralListApi);
  const { pageNum, pageSize } = params as IGetIntegralListApi;
  return {
    total,
    rows,
    pageNum,
    pageSize,
  };
});
export const getIntegralReceiptAndPaymentTypeList = useAsyncThunkWithStatus(`getIntegralReceiptAndPaymentTypeList`, async (params) => {
  const {
    data: { rows, total },
  } = await getIntegralReceiptAndPaymentTypeListApi();
  return {
    total,
    rows,
  };
});

export const getIntegralReceiptAndPaymentList = useAsyncThunkWithStatus(`getIntegralReceiptAndPaymentList`, async (params) => {
  const {
    data: { rows, total },
  } = await getIntegralReceiptAndPaymentListApi(params as IntegralReceiptAndPaymentListApi);
  const { pageNum, pageSize } = params as IntegralReceiptAndPaymentListApi;
  return {
    total,
    rows,
    pageNum,
    pageSize,
  };
},
);
export const getIntegralLogList = useAsyncThunkWithStatus(`getIntegralLogList`, async (params) => {
  const {
    data: { rows, total },
  } = await getIntegralLogListApi(params as IGetIntegralLogListApi);
  const { pageNum, pageSize } = params;
  return {
    total,
    rows,
    pageNum,
    pageSize,
  };
});

export const getIntegralRule = useAsyncThunkWithStatus(`getIntegralRule`, async () => {
  const { data } = await getIntegralRuleApi();
  return data;
});

export const addOrUpdateIntegralRule = useAsyncThunkWithStatus('addOrUpdateIntegralRule', async (params) => {
  await addOrUpdateIntegralRuleApi(params as IAddOrUpdateIntegralRuleApi);
});
export const updateContinuousSignIn = useAsyncThunkWithStatus('updateContinuousSignInApi', async (params) => {
  await updateContinuousSignInApiApi(params as IUpdateContinuousSignInApi);
});

export const clearIntegralCaptchaImage = useAsyncThunkWithStatus(`clearIntegralCaptchaImage`, async () => {
  const res = await clearIntegralCaptchaImageApi();
  return res;
});

export const clearIntegral = useAsyncThunkWithStatus('clearIntegral', async (params) => {
  await clearIntegralApi(params as IClearIntegralApi);
});

const pointsManagementSliceSlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  cases: [
    {
      thunk: getIntegralList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.total = action.payload.total;
        state.list = action.payload.rows;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },

    {
      thunk: getIntegralReceiptAndPaymentList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.total = action.payload.total;
        state.list = action.payload.rows;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getIntegralLogList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.log.total = action.payload.total;
        state.log.list = action.payload.rows;
        state.log.pageNum = action.payload.pageNum;
        state.log.pageSize = action.payload.pageSize;
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: getIntegralReceiptAndPaymentTypeList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        // state.total = action.payload.total;
        state.integralTypeList = action.payload.rows;
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: clearIntegralCaptchaImage,
      pending: (state) => {
        state.imgLoading = true;
      },
      fulfilled: (state) => {
        state.imgLoading = false;
      },
      rejected: (state) => {
        state.imgLoading = false;
      },
    },

    {
      thunk: clearIntegral,
      pending: (state) => {
        state.formLoading = true;
      },
      fulfilled: (state) => {
        state.formLoading = false;
      },
      rejected: (state) => {
        state.formLoading = false;
      },
    },
    {
      thunk: getIntegralRule,
      pending: (state) => {
        state.formDataLoading = true;
      },
      fulfilled: (state, action) => {
        state.formData = action.payload;
        state.formDataLoading = false;
      },
      rejected: (state) => {
        state.formDataLoading = false;
      },
    },
    {
      thunk: addOrUpdateIntegralRule,
      pending: (state) => {
        state.formLoading = true;
      },
      fulfilled: (state) => {
        state.formLoading = false;
      },
      rejected: (state) => {
        state.formLoading = false;
      },
    },
  ],
});

export const { clearPageState } = pointsManagementSliceSlice.actions;

export const selectPointsManagement = (state: RootState) => state.pointsManagement;

export default pointsManagementSliceSlice.reducer;
