import React, { memo, useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Menu, MenuValue } from 'tdesign-react';
import router, { IRouter } from 'router';
import { resolve } from 'utils/path';
import { useAppSelector } from 'modules/store';
import { selectGlobal } from 'modules/global';
import MenuLogo from './MenuLogo';
import Style from './Menu.module.less';
import { getRouters } from 'router/getRouter';
import { findParentPaths, getActiveMenu, mergeRoutes } from 'utils/routers';
import { strIsNull } from 'utils/tool';
import { Icon } from 'tdesign-icons-react';

const { SubMenu, MenuItem, HeadMenu } = Menu;

interface IMenuProps {
  showLogo?: boolean;
  showOperation?: boolean;
}

const renderMenuItems = (menu: IRouter[], navigate: any, parentPath = '') =>
  menu &&
  menu.map((item) => {
    const { children, meta, path } = item;

    if (!meta || meta?.hidden === true) {
      // 无meta信息 或 hidden == true，路由不显示为菜单
      return null;
    }

    const { icon, title, single } = meta;
    const routerPath = resolve(parentPath, path);

    if (!children || children.length === 0) {
      return (
        <MenuItem
          key={routerPath}
          value={routerPath}
          icon={icon ? <Icon name={icon} /> : undefined}
          onClick={() => navigate(routerPath)}
        >
          {title}
        </MenuItem>
      );
    }

    if (single && children?.length > 0) {
      const firstChild = children[0];
      if (firstChild?.meta && !firstChild?.meta?.hidden) {
        const { icon, title } = meta;
        const singlePath = resolve(resolve(parentPath, path), firstChild.path);
        return (
          <MenuItem
            key={singlePath}
            value={singlePath}
            icon={icon ? <Icon name={icon} /> : undefined}
            onClick={() => navigate(singlePath)}
          >
            {title}
          </MenuItem>
        );
      }
    }

    return (
      <SubMenu key={routerPath} value={routerPath} title={title} icon={icon ? <Icon name={icon} /> : undefined}>
        {renderMenuItems(children, navigate, routerPath)}
      </SubMenu>
    );
  });

/**
 * 顶部菜单
 */
export const HeaderMenu = memo(() => {
  const globalState = useAppSelector(selectGlobal);
  const location = useLocation();
  const [active, setActive] = useState<MenuValue>(location.pathname);
  const [expands, setExpands] = useState<MenuValue[]>([]);
  const [routers, setRouters] = useState<IRouter[]>([]);
  const USER_ROUTER = getRouters();

  useEffect(() => {
    setRouters(router.concat(USER_ROUTER));
  }, [USER_ROUTER]);

  // 解决刷新不自动展开问题
  useEffect(() => {
    setExpands([...new Set([...expands, ...findParentPaths(routers, location.pathname)])]);
    const activeMenu = getActiveMenu(routers, location.pathname);
    setActive(!strIsNull(activeMenu) ? (activeMenu as MenuValue) : location.pathname);
  }, [location.pathname, routers]);

  const navigate = useNavigate();

  return (
    <HeadMenu
      expandType='popup'
      style={{ marginBottom: 20 }}
      value={active}
      theme={globalState.theme}
      onChange={(v) => setActive(v)}
      expanded={expands}
      onExpand={(values) => setExpands(values)}
    >
      {routers && renderMenuItems(routers, navigate, '')}
    </HeadMenu>
  );
});

/**
 * 左侧菜单
 */
export default memo((props: IMenuProps) => {
  const location = useLocation();
  const globalState = useAppSelector(selectGlobal);

  const { version } = globalState;
  const bottomText = globalState.collapsed ? version : `单招后台管理系统 ${version}`;
  const [active, setActive] = useState<MenuValue>();
  const [expands, setExpands] = useState<MenuValue[]>([]);
  const [routers, setRouters] = useState<IRouter[]>([]);
  const navigate = useNavigate();
  const USER_ROUTER = getRouters();

  useEffect(() => {
    setRouters(router.concat(USER_ROUTER));
  }, [router]);

  // 解决刷新不自动展开问题
  useEffect(() => {
    setExpands([...new Set([...expands, ...findParentPaths(routers, location.pathname)])]);
    const activeMenu = getActiveMenu(routers, location.pathname);
    setActive(!strIsNull(activeMenu) ? (activeMenu as MenuValue) : location.pathname);
  }, [location.pathname, routers]);

  return (
    <Menu
      width='232px'
      style={{ flexShrink: 0, height: '100%' }}
      className={Style.menuPanel2}
      value={active}
      theme={globalState.theme}
      collapsed={globalState.collapsed}
      operations={props.showOperation ? <div className={Style.menuTip}>{bottomText}</div> : undefined}
      logo={props.showLogo ? <MenuLogo collapsed={globalState.collapsed} /> : undefined}
      expanded={expands}
      onExpand={(values) => setExpands(values)}
    >
      {renderMenuItems(routers, navigate, '')}
    </Menu>
  );
});
