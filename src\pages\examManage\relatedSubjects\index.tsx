import React, { memo, useEffect, useState, useRef } from 'react';
import { Tag, Button, Popconfirm, TableProps } from 'tdesign-react';
import { MoveIcon } from 'tdesign-icons-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { TrendIcon, ETrend } from 'components/Board';
import { useLocation } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'modules/store';
import {
  selectExamManage,
  getSubjectListByQueryId,
  clearPageState,
  addExamSubject,
  IExamSubjectDragSortBo,
  examSubjectDragSort,
} from 'modules/examManage';
import { Tables, Back, Search } from 'components';
import { IRef } from 'components/Form';

export const PaymentTypeMap: {
  [key: number]: React.ReactElement;
} = {
  0: <TrendIcon trend={ETrend.down} trendNum='已关联' />,
  1: <TrendIcon trend={ETrend.up} trendNum='未关联' />,
};

export const StatusMap: {
  [key: number]: React.ReactElement;
} = {
  0: (
    <Tag theme='warning' variant='light'>
      未编辑
    </Tag>
  ),
  1: (
    <Tag theme='success' variant='light'>
      已编辑
    </Tag>
  ),
  2: (
    <Tag theme='danger' variant='light'>
      未知
    </Tag>
  ),
};

interface Theme {
  content: string;
  theme: string;
  btnContent: string;
}

const PopconfirmTable: Theme[] = [
  {
    content: '确定取消关联吗？',
    theme: 'danger',
    btnContent: '取消关联',
  },
  {
    content: '确定关联吗？',
    theme: 'default',
    btnContent: '关联',
  },
];

export default memo(() => {
  const location = useLocation();
  const dispatch = useAppDispatch();
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const pageState = useAppSelector(selectExamManage);
  const {
    subject: { loading, contractList, pageNum, pageSize, total, dragSortLoading },
  } = pageState;
  const searchParams = { pageNum, pageSize, examId: location.state };

  // const handleResetSelection = () => {
  //   if (tableRef.current) {
  //     // 调用重置选中方法
  //     tableRef.current.resetSelection();
  //   }
  // };

  const getList = () => {
    dispatch(
      getSubjectListByQueryId({
        pageNum,
        pageSize,
        subjectName: null,
        examId: location.state,
      }),
    );
  };

  const association = async (row: any) => {
    const data = {
      examId: location.state,
      subjectId: row.subjectId,
      relation: row.isRelated ? 0 : 1,
    };
    await dispatch(addExamSubject(data));
    getList();
  };

  useEffect(() => {
    getList();

    return () => {
      dispatch(clearPageState());
    };
  }, []);

  const columns: TableProps['columns'] = [
    {
      colKey: 'drag',
      title: '排序',
      cell: ({ row }) => (row.examSubjectId ? <MoveIcon /> : <></>),
      width: 46,
    },
    { colKey: 'serial-number', width: 80, title: '序号' },
    {
      align: 'left',
      width: 200,
      ellipsis: true,
      colKey: 'subjectName',
      title: '科目名称',
    },
    {
      align: 'left',
      width: 200,
      ellipsis: true,
      colKey: 'isRelated',
      title: '状态',
      cell({ row }) {
        return row.isRelated ? PaymentTypeMap[0] : PaymentTypeMap[1];
      },
    },
    {
      align: 'left',
      fixed: 'right',
      width: 180,
      colKey: 'op',
      title: '操作',
      cell({ row }) {
        const content = PopconfirmTable[row.isRelated ? 0 : 1];
        return (
          <Popconfirm
            content={content.content}
            theme={content.theme}
            onConfirm={() => {
              association(row);
            }}
          >
            <Button theme='primary' variant='text'>
              {content.btnContent}
            </Button>
          </Popconfirm>
        );
      },
    },
  ];

  const onDragSort = async (val: any) => {
    try {
      if (val.current.examSubjectId === null) return false;

      const bo: IExamSubjectDragSortBo = {
        id: val.current.examSubjectId,
        parentId: location.state,
        position: pageNum === 1 ? val.targetIndex + 1 : (pageNum - 1) * pageSize + (val.targetIndex + 1),
      };
      await dispatch(examSubjectDragSort(bo));
      return true;
    } catch (error) {
      throw new Error('排序失败');
    }
  };

  return (
    <>
      <Back path='/examManage' header='单招考试'></Back>
      <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
        <Search
          ref={searchRef}
          method={getSubjectListByQueryId}
          params={searchParams}
          list={[
            {
              type: 'input',
              label: '科目名称',
              field: 'subjectName',
            },
          ]}
        />
        <Tables
          ref={tableRef}
          formRef={searchRef}
          tabletData={{
            columns,
            list: contractList,
            loading: loading || dragSortLoading,
            rowKey: 'subjectId',
            selectedRowKeys,
            onSelectChange,
            dragSort: 'row-handler',
            onDragSort,
          }}
          paging={{
            pageNum,
            pageSize,
            total,
          }}
          method={getSubjectListByQueryId}
          params={searchParams}
        />
      </div>
    </>
  );
});
