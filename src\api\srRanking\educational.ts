import { IGetScaleListBo, IGetEduEnrollmentListBo, IGetEmployListBo } from 'types/srRanking';
import request from 'utils/request/index';

/**
 * 获取规模榜列表
 * @param params
 * @returns
 */
export const getScaleListApi = (params: IGetScaleListBo) =>
  request.get({
    url: '/admBoard/getCollegeScaleList',
    params,
  });

/**
 * 获取招生榜列表
 * @param params
 * @returns
 */
export const getEduEnrollmentListApi = (params: IGetEduEnrollmentListBo) =>
  request.get({
    url: '/admBoard/getCollegeEnrollmentList',
    params,
  });

/**
 * 获取就业榜列表
 * @param params
 * @returns
 */
export const getEmployListApi = (params: IGetEmployListBo) =>
  request.get({
    url: '/admBoard/getCollegeEmploymentList',
    params,
  });
