import React, { useEffect, useRef } from 'react';
import { Divider } from 'tdesign-react';
import { useLocation } from 'react-router-dom';
import classnames from 'classnames';
import { SelectTable } from './Select';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import BasicList from './components/SideList';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { selectResourceManageMaterialSlice, getSubjectList, clearPageState } from 'modules/resourceManage/material';

const TreeTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const pageState = useAppSelector(selectResourceManageMaterialSlice);
  const { broadsideList } = pageState;
  const location = useLocation();
  const prevLocationRef = useRef(location);

  useEffect(() => {
    dispatch(getSubjectList({ subjectId: location?.state?.subjectId }));

    return () => {
      dispatch(clearPageState());
    };
  }, []);

  useEffect(() => {
    // 监听路由变化
    if (prevLocationRef.current.pathname === '/resource/material') {
      dispatch(clearPageState());
    }

    // 更新上一个路由
    prevLocationRef.current = location;
  }, [location, dispatch]);

  return (
    <div className={classnames(CommonStyle.pageWithColor, Style.content)}>
      <div className={Style.treeContent}>
        <div>科目名称</div>
        <Divider></Divider>
        <BasicList broadsideList={broadsideList} />
      </div>
      <div className={Style.tableContent}>
        <SelectTable />
      </div>
    </div>
  );
};

export default TreeTable;
