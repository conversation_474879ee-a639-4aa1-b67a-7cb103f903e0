/* eslint-disable no-nested-ternary */
import React, { Fragment, useEffect, useRef, useState } from 'react';
import {
  selectMallManageGoods,
  clearPageState,
  getGoodsInfo,
  getSubjectList,
  getResourceList,
  editGoods,
} from 'modules/mallManage/goods';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { Button, Checkbox, Form, Input, InputNumber, Loading, Radio, Space, Switch } from 'tdesign-react';
import type { FormInstanceFunctions, FormProps, RadioValue } from 'tdesign-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Back, Editor, Upload } from 'components';
import { strIsNull } from 'utils/tool';
import { IAddGoodsBo, IGetGoodsInfoBo, IGetResourceListBo } from 'types/mallManage';
import { Icon } from 'tdesign-icons-react';
import { useLocation, useNavigate } from 'react-router-dom';

const { FormItem } = Form;
const { Group } = Radio;

const EditVideoCourseGoods: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { goodsId } = location.state || {};
  const dispatch = useAppDispatch();
  const { goodsInfo, goodsInfoLoading, subjectList, resourceLoading, resourceList, addEditGoodsLoading } =
    useAppSelector(selectMallManageGoods);
  const formRef = useRef<FormInstanceFunctions>();
  const [form] = Form.useForm();
  const salesType = Form.useWatch('salesType', form);
  const allowExchange = Form.useWatch('allowExchange', form);
  const introduction = Form.useWatch('introduction', form);
  const [introduce, setIntroduce] = useState('');
  const [fileUrl, setFileUrl] = useState('');

  const INITIAL_DATA = {
    type: 1,
    salesType: 1,
    price: '',
    buyNum: 0,
    subjectId: -1,
    recourseIdList: '',
    allowExchange: 0,
  };

  const rules: FormProps['rules'] = {
    salesType: [
      {
        required: true,
        message: '商品属性不能为空',
        trigger: 'all',
      },
    ],
    goodsName: [
      {
        required: true,
        message: '商品名称不能为空',
        trigger: 'all',
      },
    ],
    imgId: [
      {
        required: true,
        message: '商品图片不能为空',
        trigger: 'all',
      },
    ],
    introduction: [
      {
        required: true,
        message: '商品简介不能为空',
        trigger: 'all',
      },
    ],
    price: [
      {
        required: true,
        message: '商品价格不能为空',
        trigger: 'all',
      },
    ],
    buyNum: [
      {
        required: true,
        message: '购买人数不能为空',
        trigger: 'all',
      },
    ],
    subjectId: [
      {
        required: true,
        message: '关联科目不能为空',
        trigger: 'all',
      },
    ],
    recourseIdList: [
      {
        required: true,
        message: '关联课程不能为空',
        trigger: 'all',
      },
    ],
    allowExchange: [
      {
        required: true,
        message: '支持兑换不能为空',
        trigger: 'all',
      },
    ],
    pointsRequired: [
      {
        required: true,
        message: '兑换积分不能为空',
        trigger: 'all',
      },
    ],
  };

  const renderActiveContent = () => <Icon name='check' />;
  const renderInactiveContent = () => <Icon name='close' />;

  const handleSubmit: FormProps['onSubmit'] = ({ validateResult, fields }) => {
    if (validateResult === true) {
      const bo: IAddGoodsBo = {
        ...fields,
        type: INITIAL_DATA.type,
        goodsId,
      };

      if (salesType === 1) {
        bo.recourseIdList = [fields.recourseIdList];
      }

      dispatch(editGoods(bo));
    }
  };

  const handleReset: FormProps['onReset'] = () => {
    const bo: IGetGoodsInfoBo = {
      goodsId,
    };

    dispatch(getGoodsInfo(bo));
  };

  const handleCancel = () => {
    navigate('/mall/goods', {
      state: {
        type: INITIAL_DATA.type,
      },
    });
  };

  const handleUploadImg = ({
    file: {
      response: {
        data: { id, fileUrl },
      },
    },
  }: {
    file: { response: { data: { id: number; fileUrl: string } } };
  }) => {
    formRef.current?.setFieldsValue?.({ imgId: id });
    setFileUrl(fileUrl);
  };

  const handleChangeIntroduction = (val: string) => {
    formRef.current?.setFieldsValue?.({ introduction: val });
  };

  const handleSubjectIdChange = (subjectId: RadioValue) => {
    formRef.current?.setFieldsValue({ recourseIdList: '' });

    const bo: IGetResourceListBo = {
      type: INITIAL_DATA.type as 1 | 2,
      subjectId,
    };

    dispatch(getResourceList(bo));
  };

  const handleSalesTypeChange = () => {
    formRef.current?.setFieldsValue({ recourseIdList: '' });
  };

  useEffect(() => {
    const bo: IGetGoodsInfoBo = {
      goodsId,
    };

    dispatch(getGoodsInfo(bo));

    dispatch(getSubjectList());

    return () => {
      dispatch(clearPageState());
    };
  }, []);

  useEffect(() => {
    if (!strIsNull(goodsInfo)) {
      const info = JSON.parse(JSON.stringify(goodsInfo));

      if (info.salesType === 1) {
        if (Array.isArray(info.goodsResourceList) && info.goodsResourceList.length > 0) {
          info.goodsResourceList = info.goodsResourceList[0].resourceId;
        }
      } else {
        if (Array.isArray(info.goodsResourceList) && info.goodsResourceList.length > 0) {
          info.goodsResourceList = info.goodsResourceList.map(({ resourceId }) => resourceId);
        }
        setFileUrl(info.imgUrl === null ? '' : info.imgUrl);
      }

      info.recourseIdList = info.goodsResourceList;

      formRef.current?.setFieldsValue(info);

      setIntroduce(info.introduction);

      const bo: IGetResourceListBo = {
        type: INITIAL_DATA.type as 1 | 2,
        subjectId: info.subjectId,
      };

      dispatch(getResourceList(bo));
    }
  }, [goodsInfo]);

  useEffect(() => {
    setIntroduce(introduction);
  }, [introduction]);

  return (
    <Loading
      loading={goodsInfoLoading}
      showOverlay
      style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      <Back path='/mall/goods' header='商品管理-视频课程' params={{ type: INITIAL_DATA.type }} />

      <div className={classnames(CommonStyle.pageWithColor)}>
        <Form
          ref={formRef}
          form={form}
          initialData={INITIAL_DATA}
          resetType='initial'
          rules={rules}
          colon
          className={classnames(CommonStyle.commonSubjectForm)}
          onSubmit={handleSubmit}
          onReset={handleReset}
        >
          <FormItem label='商品属性' name='salesType'>
            <Group onChange={handleSalesTypeChange}>
              <Radio value={1} label='单个' />
              <Radio value={2} label='套装' />
            </Group>
          </FormItem>
          {salesType === 2 && (
            <Fragment>
              <FormItem label='商品名称' name='goodsName'>
                <Input placeholder='请输入商品名称' clearable />
              </FormItem>
              <FormItem label='商品图片' name='imgId'>
                <Upload
                  url='/oss/uploadFile'
                  theme='image'
                  tips='请上传 .jpg/.jpeg/.png 格式的图片 文件大小5M'
                  accept='.jpg, .jpeg, .png'
                  maxFileSize={5}
                  max={1}
                  draggable
                  success={handleUploadImg}
                  files={fileUrl ? [{ url: fileUrl }] : []}
                  params={{
                    businessType: 14,
                  }}
                />
              </FormItem>
              <FormItem label='商品简介' name='introduction'>
                <Editor
                  fixedToolbar='hide'
                  border
                  text={introduce}
                  editorStyle={{ height: '600px', width: '100%' }}
                  maxLength={2000}
                  uploadVideoConfig={{ allowedFileTypes: ['mp4'], maxFileSize: 30 }}
                  uploadImgConfig={{ allowedFileTypes: ['jpg', 'jpeg', 'png'], maxFileSize: 5 }}
                  onChange={handleChangeIntroduction}
                />
              </FormItem>
            </Fragment>
          )}
          <FormItem label='商品价格' name='price'>
            <InputNumber
              placeholder='请输入商品价格'
              theme='normal'
              min={0}
              decimalPlaces={2}
              allowInputOverLimit={false}
              suffix={<span>元</span>}
            />
          </FormItem>
          <FormItem label='购买人数' name='buyNum'>
            <InputNumber
              placeholder='请输入购买人数'
              theme='normal'
              min={0}
              decimalPlaces={0}
              allowInputOverLimit={false}
              suffix={<span>人</span>}
            />
          </FormItem>
          <FormItem label='关联科目' name='subjectId'>
            <Group onChange={handleSubjectIdChange}>
              {subjectList.map(({ subjectId, subjectName }) => (
                <Radio key={subjectId} value={subjectId} label={subjectName} />
              ))}
            </Group>
          </FormItem>
          <FormItem label='关联课程' name='recourseIdList'>
            {resourceLoading ? (
              <Loading size='small' />
            ) : salesType === 1 ? (
              <Group>
                {resourceList.map(({ resourceId, resourceName }) => (
                  <Radio key={resourceId} value={resourceId} label={resourceName} />
                ))}
              </Group>
            ) : (
              <Checkbox.Group>
                {resourceList.map(({ resourceId, resourceName }) => (
                  <Checkbox key={resourceId} value={resourceId} label={resourceName} />
                ))}
              </Checkbox.Group>
            )}
          </FormItem>
          <FormItem label='支持兑换' name='allowExchange'>
            <Switch customValue={[1, 0]} label={[renderActiveContent(), renderInactiveContent()]} />
          </FormItem>
          {allowExchange === 1 && (
            <FormItem label='兑换积分' name='pointsRequired'>
              <InputNumber
                placeholder='请输入兑换积分'
                theme='normal'
                min={1}
                decimalPlaces={0}
                allowInputOverLimit={false}
                suffix={<span>分</span>}
              />
            </FormItem>
          )}
          <FormItem className={classnames(CommonStyle.commonSubjectFormBtn)}>
            <Space>
              <Button loading={addEditGoodsLoading} type='submit' theme='primary'>
                提交
              </Button>
              <Button theme='default' onClick={handleCancel}>
                取消
              </Button>
              <Button type='reset' theme='warning'>
                重置
              </Button>
            </Space>
          </FormItem>
        </Form>
      </div>
    </Loading>
  );
};

export default EditVideoCourseGoods;
