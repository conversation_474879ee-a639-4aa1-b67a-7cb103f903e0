import React, { memo, useRef, useState, useEffect } from 'react';
import {
  Form,
  Row,
  Col,
  Radio,
  Button,
  InputAdornment,
  Loading,
  Tag,
  Input as InputTD,
  Space,
  InputNumber,
} from 'tdesign-react';
import classnames from 'classnames';
import { FormInstanceFunctions } from 'tdesign-react/es/form/type';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import { AddIcon } from 'tdesign-icons-react';
import { Back, Upload, Input, Editor } from 'components';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { getEducationalInfo, selectSchool } from 'modules/directory/school';
import { MAJOR_TYPE, SCHOOL_TYPE } from 'enum';
import { strIsNull } from 'utils/tool';
import AddressMap from 'components/AddressMap';

const { FormItem } = Form;

interface Tag {
  name: string;
}

export default memo(() => {
  const navigate = useNavigate();
  const { state } = useLocation();
  const { id, type, path, header } = state || { row: {} };
  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>();
  const mapRef1 = useRef<{ resetMap: () => void }>(null);
  const mapRef2 = useRef<{ resetMap: () => void }>(null);
  const mapRef3 = useRef<{ resetMap: () => void }>(null);
  const mapRef4 = useRef<{ resetMap: () => void }>(null);
  const { formLoading, educationalInfo } = useAppSelector(selectSchool);
  const [tagList, setTagList] = useState<Tag[]>([]);
  const [logoUrl, setLogoUrl] = useState('');
  const [backgroundImgUrl, setBackgroundImgUrl] = useState('');
  const [educationalQrCodeImgUrl, setEducationalQrCodeImgUrl] = useState('');
  const [educationalIntroduce, setEducationalIntroduce] = useState('');

  useEffect(() => {
    if (id) {
      dispatch(getEducationalInfo(id));
    }
  }, [id]);

  useEffect(() => {
    if (!strIsNull(educationalInfo)) {
      if (!strIsNull(educationalInfo.educationalTitle)) {
        if (educationalInfo.educationalTitle.indexOf(',') > -1) {
          setTagList(educationalInfo.educationalTitle.split(',').map((name) => ({ name })));
        } else {
          setTagList([{ name: educationalInfo.educationalTitle }]);
        }
      }

      setLogoUrl(educationalInfo.logoUrl);
      setBackgroundImgUrl(educationalInfo.backgroundImgUrl);
      setEducationalQrCodeImgUrl(educationalInfo.educationalQrCodeImgUrl);
      setEducationalIntroduce(educationalInfo.educationalIntroduce);

      formRef.current?.setFieldsValue(educationalInfo);
    }
  }, [educationalInfo]);

  useEffect(() => {
    if (!strIsNull(tagList)) {
      formRef.current?.setFieldsValue?.({
        educationalTitle: tagList.map((i) => i.name).join(','),
      });
    }
  }, [tagList]);

  const handleCancel = () => {
    navigate(path, {
      state: {
        type,
      },
    });
  };

  const handleAddressInfo1 = () => { };
  const handleAddressInfo2 = () => { };
  const handleAddressInfo3 = () => { };
  const handleAddressInfo4 = () => { };

  const onSuccess = (
    {
      file: {
        response: {
          data: { id },
        },
      },
    }: {
      file: { response: { data: { id: number } } };
    },
    field: string,
  ) => {
    formRef.current?.setFieldsValue?.({ [field]: id });
  };

  const handleReset = () => {
    dispatch(getEducationalInfo(id));
  };
  return (
    <Loading loading={formLoading}>
      <Back path={path} header={header} params={{ type }} />
      <div className={classnames(CommonStyle.pageWithColor)}>
        <div className={Style.formContainer}>
          <Form ref={formRef} onReset={handleReset} labelWidth={100} labelAlign='top' disabled>
            <Row gutter={[42, 34]}>
              <Col span={6}>
                <Input
                  label='院校名称'
                  name='educationalName'
                  rules={[{ required: true, message: '院校名称必填', type: 'error' }]}
                  clearable
                />
              </Col>
              <Col span={6}>
                <FormItem label='专业类型' name='majorsType' rules={[{ required: true }]}>
                  <Radio.Group>
                    {MAJOR_TYPE.map((t, index) => (
                      <Radio key={index} value={t.value}>
                        {t.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='院校类型' name='educationalType' rules={[{ required: true }]}>
                  <Radio.Group>
                    {SCHOOL_TYPE.map((t, index) => (
                      <Radio key={index} value={t.value}>
                        {t.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='本科/专科' name='educationalDisciplines' rules={[{ required: true }]}>
                  <Radio.Group>
                    <Radio value={1}>本科院校</Radio>
                    <Radio value={2}>专科院校</Radio>
                    <Radio value={3}>本科/专科院校</Radio>
                  </Radio.Group>
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label='院校标签' name='educationalTitle' rules={[{ required: true }]}>
                  <Space direction='vertical'>
                    <Space>
                      {tagList.map((tag: any, i) => (
                        <Tag key={i}>{tag.name}</Tag>
                      ))}
                    </Space>
                  </Space>
                </FormItem>
              </Col>
              <Col span={6} className={Style.dateCol}>
                <FormItem label='LOGO' name='educationalLogoImgId' rules={[{ required: true }]}>
                  <Upload
                    params={{ businessType: 2 }}
                    theme='image'
                    tips='请上传 .jpg/.png 格式的图片 文件大小512kb'
                    accept='.jpg, .png'
                    maxFileSize={512}
                    fileUnit={'kb'}
                    max={1}
                    draggable
                    success={(valueFromChild: any) => onSuccess(valueFromChild, 'educationalLogoImgId')}
                    files={logoUrl ? [{ url: logoUrl }] : []}
                  />
                </FormItem>
              </Col>
              <Col span={6} className={Style.update}>
                <FormItem label='详情背景' name='educationalBackgroundImgId' rules={[{ required: true }]}>
                  <Upload
                    params={{ businessType: 3 }}
                    theme='image'
                    tips='请上传 .jpg/.png 格式的图片 文件大小512kb'
                    max={1}
                    draggable
                    maxFileSize={512}
                    fileUnit={'kb'}
                    accept='.jpg, .png'
                    success={(valueFromChild: any) => onSuccess(valueFromChild, 'educationalBackgroundImgId')}
                    files={backgroundImgUrl ? [{ url: backgroundImgUrl }] : []}
                  />
                </FormItem>
              </Col>
              <Col span={6}>
                <Input label='交流群介绍' name='educationalCrowdDesc' clearable />
              </Col>
              <Col span={6} className={Style.qrCode}>
                <FormItem label='交流群二维码' name='educationalQrCodeImgId'>
                  <Upload
                    params={{ businessType: 10 }}
                    theme='image'
                    tips='请上传 .jpg/.png 格式的图片 文件大小512kb'
                    max={1}
                    draggable
                    maxFileSize={512}
                    fileUnit={'kb'}
                    accept='.jpg, .png'
                    success={(valueFromChild: any) => onSuccess(valueFromChild, 'educationalQrCodeImgId')}
                    files={educationalQrCodeImgUrl ? [{ url: educationalQrCodeImgUrl }] : []}
                  />
                </FormItem>
              </Col>
              <Col span={12} className={Style.dateCol}>
                <FormItem label='院校介绍' name='educationalIntroduce' rules={[{ required: true }]}>
                  <Editor
                    readOnly
                    text={educationalIntroduce}
                    border
                    editorStyle={{ height: '300px', width: '100%' }}
                  ></Editor>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='院校地址1（默认地址）' name='addressBoList' rules={[{ required: true }]}>
                  <div style={{ width: '100%', height: '350px', marginBottom: '20px' }}>
                    <AddressMap
                      initData={educationalInfo?.addressVoList ? educationalInfo?.addressVoList[0] : undefined}
                      domId={'file1'}
                      tipinput={'tipinput1'}
                      onAddressInfo={handleAddressInfo1}
                      ref={mapRef1}
                      disabled
                    />
                  </div>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='院校地址2' name='addressBoList'>
                  <div style={{ width: '100%', height: '350px', marginBottom: '20px' }}>
                    <AddressMap
                      initData={educationalInfo?.addressVoList ? educationalInfo?.addressVoList[1] : undefined}
                      domId={'file2'}
                      tipinput={'tipinput2'}
                      onAddressInfo={handleAddressInfo2}
                      ref={mapRef2}
                      disabled
                    />
                  </div>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='院校地址3' name='addressBoList'>
                  <div style={{ width: '100%', height: '350px', marginBottom: '20px' }}>
                    <AddressMap
                      initData={educationalInfo?.addressVoList ? educationalInfo?.addressVoList[2] : undefined}
                      domId={'file3'}
                      tipinput={'tipinput3'}
                      onAddressInfo={handleAddressInfo3}
                      ref={mapRef3}
                      disabled
                    />
                  </div>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='院校地址4' name='addressBoList'>
                  <div style={{ width: '100%', height: '350px', marginBottom: '20px' }}>
                    <AddressMap
                      initData={educationalInfo?.addressVoList ? educationalInfo?.addressVoList[3] : undefined}
                      domId={'file4'}
                      tipinput={'tipinput4'}
                      onAddressInfo={handleAddressInfo4}
                      ref={mapRef4}
                      disabled
                    />
                  </div>
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem label='男女比例' requiredMark>
                  <FormItem name='maleRatio' rules={[{ required: true }]}>
                    <InputAdornment prepend='男/女'>
                      <InputNumber
                        decimalPlaces={1}
                        allowInputOverLimit={false}
                        min={0}
                        theme='normal'
                        placeholder='输入男生比例'
                      />
                    </InputAdornment>
                  </FormItem>
                  <FormItem name='femaleRatio' rules={[{ required: true }]}>
                    <InputAdornment prepend='女/男'>
                      <InputNumber
                        decimalPlaces={1}
                        allowInputOverLimit={false}
                        min={0}
                        theme='normal'
                        placeholder='输入女生比例'
                      />
                    </InputAdornment>
                  </FormItem>
                </FormItem>
              </Col>
              <Col span={6}>
                <Row justify='space-between'>
                  <Col span={6}>
                    <FormItem label='就业率' name='employmentRate' rules={[{ required: true }]}>
                      <InputAdornment append='%'>
                        <InputNumber
                          decimalPlaces={1}
                          allowInputOverLimit={false}
                          max={100}
                          min={0}
                          theme='normal'
                          placeholder='输入就业率'
                        />
                      </InputAdornment>
                    </FormItem>
                  </Col>
                  <Col span={6}>
                    <FormItem label='在校人数' name='stuNum' rules={[{ required: false }]}>
                      <InputAdornment append='人'>
                        <InputNumber allowInputOverLimit={false} min={0} theme='normal' placeholder='输入在校人数' />
                      </InputAdornment>
                    </FormItem>
                  </Col>
                </Row>
              </Col>
            </Row>

            <FormItem>
              <Space>
                <Button type='reset'>刷新</Button>
                <Button theme='default' onClick={handleCancel}>
                  返回
                </Button>
              </Space>
            </FormItem>
          </Form>
        </div>
      </div>
    </Loading>
  );
});
