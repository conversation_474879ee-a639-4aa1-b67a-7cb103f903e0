import React, { memo, useEffect, useRef, useState } from 'react';
import { Row, Space, TableProps } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectMallManageConvertRecord, getConvertRecordList } from 'modules/mallManage/convertRecord';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { IRef } from 'components/Form';
import { IGetResourceLecturerListBo } from 'types/resourceManage';
import PermissionButton from 'components/PermissionButton';
import { Search, Tables } from 'components';
import CustomColumns from 'components/CustomColumns';
import { useNavigate } from 'react-router-dom';

const ConvertRecordTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total } = useAppSelector(selectMallManageConvertRecord);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetResourceLecturerListBo = { pageNum, pageSize };
  const [columnControllerVisible, setColumnControllerVisible] = useState(false);
  const staticColumns = [
    'serial-number',
    'goodsName',
    'redeemPoints',
    'goodsType',
    'goodsAttr',
    'exchangePerson',
    'exchangeTime',
    'op',
  ];
  const [displayColumns, setDisplayColumns] = useState<Array<string>>(staticColumns);
  const optionalColumns = ['exchangePerson', 'exchangeTime'];
  const goodsTypeList = [
    {
      value: 1,
      label: '视频课程',
    },
    {
      value: 2,
      label: '电子教辅',
    },
  ];

  const columns: TableProps['columns'] = [
    {
      colKey: 'serial-number',
      width: 80,
      title: '序号',
    },
    {
      colKey: 'goodsName',
      title: '商品名称',
      cell({ row }) {
        return <span>{row?.ordersDetailVo?.goodsName}</span>;
      },
    },
    {
      colKey: 'redeemPoints',
      title: '兑换积分',
    },
    {
      colKey: 'goodsType',
      title: '商品类型',
      cell({ row }) {
        return <span>{row?.ordersDetailVo?.goodsType === 1 ? '视频课程' : '电子教辅'}</span>;
      },
    },
    {
      colKey: 'goodsAttr',
      title: '商品属性',
      cell({ row }) {
        return <span>{row?.ordersDetailVo?.goodsAttr === 1 ? '单体' : '套装'}</span>;
      },
    },
    {
      colKey: 'exchangePerson',
      title: '兑换人',
      width: 200,
    },
    {
      colKey: 'exchangeTime',
      title: '兑换时间',
      width: 200,
    },
    {
      colKey: 'op',
      title: '操作',
      width: 220,
      cell({ row }) {
        return (
          <>
            <PermissionButton
              permissions={['mall:goods:query']}
              onClick={() =>
                navigate(`/mall/goods/details/${row.ordersDetailVo.goodsType}`, {
                  state: {
                    goodsId: row.ordersDetailVo.goodsId,
                    backPath: '/mall/record',
                    backHeader: '商品管理-兑换记录',
                  },
                })
              }
            >
              商品详情
            </PermissionButton>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    dispatch(getConvertRecordList(searchParams));
  }, []);

  return (
    <div className={classnames(CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getConvertRecordList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '商品名称',
            field: 'goodsName',
          },
          {
            type: 'select',
            label: '商品类型',
            field: 'goodsType',
            valueField: 'value',
            nameField: 'label',
            options: goodsTypeList,
          },
          {
            type: 'input',
            label: '兑换人',
            field: 'generic',
          },
          {
            type: 'datePicker',
            label: '兑换时间',
            field: 'timeArr',
            pickerPlaceholder: ['开始时间', '结束时间'],
            isTime: true,
          },
        ]}
      />

      <Row justify='space-between'>
        <Space></Space>
        <Space>
          <CustomColumns visible={columnControllerVisible} onChangeVisible={(val) => setColumnControllerVisible(val)} />
        </Space>
      </Row>

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          displayColumns,
          onDisplayColumnsChange: setDisplayColumns,
          optionalColumns,
          columnControllerVisible,
          onColumnControllerVisibleChange: setColumnControllerVisible,
          columns,
          list: list ?? [],
          loading,
          rowKey: 'orderIntegeralId',
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getConvertRecordList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(ConvertRecordTable);
