import React, { useEffect, useState, useRef } from 'react';
import { Tree, Menu, Button, Popconfirm, List, Space, Dialog, Tag } from 'tdesign-react';
import AutoTooltip from 'components/AutoTooltip';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { getMajorsCategoryList, selectSpeciality, deleteMajorsCategory, setActive } from 'modules/directory/speciality';
import type { TreeProps, DialogProps } from 'tdesign-react';
import { EditIcon, DeleteIcon, AddIcon } from 'tdesign-icons-react';
import Froms from './Froms';
import Style from './index.module.less';

export default () => {
  const dispatch = useAppDispatch();
  const { professionalList, active } = useAppSelector(selectSpeciality);
  const [visible, setVisible] = useState(false);
  const [type, setType] = useState<string>('');
  const [row, setRow] = useState();
  const isMounted = useRef(false);

  const onClickCloseBtn: DialogProps['onCloseBtnClick'] = () => {
    setVisible(false);
  };

  useEffect(() => {
    const fetchData = () => {
      if (isMounted.current) {
        dispatch(getMajorsCategoryList(''));
      }
    };
    isMounted.current = true;
    fetchData();

    return () => {
      isMounted.current = false;
    };
  }, []);

  const renderLabel: TreeProps['label'] = (node) => {
    const { data } = node;
    const treeDom = (
      <div className={Style.menuList}>
        <Menu
          value={active}
          onChange={(v) => {
            dispatch(setActive(v as number));
          }}
        >
          <Menu.MenuItem style={{ width: '100%' }} value={data.majorsCategoryId} key={data.majorsCategoryId}>
            <div className={Style.content}>
              <AutoTooltip maxWidth={150}>{data.categoryName}</AutoTooltip>
              <div>
                {(data.parentId === null || data.parentId === undefined) && (
                  <>
                    <Tag theme={data.eduLevel === 1 ? 'warning' : 'success'} variant='light'>
                      {data.eduLevel === 1 ? '专科' : '本科'}
                    </Tag>
                    <Button
                      className={Style.icon}
                      variant='text'
                      shape='square'
                      onClick={() => {
                        setVisible(true);
                        setType('append');
                        setRow(data);
                      }}
                    >
                      <AddIcon className={Style.icon} />
                    </Button>
                  </>
                )}

                <Button
                  className={Style.icon}
                  variant='text'
                  shape='square'
                  onClick={() => {
                    setVisible(true);
                    setType('edit');
                    setRow(data);
                  }}
                >
                  <EditIcon className={Style.icon} />
                </Button>
                <Popconfirm
                  content='确定要删除该数据吗？'
                  theme='danger'
                  onConfirm={() => dispatch(deleteMajorsCategory(data.majorsCategoryId))}
                >
                  <Button className={Style.icon} variant='text' shape='square'>
                    <DeleteIcon />
                  </Button>
                </Popconfirm>
              </div>
            </div>
          </Menu.MenuItem>
        </Menu>
      </div>
    );
    return treeDom;
  };

  return (
    <div className={Style.treeLabel}>
      <List split style={{ margin: '10px 0' }}>
        <List.ListItem
          action={
            <Space>
              <Button
                variant='base'
                onClick={() => {
                  setVisible(true);
                  setType('add');
                }}
              >
                新增大类
              </Button>
            </Space>
          }
        >
          <List.ListItemMeta title='类目名称' />
        </List.ListItem>
      </List>
      <Tree activable data={professionalList} label={renderLabel} />
      {visible && (
        <Dialog
          className={Style.dialog_body}
          header='添加/修改专业类目'
          visible={visible}
          confirmBtn={null}
          cancelBtn={null}
          onCloseBtnClick={onClickCloseBtn}
          width={600}
        >
          <Froms row={row} type={type} success={() => setVisible(false)} onCancel={() => setVisible(false)} />
        </Dialog>
      )}
    </div>
  );
};
