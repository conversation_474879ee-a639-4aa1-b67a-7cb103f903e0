import React, { useRef, forwardRef, useImperativeHandle } from 'react';
import { Row, Form, Input, Space, Button, Select, Col, Cascader, DateRangePicker } from 'tdesign-react';
import { Data, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import { useAppDispatch } from 'modules/store';
import { strIsNull } from 'utils/tool';

const { FormItem } = Form;

interface IFormItem {
  type?: string;
  label?: string;
  field?: string;
  isTime?: boolean | false;
  multiple?: boolean;
  filterable?: boolean;
  disabled?: boolean;
  clearable?: boolean;
  checkStrictly?: boolean;
  nameField?: string;
  valueField?: string;
  disabledField?: string;
  childrenField?: string;
  options?: { label: string; value: string | number }[];
  pickerPlaceholder?: string | Array<string>;
}

interface SearchFormProps {
  method: any;
  params: Record<string, any>;
  list: IFormItem[];
  onFilterChange?: any;
  isExport?: boolean | false;
}

export interface IRef {
  resetForm: () => void;
  getFormParams: () => Record<string, any>;
  setFormParams: (data: Data) => void;
}

const SearchForm = forwardRef<IRef, SearchFormProps>(({ method, params, list, onFilterChange, isExport }, ref) => {
  const formRef = useRef<FormInstanceFunctions>();
  const dispatch = useAppDispatch();
  const onSubmit = () => {
    const queryValue = JSON.parse(JSON.stringify(formRef?.current?.getFieldsValue?.(true)));

    if (isExport) {
      onFilterChange(queryValue);
    }

    if (method) {
      if (queryValue?.timeArr && queryValue?.timeArr.length > 0) {
        queryValue.startTime = queryValue?.timeArr[0];
        queryValue.endTime = queryValue?.timeArr[1];
      }

      if (queryValue?.orderTimeArr && queryValue?.orderTimeArr.length > 0) {
        queryValue.shopStartTime = queryValue?.orderTimeArr[0];
        queryValue.shopEndTime = queryValue?.orderTimeArr[1];
      }

      if (queryValue?.payTimeArr && queryValue?.payTimeArr.length > 0) {
        queryValue.payStartTime = queryValue?.payTimeArr[0];
        queryValue.payEndTime = queryValue?.payTimeArr[1];
      }

      if (queryValue?.dateArr && queryValue?.dateArr.length > 0) {
        queryValue.params = {
          beginTime: queryValue?.dateArr[0],
          endTime: queryValue?.dateArr[1],
        };
      }

      const getParams = {
        ...params,
        ...queryValue,
      };

      delete getParams.timeArr;
      delete getParams.orderTimeArr;
      delete getParams.payTimeArr;
      delete getParams.dateArr;

      dispatch(method(getParams));
    } else {
      throw new Error('表单缺少请求方法');
    }
  };
  const onReset = () => {
    const formParams = formRef?.current?.getFieldsValue?.(true);

    const getParams = {
      ...params,
      ...formParams,
      pageNum: 1,
      pageSize: 10,
    };
    if (isExport) {
      onFilterChange(getParams);
    }

    dispatch(method(getParams));
  };

  const renderFormItem = (item: IFormItem) => {
    if (strIsNull(item.clearable)) {
      item.clearable = true;
    }
    const optionsData = () => {
      if (item?.options && item.options.length > 0 && item?.multiple)
        return [{ [`${item.nameField || 'label'}`]: '全选', checkAll: true }].concat(item.options);
      if (item?.options && item.options.length > 0 && !item?.multiple) return item.options;
      return [];
    };

    switch (item.type) {
      case 'input':
        return (
          <Input
            style={{ width: '200px' }}
            clearable={item?.clearable === true}
            disabled={item?.disabled === true}
            placeholder={`请输入${item.label}`}
          />
        );
      case 'select':
        return (
          <Select
            style={{ width: '200px' }}
            clearable={item?.clearable === true}
            disabled={item?.disabled === true}
            multiple={item?.multiple === true}
            filterable={item?.filterable === true}
            placeholder={`请选择${item.label}`}
            options={optionsData()}
            keys={{
              label: `${item.nameField || 'label'}`,
              value: `${item.valueField || 'value'}`,
              disabled: `${item.disabledField || 'disabled'}`,
            }}
          ></Select>
        );
      case 'cascader':
        return (
          <Cascader
            style={{ width: '200px' }}
            clearable={item?.clearable === true}
            disabled={item?.disabled === true}
            multiple={item?.multiple === true}
            filterable={item?.filterable === true}
            checkStrictly={item?.checkStrictly === true}
            placeholder={`请选择${item.label}`}
            options={optionsData()}
            keys={{
              label: `${item.nameField || 'label'}`,
              value: `${item.valueField || 'value'}`,
              disabled: `${item.disabledField || 'disabled'}`,
              children: `${item.childrenField || 'children'}`,
            }}
          />
        );
      case 'datePicker':
        return (
          <DateRangePicker
            enableTimePicker={item.isTime}
            style={{ width: '200px' }}
            clearable={item?.clearable === true}
            placeholder={item.pickerPlaceholder}
            allowInput
            format={item.isTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'}
          />
        );
      default:
        return null;
    }
  };

  // 暴露给外部的方法
  useImperativeHandle(ref, () => ({
    resetForm: () => {
      formRef?.current?.reset?.();
    },
    getFormParams: () => formRef?.current?.getFieldsValue?.(true),
    setFormParams: (data: Data) => {
      formRef?.current?.setFieldsValue?.(data);
    },
  }));

  return (
    <Form ref={formRef} onSubmit={onSubmit} onReset={onReset} colon labelAlign='right' className='search-form'>
      <Row justify='start' style={{ marginBottom: '20px', rowGap: '20px', columnGap: '10px' }}>
        {list.map((item, index) => (
          <Col key={index}>
            <FormItem label={item.label} name={item.field}>
              {renderFormItem(item)}
            </FormItem>
          </Col>
        ))}
        <Col>
          <Space>
            <Button theme='primary' type='submit'>
              查询
            </Button>
            <Button type='reset' variant='base' theme='default'>
              重置
            </Button>
          </Space>
        </Col>
      </Row>
    </Form>
  );
});

export default SearchForm;
