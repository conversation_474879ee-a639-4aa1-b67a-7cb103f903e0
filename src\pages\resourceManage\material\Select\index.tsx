import React, { useState, memo, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  Button,
  Row,
  Col,
  Popconfirm,
  DialogPlugin,
  MessagePlugin,
  type DialogProps,
  Form,
  Input,
  TableProps,
} from 'tdesign-react';
import { MoveIcon, EditIcon } from 'tdesign-icons-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
// import PreviewFile from 'components/FileViewer';
import Style from './index.module.less';
import {
  selectResourceManageMaterialSlice,
  getSubjectMaterialList,
  deleteSubjectMaterial,
  MaterialDragSort,
  addSubjectMaterial,
  updateMaterialType,
} from 'modules/resourceManage/material';
import { Tables, Search, Upload } from 'components';
import { IRef } from 'components/Form';

const ossUrl = '/oss/uploadFile';

const { FormItem } = Form;
const INITIAL_DATA = {
  materialType: '',
};

export const SelectTable = () => {
  const dispatch = useAppDispatch();
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const [visible, setVisible] = useState(false);
  const [files, setFiles] = useState([]);
  const [fileIdList, setFileIdList] = useState([]);
  const [editable, setEditable] = useState(false);
  const [rowData, setRowData] = useState(false);
  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  const { active, tablist, pageNum, pageSize, total, loading } = useAppSelector(selectResourceManageMaterialSlice);

  const searchParams = { pageNum, pageSize, subjectId: active };

  const getMaterialList = (params: any) => {
    dispatch(getSubjectMaterialList(params));
  };
  useEffect(() => {
    if (active) {
      searchRef.current?.resetForm();
    }
  }, [active]);
  const onClickCloseBtn: DialogProps['onCloseBtnClick'] = () => {
    setFiles([]);
    setVisible(false);
  };

  const onConfirmBtn: DialogProps['onConfirm'] = async () => {
    await dispatch(
      addSubjectMaterial({
        fileIdList,
        subjectId: active,
      }),
    );
    MessagePlugin.success('批量上传资料成功');
    getMaterialList(searchParams);
    setFiles([]);
    setFileIdList([]);
    setVisible(false);
  };

  const onSubmits = async (e: { validateResult: boolean; files: { materialType: any } }) => {
    if (e.validateResult === true) {
      await dispatch(
        updateMaterialType({
          subjectMaterialId: rowData.subjectMaterialId,
          materialType: e?.fields.materialType,
        }),
      );
      MessagePlugin.info('提交成功');
      getMaterialList(searchParams);
      setEditable(false);
    }
  };
  const batchDelete = () => {
    if (selectedRowKeys.length === 0) return MessagePlugin.warning('请选择数据！');
    const confirmDia = DialogPlugin.confirm({
      header: '批量删除提示',
      body: '确定批量删除该数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        await dispatch(
          deleteSubjectMaterial({
            subjectMaterialIdList: selectedRowKeys,
            subjectId: active,
          }),
        );
        MessagePlugin.success('批量删除成功');

        getMaterialList(searchParams);
        handleResetSelection();
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
    return false;
  };

  const handleSuccess = (res: []) => {
    const { response } = res;
    if (response.length === 1) {
      const newItemId = response[0].data.id;
      setFileIdList([...fileIdList, newItemId]);
    } else {
      const arr: any[] | ((prevState: never[]) => never[]) = [];
      response.map(({ data }) => {
        if (data?.id) {
          arr.push(data.id);
        }
      });
      setFileIdList(arr);
    }
  };

  const onDragSort = async (val: any) => {
    try {
      await dispatch(
        MaterialDragSort({
          position: pageNum === 1 ? val.targetIndex + 1 : (pageNum - 1) * pageSize + (val.targetIndex + 1),
          id: val.current.subjectMaterialId,
          parentId: active,
        }),
      );
      MessagePlugin.success('排序成功');
      getMaterialList(searchParams);
      return true;
    } catch (error) {
      throw new Error('排序失败');
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'drag',
      title: '排序',
      cell: () => <MoveIcon />,
      width: 46,
    },
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    { colKey: 'serial-number', width: 80, title: '序号' },
    {
      title: '资料名称',
      fixed: 'left',
      align: 'left',
      ellipsis: true,
      colKey: 'fileOriginalName',
    },
    {
      title: '类型',
      colKey: 'materialType',
      width: 200,
      cell({ row }) {
        return (
          <>
            {row.materialType}
            <EditIcon
              style={{ marginLeft: 10 }}
              onClick={() => {
                setEditable(true);
                setRowData(row);
                INITIAL_DATA.materialType = row.materialType;
              }}
            />
          </>
        );
      },
    },
    {
      title: '上传人',
      width: 200,
      ellipsis: true,
      colKey: 'person',
    },
    {
      title: '上传时间',
      width: 200,
      ellipsis: true,
      colKey: 'createTime',
    },
    {
      align: 'left',
      fixed: 'right',
      width: 200,
      colKey: 'op',
      title: '操作',
      cell({ row }) {
        return (
          <>
            <Button
              theme='primary'
              variant='text'
              onClick={() => {
                if (row.extension === 'pdf') {
                  window.open(row.fileUrl, '_blank');
                } else {
                  window.open(
                    `https://view.officeapps.live.com/op/embed.aspx?src=${row.fileUrl}&wdAccPdf=1&wdDownloadButton=True'`,
                  );
                }
              }}
            >
              预览
            </Button>
            <Popconfirm
              content='确定要删除该数据吗？'
              theme='danger'
              onConfirm={async () => {
                await dispatch(
                  deleteSubjectMaterial({
                    subjectMaterialIdList: [row.subjectMaterialId],
                    subjectId: active,
                  }),
                );
                MessagePlugin.success('删除成功');
                getMaterialList(searchParams);
              }}
            >
              <Button theme='danger' variant='text'>
                删除
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  return (
    <>
      <div
        style={{
          background: 'var(--bg-color-page)',
          borderRadius: 3,
          padding: 24,
        }}
      >
        <Search
          ref={searchRef}
          method={getSubjectMaterialList}
          params={searchParams}
          list={[
            {
              type: 'input',
              label: '资料名称',
              field: 'fileOriginalName',
            },
          ]}
        />
        <Row gutter={8} align='middle' style={{ marginTop: 20 }}>
          <Col>
            <Button variant='outline' theme='primary' onClick={() => setVisible(true)}>
              批量上传资料
            </Button>
          </Col>
          <Col>
            <Button variant='outline' theme='danger' onClick={batchDelete}>
              批量删除
            </Button>
          </Col>
        </Row>
        <Tables
          ref={tableRef}
          formRef={searchRef}
          tabletData={{
            columns,
            list: tablist ?? [],
            loading,
            rowKey: 'subjectMaterialId',
            selectedRowKeys,
            onSelectChange,
            dragSort: 'row-handler',
            onDragSort,
          }}
          paging={{
            pageNum,
            pageSize,
            total,
          }}
          method={getSubjectMaterialList}
          params={searchParams}
        />
      </div>

      <Dialog
        header='批量上传资料'
        visible={visible}
        onCloseBtnClick={onClickCloseBtn}
        style={{ width: 700 }}
        onClose={onClickCloseBtn}
        onConfirm={onConfirmBtn}
      >
        {visible && (
          <Upload
            url={ossUrl}
            max={8}
            theme='file-flow'
            params={{ businessType: 11 }}
            accept='.doc, .docx,.ppt,.pptx,.xlsx,.xls,.txt,.pdf'
            tips='请上传 .doc .docx .ppt .pptx .xlsx .xls .txt .pdf 文件大小10M'
            success={handleSuccess}
            files={files}
          />
        )}
      </Dialog>
      {editable && (
        <Dialog
          header='添加/修改资料类型'
          visible={editable}
          confirmBtn={null}
          cancelBtn={null}
          onCloseBtnClick={onClickCloseBtn}
          width={400}
        >
          <Form className={Style.form} onSubmit={onSubmits} labelAlign='top'>
            <FormItem label='' name='materialType' initialData={INITIAL_DATA.materialType}>
              <Input placeholder='请输入题库资料类型' maxlength={30} showLimitNumber />
            </FormItem>
            <FormItem style={{ marginRight: 0, float: 'right' }}>
              <Button
                type='reset'
                theme='default'
                onClick={() => {
                  setEditable(false);
                }}
                style={{ marginRight: 10 }}
              >
                取消
              </Button>
              <Button type='submit' theme='primary'>
                提交
              </Button>
            </FormItem>
          </Form>
        </Dialog>
      )}
    </>
  );
};

const selectPage: React.FC = () => (
  <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
    <SelectTable />
  </div>
);

export default memo(selectPage);
