import { IDragSort } from 'types';
import { IAddMechanismBo, IEditMechanismBo, IGetMechanismListBo } from 'types/srRanking';
import request from 'utils/request/index';

/**
 * 获取机构榜列表
 * @param params
 * @returns
 */
export const getMechanismListApi = (params: IGetMechanismListBo) =>
  request.get({
    url: '/admBoard/getAgencyList',
    params,
  });

/**
 * 获取机构信息
 * @param id
 * @returns
 */
export const getMechanismInfoApi = (id: number) =>
  request.get({
    url: `/agency/getDetailById/${id}`,
  });

/**
 * 机构榜拖动排序
 * @param data
 * @returns
 */
export const dragSortMechanismApi = (data: IDragSort) =>
  request.post({
    url: '/agency/dragSort',
    data,
  });

/**
 * 添加机构榜
 * @param data
 * @returns
 */
export const addMechanismApi = (data: IAddMechanismBo) =>
  request.post({
    url: '/agency/add',
    data,
  });

/**
 * 编辑机构榜
 * @param data
 * @returns
 */
export const editMechanismApi = (data: IEditMechanismBo) =>
  request.put({
    url: '/agency/edit',
    data,
  });

/**
 * 删除机构榜
 * @param data
 * @returns
 */
export const deleteMechanismApi = (id: number) =>
  request.delete({
    url: `/agency/remove/${id}`,
  });
