import React, { useState, memo, useEffect, useRef } from 'react';
import { But<PERSON>, Col, Row, Popconfirm, TableProps, MessagePlugin, DialogPlugin, Switch } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import {
  selectExamFrequentlyAsked,
  getExamQuestionAdminList,
  deleteExamQuestion,
  examQuestionDragSort,
  openOrCloseExamQuestionTop,
} from 'modules/operationManage/examFrequentlyAsked';
import { Tables, Editor, Search } from 'components';
import { useNavigate } from 'react-router-dom';
import { IData } from '../components/BasicList';
import { MoveIcon } from 'tdesign-icons-react';
import { IRef } from 'components/Form';

export const SelectTable: React.FC<IData> = ({ broadsideList }: IData) => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);

  const { active, loading, tablist, pageNum, pageSize, total } = useAppSelector(selectExamFrequentlyAsked);

  const searchParams = { pageNum, pageSize, examId: active };

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  useEffect(() => {
    if (active) {
      searchRef.current?.resetForm();
    }
  }, [active]);

  const handleClickDelete = (row: any) => {
    dispatch(
      deleteExamQuestion({
        examId: active,
        examQuestionIdList: [row.examQuestionId],
      }),
    );
  };

  const batchUnlinking = () => {
    if (selectedRowKeys.length === 0) return MessagePlugin.warning('请先选择要删除的数据！');
    const confirmDia = DialogPlugin.confirm({
      header: '批量删除提示',
      body: '确定批量删除数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        await dispatch(
          deleteExamQuestion({
            examId: active,
            examQuestionIdList: selectedRowKeys,
          }),
        );
        handleResetSelection();
        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
    return false;
  };

  const pageTitle = (type: string) =>
    `${broadsideList?.find((item) => item.examId === active)?.examName ?? ''} - ${type}问题`;

  const columns: TableProps['columns'] = [
    {
      colKey: 'drag',
      title: '排序',
      cell: () => <MoveIcon />,
      width: 46,
    },
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    {
      title: '问题',
      fixed: 'left',
      colKey: 'questionContent',
      width: 150,
    },
    {
      title: '解答',
      colKey: 'answerContent',
      width: 300,
      cell({ row }) {
        return <Editor readOnly text={row.answerContent} editorStyle={{ height: '50px' }}></Editor>;
      },
    },
    {
      colKey: 'topped',
      title: '置顶',
      width: 80,
      cell({ row }) {
        return (
          <Switch
            value={row.topped === 1}
            onChange={async (val) => {
              await dispatch(
                openOrCloseExamQuestionTop({
                  examQuestionId: row.examQuestionId,
                  isTop: val ? 1 : 0,
                }),
              );
              MessagePlugin.success(`${row.topped === 1 ? '关闭' : '开启'}成功！`);
            }}
          />
        );
      },
    },
    {
      title: '创建人',
      colKey: 'createBy',
    },
    {
      width: 140,
      title: '创建时间',
      colKey: 'createTime',
    },
    {
      title: '修改人',
      colKey: 'updateBy',
    },
    {
      width: 140,
      title: '修改时间',
      colKey: 'updateTime',
    },
    {
      fixed: 'right',
      colKey: 'op',
      title: '操作',
      width: 150,
      cell({ row }) {
        return (
          <>
            <Button
              theme='primary'
              variant='text'
              onClick={() =>
                navigate('/operationManage/examFrequentlyAsked/edit', {
                  state: { row, active, type: 'edit', pageTitle: pageTitle('编辑') },
                })
              }
            >
              编辑
            </Button>
            <Popconfirm content='确定要删除该数据吗？' theme='danger' onConfirm={() => handleClickDelete(row)}>
              <Button theme='danger' variant='text'>
                删除
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  const onDragSort = async (val: any) => {
    try {
      await dispatch(
        examQuestionDragSort({
          parentId: active,
          position: pageNum === 1 ? val.targetIndex + 1 : (pageNum - 1) * pageSize + (val.targetIndex + 1),
          id: val.current.examQuestionId,
        }),
      );
      return true;
    } catch (error) {
      throw new Error('排序失败');
    }
  };

  return (
    <div className={classnames(CommonStyle.pageWithPadding, CommonStyle.pageWithColor)}>
      <Search
        ref={searchRef}
        method={getExamQuestionAdminList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '问题关键字',
            field: 'examQuestionName',
          },
        ]}
      />
      <Row gutter={8} align='middle'>
        <Col>
          <Button
            variant='outline'
            theme='primary'
            onClick={() => {
              navigate('/operationManage/examFrequentlyAsked/add', {
                state: { type: 'add', active, pageTitle: pageTitle('新增') },
              });
            }}
          >
            新增问题
          </Button>
        </Col>
        <Col>
          <Button variant='outline' theme='danger' onClick={batchUnlinking}>
            批量删除
          </Button>
        </Col>
      </Row>
      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: tablist,
          loading,
          rowKey: 'examQuestionId',
          selectedRowKeys,
          onSelectChange,
          dragSort: 'row-handler',
          onDragSort,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getExamQuestionAdminList}
        params={{ examId: active }}
      />
    </div>
  );
};

export default memo(SelectTable);
