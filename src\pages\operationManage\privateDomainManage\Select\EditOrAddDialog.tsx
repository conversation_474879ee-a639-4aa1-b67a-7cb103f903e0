import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  Form,
  MessagePlugin,
  Input,
  type FormInstanceFunctions,
  SubmitContext,
  TableRowData,
} from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import classnames from 'classnames';
import { addOrUpdatePrivateDomain, selectPrivateDomainManage } from 'modules/operationManage/privateDomainManage';
import Style from '../index.module.less';
import { Upload } from 'components';

const { FormItem } = Form;

const ossUrl = '/oss/uploadFile';

interface IRuleDialogProps {
  typeProp: number;
  visibleProp: boolean;
  rowProp?: TableRowData;
  dataType?: string;
  activeProp?: string | number;
  onCancel?: (val: boolean) => void;
  onSuccess?: (val: boolean) => void;
}

export const EditOrAddDialog = ({
  visibleProp,
  rowProp,
  typeProp,
  activeProp,
  dataType,
  onCancel,
}: IRuleDialogProps) => {
  const dispatch = useAppDispatch();

  const { formLoading } = useAppSelector(selectPrivateDomainManage);
  const formRef = useRef<FormInstanceFunctions>();
  const [visible, setVisible] = useState<boolean>(false);
  const INTIN_DATA = rowProp ?? {};

  useEffect(() => {
    setVisible(visibleProp);
  }, [visibleProp, rowProp, typeProp]);

  const cancelBtn = () => {
    setVisible(false);
    if (onCancel) onCancel(false);
  };

  const onSubmit = async (e: SubmitContext) => {
    if (e.validateResult === true) {
      const formValue = formRef.current?.getFieldsValue?.(true);
      await dispatch(
        addOrUpdatePrivateDomain({
          ...formValue,
          privateDomainId: dataType === 'edit' && rowProp ? rowProp.privateDomainId : undefined,
          examStudentRoleId: activeProp,
          type: typeProp,
        }),
      );
      MessagePlugin.success(`${dataType === 'add' ? '新增' : '编辑'}成功！`);
      cancelBtn();
    }
  };

  const onSuccess1 = ({
    file: {
      response: {
        data: { id },
      },
    },
  }: {
    file: { response: { data: { id: number } } };
  }) => {
    formRef.current?.setFieldsValue?.({ qrCodeId: id });
  };

  return (
    <>
      {visible && (
        <Dialog
          confirmLoading={formLoading}
          header={`${dataType === 'add' ? '新增' : '编辑'}${typeProp === 1 ? '群聊' : '咨询师'}`}
          visible={visible}
          confirmBtn={'确定'}
          onConfirm={() => formRef.current?.submit?.()}
          cancelBtn={'取消'}
          onCancel={cancelBtn}
          onCloseBtnClick={cancelBtn}
          width={500}
        >
          <Form ref={formRef} className={classnames(Style.itemContainer)} labelWidth={0} onSubmit={onSubmit}>
            <FormItem
              name='name'
              initialData={INTIN_DATA.name}
              label={typeProp === 1 ? '群聊名称' : '咨询师昵称'}
              rules={[{ required: true, message: `${typeProp === 1 ? '群聊名称' : '咨询师昵称'}必填`, type: 'error' }]}
            >
              <Input
                maxlength={20}
                showLimitNumber
                placeholder={`请输入${typeProp === 1 ? '群聊名称' : '咨询师昵称'}`}
              />
            </FormItem>
            {typeProp === 2 && (
              <FormItem
                name='actualName'
                initialData={INTIN_DATA.actualName}
                label='咨询师真实姓名'
                rules={[{ required: true, message: '咨询师真实姓名必填', type: 'error' }]}
              >
                <Input maxlength={5} showLimitNumber placeholder='请输入咨询师真实姓名' />
              </FormItem>
            )}

            <FormItem
              name='qrCodeId'
              initialData={INTIN_DATA.qrCodeId}
              label='群聊二维码'
              rules={[{ required: true, message: '群聊二维码必传', type: 'error' }]}
            >
              <Upload
                url={ossUrl}
                params={{ businessType: 7 }}
                theme='image'
                tips='请上传 .jpg 文件大小2M'
                max={1}
                draggable
                files={rowProp?.qrCodeURL ? [{ url: rowProp.qrCodeURL }] : []}
                maxFileSize={2}
                accept='.jpg'
                success={onSuccess1}
              />
            </FormItem>
          </Form>
        </Dialog>
      )}
    </>
  );
};

export default EditOrAddDialog;
