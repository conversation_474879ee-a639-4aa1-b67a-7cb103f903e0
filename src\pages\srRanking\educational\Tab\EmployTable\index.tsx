import React, { memo, useEffect, useRef } from 'react';
import { Button, TableProps } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectSRRankingEducational, getEmployList } from 'modules/srRanking/educational';
import { IRef } from 'components/Form';
import { IGetEmployListBo } from 'types/srRanking';
import { ImageViewer, Search, Tables } from 'components';
import { strIsNull } from 'utils/tool';
import { useNavigate } from 'react-router-dom';

const SRRankingEducationalEmployTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total, active } = useAppSelector(selectSRRankingEducational);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetEmployListBo = { pageNum, pageSize, examId: active };

  const columns: TableProps['columns'] = [
    {
      colKey: 'serial-number',
      width: 80,
      title: '排名',
    },
    {
      colKey: 'educationalName',
      title: '院校名称',
    },
    {
      colKey: 'educationalLogoImg',
      title: '院校logo',
      width: 120,
      cell: ({ row }) =>
        row.educationalLogoImg
          ? ImageViewer({
            type: 'item',
            images: [row.educationalLogoImg],
            style: {
              width: 30,
              height: 30,
            },
          })
          : null,
    },
    {
      colKey: 'employmentRate',
      title: '就业率',
      width: 120,
    },
    {
      colKey: 'sexRatio',
      title: '男/女比例',
      width: 120,
    },
    {
      colKey: 'op',
      title: '操作',
      width: 120,
      cell({ row }) {
        return (
          <>
            <Button
              variant='text'
              theme='primary'
              size='medium'
              onClick={() =>
                navigate('/directory/school/details', {
                  state: { id: row.id, type: 3, path: '/srRanking/educational', header: '单招榜-院校榜-就业榜' },
                })
              }
            >
              详情
            </Button>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    if (!strIsNull(active)) {
      dispatch(getEmployList(searchParams));
    }
  }, [active]);

  return (
    <div style={{ paddingTop: 'var(--td-comp-paddingTB-l)' }}>
      <Search
        ref={searchRef}
        method={getEmployList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '院校名称',
            field: 'educationalName',
          },
        ]}
      />

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: list ?? [],
          loading,
          rowKey: 'id',
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getEmployList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(SRRankingEducationalEmployTable);
