import { RootState } from '../store';
import useCreateSliceWithThunk, { useCreateSlice } from 'hooks/useCreateSlice';
import { getBulletinTrendsListApi, deleteTrendsApi, addOrUpdateTrendApi, openOrCloseTrendsApi, openOrCloseBulletinTrendApi } from 'api';
import type { IGetBulletinTrendsListApi, IDeleteTrendsApi, IOpenOrCloseTrendsApi, IbulletinTrendApi, IAddOrUpdateTrendApi } from 'api';
import { MessagePlugin } from 'tdesign-react';

const namespace = 'dynamicPublishing';

const initialState = {
  loading: true,
  formLoading: false,
  pageNum: 1,
  pageSize: 10,
  total: 0,
  list: [] as Array<any>,
};

const { useAsyncThunkWithStatus } = useCreateSliceWithThunk(namespace, initialState);

export const getBulletinTrendsList = useAsyncThunkWithStatus(`getBulletinTrendsList`, async (params) => {
  const {
    data: { rows, total },
  } = await getBulletinTrendsListApi(params as IGetBulletinTrendsListApi);
  const { pageNum, pageSize } = params;
  return {
    total,
    rows,
    pageNum,
    pageSize,
  };
});

export const openOrCloseTrends = useAsyncThunkWithStatus(
  `openOrCloseTrends`,
  async (params, { dispatch, getState }) => {
    await openOrCloseTrendsApi(params as IOpenOrCloseTrendsApi);
    const {
      dynamicPublishing: { pageNum, pageSize },
    } = getState() as RootState;
    await dispatch(
      getBulletinTrendsList({
        pageNum,
        pageSize,
      }),
    );
  },
);
export const openOrCloseBulletinTrend = useAsyncThunkWithStatus(
  `openOrCloseBulletinTrend`,
  async (params, { dispatch, getState }) => {
    await openOrCloseBulletinTrendApi(params as IbulletinTrendApi);
    const {
      dynamicPublishing: { pageNum, pageSize },
    } = getState() as RootState;
    await dispatch(
      getBulletinTrendsList({
        pageNum,
        pageSize,
      }),
    );
  },
);
export const addOrUpdateTrend = useAsyncThunkWithStatus(`addOrUpdateTrend`, async (params) => {
  await addOrUpdateTrendApi(params as IAddOrUpdateTrendApi);
});

export const deleteTrends = useAsyncThunkWithStatus(`deleteTrends`, async (params, { dispatch, getState }) => {
  await deleteTrendsApi(params as IDeleteTrendsApi);
  const {
    dynamicPublishing: { pageNum, pageSize },
  } = getState() as RootState;
  MessagePlugin.success('删除成功！');
  await dispatch(
    getBulletinTrendsList({
      pageNum,
      pageSize,
    }),
  );
});

const dynamicPublishingSliceSlice = useCreateSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  cases: [
    {
      thunk: getBulletinTrendsList,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state, action) => {
        state.total = action.payload.total;
        state.list = action.payload.rows;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: openOrCloseTrends,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state) => {
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: deleteTrends,
      pending: (state) => {
        state.loading = true;
      },
      fulfilled: (state) => {
        state.loading = false;
      },
      rejected: (state) => {
        state.loading = false;
      },
    },
    {
      thunk: addOrUpdateTrend,
      pending: (state) => {
        state.formLoading = true;
      },
      fulfilled: (state) => {
        state.formLoading = false;
      },
      rejected: (state) => {
        state.formLoading = false;
      },
    },
  ],
});

export const { clearPageState } = dynamicPublishingSliceSlice.actions;

export const selectDynamicPublishing = (state: RootState) => state.dynamicPublishing;

export default dynamicPublishingSliceSlice.reducer;
