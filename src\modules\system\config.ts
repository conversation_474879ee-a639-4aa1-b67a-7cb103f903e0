import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getSystemConfigListApi,
  delSystemConfigApi,
  addSystemConfigApi,
  editSystemConfigApi,
  getSystemConfigDetailsInfoApi,
  refreshSystemConfigCacheApi,
} from 'api';
import { MessagePlugin } from 'tdesign-react/es/message/Message';
import { IAddConfigBo, IEditConfigBo, IGetConfigListBo } from 'types/system';

const namespace = 'system/config';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  addEditLoading: boolean;
  delLoading: boolean;
  infoLoading: boolean;
  info: any;
  refreCacheLoading: boolean;
}

const initialState: IInitialState = {
  loading: true,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  addEditLoading: false,
  delLoading: false,
  infoLoading: false,
  info: {},
  refreCacheLoading: false,
};

export const getSystemConfigList = createAsyncThunk(
  `${namespace}/getSystemConfigList`,
  async (bo: IGetConfigListBo) => {
    const data = await getSystemConfigListApi(bo);

    return {
      list: data.rows,
      total: data.total,
      pageNum: bo.pageNum,
      pageSize: bo.pageSize,
    };
  },
);

export const addSystemConfig = createAsyncThunk(`${namespace}/addSystemConfig`, async (bo: IAddConfigBo) => {
  const { data } = await addSystemConfigApi(bo);

  return data;
});

export const editSystemConfig = createAsyncThunk(`${namespace}/editSystemConfig`, async (bo: IEditConfigBo) => {
  await editSystemConfigApi(bo);
});

export const delSystemConfig = createAsyncThunk(`${namespace}/delSystemConfig`, async (configId: number | string) => {
  await delSystemConfigApi(configId);
});

export const getSystemConfigDetailsInfo = createAsyncThunk(
  `${namespace}/getSystemConfigDetailsInfo`,
  async (configId: number) => {
    const { data } = await getSystemConfigDetailsInfoApi(configId);

    return data;
  },
);

export const refreshSystemConfigCache = createAsyncThunk(`${namespace}/refreshSystemConfigCache`, async () => {
  await refreshSystemConfigCacheApi();
});

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSystemConfigList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSystemConfigList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getSystemConfigList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(addSystemConfig.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(addSystemConfig.fulfilled, (state) => {
        MessagePlugin.success('添加成功');
        state.addEditLoading = false;
      })
      .addCase(addSystemConfig.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(editSystemConfig.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(editSystemConfig.fulfilled, (state) => {
        MessagePlugin.success('编辑成功');
        state.addEditLoading = false;
      })
      .addCase(editSystemConfig.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(delSystemConfig.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(delSystemConfig.fulfilled, (state) => {
        MessagePlugin.success('删除成功');
        state.delLoading = false;
      })
      .addCase(delSystemConfig.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(getSystemConfigDetailsInfo.pending, (state) => {
        state.infoLoading = true;
      })
      .addCase(getSystemConfigDetailsInfo.fulfilled, (state, action) => {
        state.info = action.payload;
        state.infoLoading = false;
      })
      .addCase(getSystemConfigDetailsInfo.rejected, (state) => {
        state.infoLoading = false;
      })

      .addCase(refreshSystemConfigCache.pending, (state) => {
        state.refreCacheLoading = true;
      })
      .addCase(refreshSystemConfigCache.fulfilled, (state) => {
        state.refreCacheLoading = false;
        MessagePlugin.success('刷新成功');
      })
      .addCase(refreshSystemConfigCache.rejected, (state) => {
        state.refreCacheLoading = false;
      });
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectSystemConfig = (state: RootState) => state.systemConfig;

export default listBaseSlice.reducer;
