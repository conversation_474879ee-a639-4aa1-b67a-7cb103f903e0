import React, { ReactNode } from 'react';
import { Button } from 'tdesign-react'; // 引入 TDesign 的 Button 组件
import useHasPermission from 'hooks/useHasPermission';
import type { SizeEnum } from 'tdesign-react/es/common';

// 定义允许的主题类型
type ThemeType = 'default' | 'primary' | 'danger' | 'warning' | 'success';
// 定义允许的按钮样式类型
type variantType = 'base' | 'outline' | 'text' | 'dashed';

interface PermissionButtonProps {
  permissions: string[]; // 需要的权限
  onClick?: () => void; // 点击事件
  children?: ReactNode; // 按钮内容
  content?: string | ReactNode;
  fallback?: ReactNode; // 无权限时的替代内容
  loading?: boolean;
  variant?: variantType; // 按钮样式
  theme?: ThemeType; // 按钮主题
  disabled?: boolean; // 是否禁用
  size?: SizeEnum; // 按钮大小
}

const PermissionButton: React.FC<PermissionButtonProps> = ({
  permissions,
  onClick,
  children,
  content,
  fallback = null,
  loading = false,
  variant = 'text',
  theme = 'primary',
  disabled = false,
  size = 'medium',
}) => {
  const hasPermission = useHasPermission(permissions);

  // eslint-disable-next-line no-nested-ternary
  return hasPermission ? (
    children ? (
      <Button loading={loading} variant={variant} theme={theme} size={size} disabled={disabled} onClick={onClick}>
        {children}
      </Button>
    ) : (
      <Button
        loading={loading}
        variant={variant}
        theme={theme}
        size={size}
        disabled={disabled}
        onClick={onClick}
        content={content}
      />
    )
  ) : (
    <>{fallback}</>
  );
};

export default PermissionButton;
