import { IExamSubjectDragSortBo } from 'modules/examManage';
import request from 'utils/request/index';

/**
 * 查询考试
 * @returns
 */
export const getExamListApi = async () => {
  const result = await request.get({
    url: '/exam/getExamList',
  });
  return result;
};

/**
 * 新增考试
 * @param data
 * @param data.examName 考试名称
 * @returns
 */
export const addExamApi = async (data: object) => {
  const result = await request.post({
    url: '/exam/addExam',
    data,
  });
  return result;
};

/**
 * 修改考试
 * @returns
 */
export const updateExamApi = async (data: any) => {
  const result = await request.put({
    url: '/exam/updateExam',
    data,
  });
  return result;
};

interface subjectVoListType {
  subjectId: number;
  subjectName: string;
  isRelated: any;
  examId: number;
}

export interface ExamAllListType {
  examId: number;
  examName: string;
  fullName: string;
  examIntroduction: string;
  examTime: string;
  editingStatus: 1 | 2;
  subjectVoList: subjectVoListType[];
  isRelated: boolean;
}
/**
 * 分页条件查询考试列表
 * @returns
 */
export const getExamAllListApi = async () => {
  const result = await request.get({
    url: '/exam/getExamAllList',
  });
  return result;
};

/**
 * 条件分页查询科目信息
 * @returns
 */
export const getSubjectListByQueryIdApi = async (params: any) => {
  const result = await request.get({
    url: '/exam/getSubjectListByQueryId',
    params,
  });
  return result;
};

/**
 * 考试关联科目
 * @param data
 * @param {number} data.examId 考试id
 * @param {number} data.subjectId 科目id
 * @param {number} data.relation 关联/取消关联(1:关联, 0:取消关联)
 * @returns
 */
export const addExamSubjectApi = async (data: object) => {
  const result = await request.post({
    url: '/exam/addExamSubject',
    data,
  });
  return result;
};

/**
 * 删除考试
 * @param id
 * @returns
 */
export const deleteExamApi = async (id: number) => {
  const result = await request.delete({
    url: `/exam/deleteExam/${id}`,
  });
  return result;
};

/**
 * 查询所有院校类型
 * @returns
 */
export const getEducationalTypeAllApi = async () => {
  const result = await request.get({
    url: `/exam/getEducationalTypeAll`,
  });
  return result;
};

/**
 * 查询所有考生类型
 * @returns
 */
export const getCandidateTypeAllApi = async () => {
  const result = await request.get({
    url: `/exam/getCandidateTypeAll`,
  });
  return result;
};

export interface IExamDragSortApi {
  position: number;
  id: number;
}

/**
 * 考试列表拖动排序
 * @returns
 */
export const examDragSortApi = async (params: IExamDragSortApi) => {
  const result = await request.put({
    url: `/exam/examDragSort`,
    params,
  });
  return result;
};

/**
 * 科目关联列表拖动排序
 * @returns
 */
export const examSubjectDragSortApi = (params: IExamSubjectDragSortBo) =>
  request.put({
    url: `/exam/examSubjectDragSort`,
    data: params,
  });
