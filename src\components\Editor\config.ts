import { createAxios } from 'utils/request/index';
import { isFileExtensionAllowed } from 'utils/tool';
import { MessagePlugin } from 'tdesign-react';
import { closeSignal, showDialog } from 'components';

type strOptional = string;
type numOptional = number | undefined;
type InsertFnType = (url: string, alt: string, href: string) => void;
type FileType = 'image' | 'video';

interface IImgConfig {
  /** form-data fieldName ，默认值 'wangeditor-uploaded-image */
  fieldName?: strOptional;

  /** 单个文件的最大体积限制，默认为 2M */
  maxFileSize?: number;

  /** 最多可上传几个文件，默认为 100 */
  maxNumberOfFiles?: numOptional;

  /** 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 [] */
  allowedFileTypes?: string[];

  /** 自定义参数 */
  meta?: object;
}

interface IVideoConfig {
  /** form-data fieldName ，默认值 'wangeditor-uploaded-image */
  fieldName?: strOptional;

  /** 单个文件的最大体积限制，默认为 2M */
  maxFileSize?: number;

  /** 最多可上传几个文件，默认为 100 */
  maxNumberOfFiles?: numOptional;

  /** 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 [] */
  allowedFileTypes?: string[];

  /** 自定义参数 */
  meta?: object;
}

export interface IRichTextEditor {
  /** ref */
  ref?: any;

  /** 上传图片配置 */
  uploadImgConfig?: IImgConfig;

  /** 上传视频配置 */
  uploadVideoConfig?: IVideoConfig;

  /** 提示占位 */
  placeholder?: string | undefined;

  /** 最大输入 */
  maxLength?: number;

  /** 是否显示边框 */
  border?: boolean;

  /** 编辑器样式 */
  editorStyle?: object;

  /** 显示工具栏菜单列表 */
  toolbar?: any;

  /** 默认值 */
  text?: string;

  /** 隐藏工具栏菜单列表 */
  hideToolbar?: any;

  /** 只读 */
  readOnly?: boolean;

  /** 固定显示/隐藏工具栏 display | hide | default */
  fixedToolbar?: 'display' | 'hide' | 'default';

  /** 输入触发回调 */
  onChange?: any;

  /** 获取 html 数据 同  onChange */
  onChangeHtml?: any;

  /** 获取 text 数据 */
  onChangeText?: any;

  /** 重置 */
  onReset?: any;
}

const axiosInstance = (signal: AbortSignal) =>
  createAxios({
    timeout: 30 * 60 * 1000,
    signal,
    onUploadProgress: (progressEvent: any) => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      showDialog('_', 'active', percentCompleted);
    },
  });

const uploadFile = async (params: any, type: 'image' | 'video') => {
  try {
    const signal: AbortSignal = closeSignal();
    if (type === 'video') {
      axiosInstance(signal);
      showDialog('_', 'active', 0);
      const result = await axiosInstance(signal).upload(params, { url: '/oss/uploadFileQuestionImgList' });
      showDialog(result, 'success', 100);
      return result;
    }
    const result = await createAxios().upload(params, { url: '/oss/uploadFileQuestionImgList' });

    return result;
  } catch (error) {
    showDialog(error, 'error', 100);
    throw new Error(error as string);
  }
};

const createCustomUploadHandler =
  (config: any, fileTypePrefix: string, defaultMaxSize: number) => async (file: File, insertFn: InsertFnType) => {
    if (!file.type.startsWith(fileTypePrefix)) {
      MessagePlugin.warning(`请上传${fileTypePrefix === 'image/' ? '图片' : '视频'}文件！`);
      return false;
    }
    if (config?.allowedFileTypes && Array.isArray(config?.allowedFileTypes) && config?.allowedFileTypes.length > 0) {
      const isAllowed = isFileExtensionAllowed(file, config?.allowedFileTypes);
      if (!isAllowed) {
        MessagePlugin.warning(
          `请上传格式为 ${config?.allowedFileTypes.join(',')} 的${fileTypePrefix === 'image/' ? '图片' : '视频'}！`,
        );
        return false;
      }
    }
    const maxFileSize = config?.maxFileSize ? Number(config?.maxFileSize) * 1024 * 1024 : defaultMaxSize;
    if (maxFileSize < file.size) {
      MessagePlugin.warning(
        `请上传大小不超过 ${config?.maxFileSize ?? 2}M 的${fileTypePrefix === 'image/' ? '图片' : '视频'}！`,
      );
      return false;
    }
    const params = {
      [config?.fieldName ?? 'file']: file,
      ...config?.meta,
    };
    await uploadFile(params, fileTypePrefix.slice(0, -1) as FileType).then(({ data }) => {
      insertFn(data.fileUrl, `${data.fileOriginalName}`, data.fileUrl);
    });

    return true;
  };

/**
 * 显示的菜单
 */
export const MENU_CONFIG = [
  'headerSelect',
  'blockquote',
  '|',
  'bold',
  'underline',
  'italic',
  {
    key: 'group-more-style',
    title: '更多',
    iconSvg:
      '<svg viewBox="0 0 1024 1024"><path d="M204.8 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M505.6 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M806.4 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path></svg>',
    menuKeys: ['through', 'code', 'sup', 'sub', 'clearStyle'],
  },
  'color',
  'bgColor',
  '|',
  'fontSize',
  'fontFamily',
  'lineHeight',
  '|',
  'bulletedList',
  'numberedList',
  'todo',
  {
    key: 'group-justify',
    title: '对齐',
    iconSvg:
      '<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',
    menuKeys: ['justifyLeft', 'justifyRight', 'justifyCenter', 'justifyJustify'],
  },
  {
    key: 'group-indent',
    title: '缩进',
    iconSvg:
      '<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m0-128V320l256 192z"></path></svg>',
    menuKeys: ['indent', 'delIndent'],
  },
  '|',
  'emotion',
  'insertLink',
  {
    key: 'group-image',
    title: '图片',
    iconSvg:
      '<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>',
    menuKeys: ['insertImage', 'uploadImage'],
  },
  {
    key: 'group-video',
    title: '视频',
    iconSvg:
      '<svg viewBox="0 0 1024 1024"><path d="M981.184 160.096C837.568 139.456 678.848 128 512 128S186.432 139.456 42.816 160.096C15.296 267.808 0 386.848 0 512s15.264 244.16 42.816 351.904C186.464 884.544 345.152 896 512 896s325.568-11.456 469.184-32.096C1008.704 756.192 1024 637.152 1024 512s-15.264-244.16-42.816-351.904zM384 704V320l320 192-320 192z"></path></svg>',
    menuKeys: ['insertVideo', 'uploadVideo'],
  },
  'insertTable',
  'codeBlock',
  'divider',
  '|',
  'undo',
  'redo',
  '|',
  'fullScreen',
  'insertFormula',
];

/**
 * 隐藏的菜单
 */
export const HIDE_MENU_CONFIG = ['fullScreen'];

/**
 * 编辑器配置
 */

export const EDITOR_CONFIG = (configProps: IRichTextEditor) => {
  const { uploadImgConfig, uploadVideoConfig, placeholder, maxLength } = configProps;
  return {
    maxLength: maxLength ?? false,
    placeholder: placeholder ?? '请输入内容...',
    hoverbarKeys: {
      formula: {
        menuKeys: ['editFormula'],
      },
    },
    MENU_CONF: {
      // 配置字体大小
      fontSize: {
        // 字号选项
        fontSizeList: [
          '12px',
          '13px',
          '14px',
          '15px',
          '16px',
          '17px',
          '18px',
          '19px',
          '20px',
          '21px',
          '22px',
          '23px',
          '24px',
          '25px',
          '26px',
          '27px',
          '28px',
          '29px',
          '30px',
          '32px',
          '40px',
          '48px',
        ],
        // 默认字号
        defaultFontSize: '17px',
      },
      // 配置行高
      lineHeight: {
        lineHeightList: ['1', '1.25', '1.5', '1.75', '2', '2.5', '3', '3.5', '4'], // 行高选项
        defaultLineHeight: '1.5', // 默认行高
      },
      uploadImage: {
        base64LimitSize: 5 * 1024,
        maxNumberOfFiles: uploadImgConfig?.maxNumberOfFiles ?? 1,
        withCredentials: true,
        customUpload: createCustomUploadHandler(uploadImgConfig, 'image/', 2 * 1024 * 1024),
      },

      uploadVideo: {
        customUpload: createCustomUploadHandler(uploadVideoConfig, 'video/', 2 * 1024 * 1024),
      },
    },
  };
  // onMaxLength: function (editor: any) {
  //   console.log(editor.getConfig().maxLength);
  // },
};

/**
 * 默认样式
 */
export const EDITOR_STYLE = { height: '100px', width: '100%' };
