import React, { useState, memo, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  Button,
  Row,
  Col,
  Popconfirm,
  DialogPlugin,
  MessagePlugin,
  type DialogProps,
  TableProps,
} from 'tdesign-react';
import { useNavigate } from 'react-router-dom';
import { MoveIcon } from 'tdesign-icons-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import Upload from 'components/Upload';
import Style from './index.module.less';
import {
  selectResourceManageQuestionSlice,
  getQuestionList,
  deleteQuestion,
  questionDragSort,
} from 'modules/resourceManage/question';
import { createData } from '../consts';
import { Tables, Search, LaTeX } from 'components';
import { IRef } from 'components/Form';

export const SelectTable = () => {
  const dispatch = useAppDispatch();
  const [selectedRowKeys, onSelectChange] = useState<(string | number)[]>([]);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const [visible, setVisible] = useState(false);
  const [files, setFiles] = useState([]);
  const navigate = useNavigate();

  const handleResetSelection = () => {
    if (tableRef.current) {
      // 调用重置选中方法
      tableRef.current.resetSelection();
    }
  };

  const { active, loading, tablist, pageNum, pageSize, total } = useAppSelector(selectResourceManageQuestionSlice);
  const searchParams = { pageNum, pageSize, subjectId: active };

  const getList = () => {
    dispatch(getQuestionList(searchParams));
  };

  useEffect(() => {
    if (active) {
      searchRef.current?.resetForm();
    }
  }, [active]);

  const onClickCloseBtn: DialogProps['onCloseBtnClick'] = () => {
    setFiles([]);
    setVisible(false);
  };

  const batchDelete = () => {
    if (selectedRowKeys.length === 0) return MessagePlugin.warning('请选择数据！');
    const confirmDia = DialogPlugin.confirm({
      header: '批量删除提示',
      body: '确定批量删除该数据吗？',
      theme: 'warning',
      confirmBtn: '确定',
      cancelBtn: '取消',
      onConfirm: async () => {
        await dispatch(
          deleteQuestion({
            questionIdList: selectedRowKeys,
            subjectId: active,
          }),
        );

        handleResetSelection();

        confirmDia.hide();
      },
      onClose: () => {
        confirmDia.hide();
      },
    });
    return false;
  };

  const handleSuccess = () => {
    setFiles([]);
    setVisible(false);
    getList();
  };

  const download = () => {
    const { VITE_API_URL, VITE_API_URL_PREFIX } = import.meta.env;
    window.open(
      `${VITE_API_URL.replace(/\/+$/, '')}${VITE_API_URL_PREFIX.replace(/\/+$/, '')}/static/单选题模版规范.docx`,
    );
  };

  const onDragSort = async (val: any) => {
    try {
      await dispatch(
        questionDragSort({
          position: pageNum === 1 ? val.targetIndex + 1 : (pageNum - 1) * pageSize + (val.targetIndex + 1),
          strId: val.current.questionId,
          parentId: active,
        }),
      );
      return true;
    } catch (error) {
      throw new Error('排序失败');
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'drag',
      title: '排序',
      cell: () => <MoveIcon />,
      width: 46,
    },
    {
      colKey: 'row-select',
      type: 'multiple',
      width: 50,
    },
    { colKey: 'serial-number', width: 80, title: '序号' },
    {
      title: '题干',
      fixed: 'left',
      align: 'left',
      ellipsis: true,
      colKey: 'stem',
      cell({ row: { stem } }) {
        return (
          <div className={Style.stem}>
            <LaTeX content={stem} />
          </div>
        );
      },
    },
    {
      title: '正确答案',
      colKey: 'answer',
      width: 200,
    },
    {
      title: '解析',
      width: 200,
      ellipsis: true,
      colKey: 'analysis',
      cell({ row: { analysis } }) {
        return (
          <div className={Style.stem}>
            <LaTeX content={analysis} />
          </div>
        );
      },
    },
    {
      title: '修改人',
      width: 200,
      ellipsis: true,
      colKey: 'updateBy',
    },
    {
      title: '修改时间',
      width: 200,
      ellipsis: true,
      colKey: 'updateTime',
    },
    {
      align: 'left',
      fixed: 'right',
      width: 200,
      colKey: 'op',
      title: '操作',
      cell({ row }) {
        return (
          <>
            <Button
              theme='primary'
              variant='text'
              onClick={() => navigate('/resource/question/edit', { state: { row, active, type: 'edit' } })}
            >
              编辑
            </Button>
            <Popconfirm
              content='确定要删除该数据吗？'
              theme='danger'
              onConfirm={() => {
                dispatch(
                  deleteQuestion({
                    questionId: row.questionId,
                    subjectId: active,
                  }),
                );
              }}
            >
              <Button theme='danger' variant='text'>
                删除
              </Button>
            </Popconfirm>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Search
        ref={searchRef}
        method={getQuestionList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '题干',
            field: 'stem',
          },
        ]}
      />
      <Row gutter={8} align='middle'>
        <Col>
          <Button
            variant='outline'
            theme='primary'
            onClick={() => {
              navigate('/resource/question/add', { state: { row: createData, active, type: 'add' } });
            }}
          >
            新增单个题目
          </Button>
        </Col>
        <Col>
          <Button variant='outline' theme='primary' onClick={() => setVisible(true)}>
            批量导入题目
          </Button>
        </Col>
        <Col>
          <Button variant='outline' theme='danger' onClick={batchDelete}>
            批量删除
          </Button>
        </Col>
      </Row>
      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: tablist ?? [],
          loading,
          rowKey: 'questionId',
          selectedRowKeys,
          onSelectChange,
          dragSort: 'row-handler',
          onDragSort,
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getQuestionList}
        params={searchParams}
      />
      <Dialog
        header='批量导入题目'
        visible={visible}
        confirmBtn={null}
        cancelBtn={null}
        onCloseBtnClick={onClickCloseBtn}
      >
        <p onClick={download} style={{ cursor: 'pointer', color: 'var(--td-brand-color)' }}>
          点击下载模板规范
        </p>
        {visible && (
          <Upload params={{ subjectId: active }} url={'/question/word'} success={handleSuccess} files={files} />
        )}
      </Dialog>
    </>
  );
};
