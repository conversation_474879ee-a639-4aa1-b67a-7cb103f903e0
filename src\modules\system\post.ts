import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getSystemPostListApi,
  delSystemPostApi,
  addSystemPostApi,
  editSystemPostApi,
  getSystemPostDetailsInfoApi,
} from 'api';
import { MessagePlugin } from 'tdesign-react/es/message/Message';
import { IAddPostBo, IEditPostBo, IGetPostListBo } from 'types/system';

const namespace = 'system/post';

interface IInitialState {
  loading: boolean;
  list: Array<any>;
  pageNum: number;
  pageSize: number;
  total: number;
  addEditLoading: boolean;
  delLoading: boolean;
  infoLoading: boolean;
  info: any;
}

const initialState: IInitialState = {
  loading: true,
  list: [],
  pageNum: 1,
  pageSize: 10,
  total: 0,
  addEditLoading: false,
  delLoading: false,
  infoLoading: false,
  info: {},
};

export const getSystemPostList = createAsyncThunk(`${namespace}/getSystemPostList`, async (bo: IGetPostListBo) => {
  const data = await getSystemPostListApi(bo);

  return {
    list: data.rows,
    total: data.total,
    pageNum: bo.pageNum,
    pageSize: bo.pageSize,
  };
});

export const addSystemPost = createAsyncThunk(`${namespace}/addSystemPost`, async (bo: IAddPostBo) => {
  const { data } = await addSystemPostApi(bo);

  return data;
});

export const editSystemPost = createAsyncThunk(`${namespace}/editSystemPost`, async (bo: IEditPostBo) => {
  await editSystemPostApi(bo);
});

export const delSystemPost = createAsyncThunk(`${namespace}/delSystemPost`, async (postId: number | string) => {
  await delSystemPostApi(postId);
});

export const getSystemPostDetailsInfo = createAsyncThunk(
  `${namespace}/getSystemPostDetailsInfo`,
  async (postId: number) => {
    const { data } = await getSystemPostDetailsInfoApi(postId);

    return data;
  },
);

const listBaseSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSystemPostList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSystemPostList.fulfilled, (state, action) => {
        state.loading = false;
        state.pageNum = action.payload.pageNum;
        state.pageSize = action.payload.pageSize;
        state.list = action.payload.list;
        state.total = action.payload.total;
      })
      .addCase(getSystemPostList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(addSystemPost.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(addSystemPost.fulfilled, (state) => {
        MessagePlugin.success('添加成功');
        state.addEditLoading = false;
      })
      .addCase(addSystemPost.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(editSystemPost.pending, (state) => {
        state.addEditLoading = true;
      })
      .addCase(editSystemPost.fulfilled, (state) => {
        MessagePlugin.success('编辑成功');
        state.addEditLoading = false;
      })
      .addCase(editSystemPost.rejected, (state) => {
        state.addEditLoading = false;
      })

      .addCase(delSystemPost.pending, (state) => {
        state.delLoading = true;
      })
      .addCase(delSystemPost.fulfilled, (state) => {
        MessagePlugin.success('删除成功');
        state.delLoading = false;
      })
      .addCase(delSystemPost.rejected, (state) => {
        state.delLoading = false;
      })

      .addCase(getSystemPostDetailsInfo.pending, (state) => {
        state.infoLoading = true;
      })
      .addCase(getSystemPostDetailsInfo.fulfilled, (state, action) => {
        state.info = action.payload;
        state.infoLoading = false;
      })
      .addCase(getSystemPostDetailsInfo.rejected, (state) => {
        state.infoLoading = false;
      });
  },
});

export const { clearPageState } = listBaseSlice.actions;

export const selectSystemPost = (state: RootState) => state.systemPost;

export default listBaseSlice.reducer;
