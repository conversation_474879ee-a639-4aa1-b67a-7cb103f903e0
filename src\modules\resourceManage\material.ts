import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';
import {
  getSubjectListApi,
  getSubjectMaterialListApi,
  deleteSubjectMaterialApi,
  MaterialDragSortApi,
  addSubjectMaterialApi,
  updateMaterialTypeApi,
} from 'api';

import type { IMaterialnDragSortApi, IUpdateMaterialTypeApi } from 'api';

const namespace = 'resourceManage/material';

// interface IQuestionAnalysis {
//   oid: string;
//   opt: string;
//   optionContent: string;
// }

interface ISubjectBo {
  subjectName?: string | null | undefined;
  subjectId?: number | null | undefined;
}

interface IInitialState {
  loading: boolean;
  formLoading: boolean;
  pageNum: number;
  current: number;
  pageSize: number;
  total: number;
  active: number | undefined;
  // contractList: ExamAllListType[];
  broadsideList: Array<any>;
  tablist: Array<any>;
  tabslist: Array<any>;
  answer: string;
  answerId: number | string;
  questionId: string;
  subjectId: number;
  stem: string;
  examSubjectId: number | string;
  // optionBoList: IQuestionAnalysis[];
  questionAnalysis: string;
  questionProvenance: string;
}

const initialState: IInitialState = {
  loading: true,
  formLoading: false,
  pageNum: 1,
  current: 1,
  pageSize: 10,
  total: 0,
  // contractList: [],
  active: undefined,
  broadsideList: [],
  tablist: [],
  tabslist: [],
  answer: '',
  answerId: '',
  questionId: '',
  examSubjectId: '',
  subjectId: 0,
  stem: '',
  questionAnalysis: '',
  // optionBoList: [],
  questionProvenance: '',
};

// 查询所有科目
export const getSubjectList = createAsyncThunk(`${namespace}/getList`, async (subjectBo: ISubjectBo, { getState }) => {
  const {
    data: { rows },
  } = await getSubjectListApi(subjectBo.subjectName);
  const state = getState() as RootState;
  const currentActive = state.resourceManageMaterial.active;
  const obj: any = {
    rows,
  };
  if (subjectBo.subjectId) {
    obj.active = subjectBo.subjectId;
  } else if (!currentActive) {
    obj.active = rows[0].subjectId;
  }
  return obj;
});
export const getSubjectMaterialList = createAsyncThunk(`${namespace}/getSubjectMaterialList`, async (params: any) => {
  const {
    data: { rows, total },
  } = await getSubjectMaterialListApi(params);
  return {
    list: rows,
    total,
    pageSize: params.pageSize,
    pageNum: params.pageNum,
  };
});
// 上传资料
export const addSubjectMaterial = createAsyncThunk(`${namespace}/addSubjectMaterial`, async (params: object) => {
  await addSubjectMaterialApi(params);
});
// 删除资料列表
export const deleteSubjectMaterial = createAsyncThunk(
  `${namespace}/deleteSubjectMaterial`,
  async (params: object | undefined) => {
    await deleteSubjectMaterialApi(params);
  },
);
// 修改资料类型
export const updateMaterialType = createAsyncThunk(
  `${namespace}/updateMaterialType`,
  async (params: IUpdateMaterialTypeApi) => {
    await updateMaterialTypeApi(params);
  },
);

export const MaterialDragSort = createAsyncThunk(
  `${namespace}/MaterialDragSort`,
  async (params: IMaterialnDragSortApi) => {
    await MaterialDragSortApi(params);
  },
);

const resourceManageMaterialSlice = createSlice({
  name: namespace,
  initialState,
  reducers: {
    clearPageState: () => initialState,

    setActive: (state, action: PayloadAction<number>) => {
      state.active = action.payload;
    },
    setExamSubjectId: (state, action: PayloadAction<number>) => {
      state.examSubjectId = action.payload;
    },
    /** 添加题干 */
    // setStem: (state, action) => {
    //   state.stem = action.payload;
    // },

    /** 题目选项 */
    // setOption: (state, action) => {
    //   state.optionBoList = action.payload;
    // },

    /** 添加题目解析 */
    // setQuestionAnalysis: (state, action) => {
    //   state.questionAnalysis = action.payload;
    // },

    /** 添加题目出处 */
    // setQuestionProvenance: (state, action) => {
    //   state.questionProvenance = action.payload;
    // },

    /** 添加正确答案 */
    // setAnswer: (state, action) => {
    //   state.answer = action.payload.opt;
    //   state.answerId = action.payload.oid;
    // },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getSubjectList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSubjectList.fulfilled, (state, action) => {
        state.loading = false;
        state.broadsideList = action.payload?.rows;
        if (action.payload?.active) {
          state.active = action.payload?.active;
        }
        if (action.payload?.examSubjectId) {
          state.examSubjectId = action.payload?.examSubjectId;
        }
      })
      .addCase(getSubjectList.rejected, (state) => {
        state.loading = false;
      })

      .addCase(getSubjectMaterialList.pending, (state) => {
        state.loading = true;
      })
      .addCase(getSubjectMaterialList.fulfilled, (state, action) => {
        state.loading = false;
        state.tablist = action.payload?.list;
        state.total = action.payload?.total;
        state.pageSize = action.payload?.pageSize;
        state.pageNum = action.payload?.pageNum;
      })
      .addCase(getSubjectMaterialList.rejected, (state) => {
        state.loading = false;
      });
  },
});
export const { clearPageState, setActive, setExamSubjectId } = resourceManageMaterialSlice.actions;

export const selectResourceManageMaterialSlice = (state: RootState) => state.resourceManageMaterial;

export default resourceManageMaterialSlice.reducer;
