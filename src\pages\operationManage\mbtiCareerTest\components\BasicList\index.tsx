import React, { useState, useEffect } from 'react';
import { Menu, Switch, MessagePlugin, MenuValue } from 'tdesign-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { setActive, setActiveName, selectMbtiAdminExam, openOrCloseMbtiTop } from 'modules/operationManage/mbti';
import Style from './index.module.less';
import './index.less';

interface IListItemData<T = any> {
  listProps?: object;
  style?: object;
  title?: string;
  description?: string;
  image?: string;
  action?: React.ReactElement;
  listItemProps?: T;
  id: T;
  name: string;
  subjectId: number;
  subjectName: string;
}

interface IListData<T = any> {
  [x: string]: any;
  map(arg0: (item: any) => import("react/jsx-runtime").JSX.Element): React.ReactNode;
  length: number;
  listProps?: object;
  IListItemData: IListItemData[];
  active?: T;
  broadsideList: IListItemData[];
}

export interface IData extends React.HTMLAttributes<HTMLElement> {
  broadsideList: IListData;
}

const { MenuItem } = Menu;

const BasicList: React.FC<IData> = ({ broadsideList }) => {
  const dispatch = useAppDispatch();

  const { active } = useAppSelector(selectMbtiAdminExam);
  const [listItemData, setListItemData] = useState(broadsideList);

  useEffect(() => {
    setListItemData(broadsideList);

  }, [broadsideList]);

  return (
    <React.Fragment>
      <Menu
        value={active}
        onChange={(v) => {
          dispatch(setActive(v as number));
          dispatch(setActiveName(listItemData.find((item: { id: MenuValue }) => item.id === v)?.examName));
        }}
        style={{ marginRight: 20 }}
      >
        {listItemData.length !== 0 &&
          listItemData.map((item) => (
            <MenuItem style={{ width: '100%' }} value={item.examId} key={item.examId}>
              <div className={Style.content}>
                <span>{item.examName}</span>
                <Switch
                  value={item.status === 1}
                  onChange={async (val) => {
                    await dispatch(
                      openOrCloseMbtiTop({
                        id: item.id,
                        status: val ? 1 : 0,
                      }),
                    );
                    MessagePlugin.success(`${item.status === 1 ? '关闭' : '开启'}成功！`);
                  }} />
              </div>
            </MenuItem>
          ))}
        {listItemData.length === 0 && <div style={{ textAlign: 'center' }}>暂无数据</div>}
      </Menu>
    </React.Fragment>
  );
};

export default BasicList;
