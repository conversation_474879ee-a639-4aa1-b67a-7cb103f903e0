import React, { memo, useRef, useEffect } from 'react';
import { Form, Row, Col, Input, Button, MessagePlugin, Loading, Checkbox } from 'tdesign-react';
import classnames from 'classnames';
import { SubmitContext, FormInstanceFunctions } from 'tdesign-react/es/form/type';
import CommonStyle from 'styles/common.module.less';
import Style from './index.module.less';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { addOrUpdateInformationRelease, selectInformationRelease } from 'modules/operationManage/InformationRelease';
import { publicGetExamList, selectPublic } from 'modules/public';
import { useLocation, useNavigate } from 'react-router-dom';
import { Back, Upload } from 'components';

const { FormItem } = Form;

const backPath = '/operationManage/InformationRelease';

export default memo(() => {
  const navigate = useNavigate();
  const { state } = useLocation();

  const INITIAL_DATA =
    state.type === 'add'
      ? {
          title: '',
          cover: undefined,
          videoNumberId: '',
          videoFeedId: '',
          examIdList: [],
        }
      : { ...state.row };

  const dispatch = useAppDispatch();
  const formRef = useRef<FormInstanceFunctions>();
  const { formLoading } = useAppSelector(selectInformationRelease);
  const { examList } = useAppSelector(selectPublic);

  useEffect(() => {
    if (state.type === 'edit') {
      formRef.current?.setFieldsValue?.(state.row);
    }
    if (examList.length === 0) {
      dispatch(publicGetExamList(''));
    }
  }, [useLocation]);

  const onSubmit = async (e: SubmitContext) => {
    if (e.validateResult === true) {
      const params = {
        infoId: state.type === 'add' ? undefined : state.row.infoId,
        ...formRef.current?.getFieldsValue?.(true),
      };
      await dispatch(addOrUpdateInformationRelease(params));
      MessagePlugin.success(`${state.type === 'add' ? '新增' : '修改'}成功`);
      navigate(backPath);
    }
  };

  const onSuccess1 = ({
    file: {
      response: {
        data: { id },
      },
    },
  }: {
    file: { response: { data: { id: number } } };
  }) => {
    formRef.current?.setFieldsValue?.({ cover: id });
  };

  const ossUrl = '/oss/uploadFile';

  return (
    <>
      <Back path={backPath} header={state.type === 'add' ? '新增资讯' : '编辑资讯'}></Back>
      <div className={classnames(CommonStyle.pageWithColor)}>
        <div className={Style.formContainer}>
          <Loading loading={formLoading} showOverlay>
            <Form ref={formRef} onSubmit={onSubmit} labelWidth={100} labelAlign='top'>
              <div className={Style.titleBox}></div>
              <Row gutter={[32, 44]}>
                <Col span={12}>
                  <FormItem
                    label='资讯名称'
                    name='title'
                    initialData={INITIAL_DATA.title}
                    rules={[{ required: true, message: '问题名称必填', type: 'error' }]}
                  >
                    <Input maxlength={20} showLimitNumber placeholder='请输入问题名称' />
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem label='资讯封面' name='cover' initialData={INITIAL_DATA.cover} rules={[{ required: true }]}>
                    <Upload
                      url={ossUrl}
                      params={{ businessType: 8 }}
                      theme='image'
                      tips='请上传格式为 .jpg .png. 的图片，大小在3M以内，建议分辨率为318*420px'
                      max={1}
                      maxFileSize={3}
                      draggable
                      accept='.jpg, .png'
                      success={onSuccess1}
                      files={state?.row?.coverUrl ? [{ url: state.row.coverUrl }] : []}
                    />
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label='视频号ID'
                    name='videoNumberId'
                    initialData={INITIAL_DATA.videoNumberId}
                    rules={[{ required: true, message: '视频号ID必填', type: 'error' }]}
                  >
                    <Input maxlength={100} showLimitNumber placeholder='请输入问题名称' />
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label='视频号feedID'
                    name='videoFeedId'
                    initialData={INITIAL_DATA.videoFeedId}
                    rules={[{ required: true, message: '视频号feedID必填', type: 'error' }]}
                  >
                    <Input maxlength={200} showLimitNumber placeholder='请输入视频号feedID' />
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem
                    label='可见考试范围'
                    name='examIdList'
                    initialData={INITIAL_DATA.examIdList}
                    rules={[{ required: true, message: '可见考试范围必填', type: 'error' }]}
                  >
                    <Checkbox.Group>
                      {examList.map((item: any) => (
                        <Checkbox key={item.examId} value={item.examId}>
                          {item.examName}
                        </Checkbox>
                      ))}
                    </Checkbox.Group>
                  </FormItem>
                </Col>
                <Col span={12}>
                  <FormItem>
                    <Button type='submit' theme='primary'>
                      提交
                    </Button>
                    <Button type='reset' style={{ marginLeft: 12 }}>
                      重置
                    </Button>
                  </FormItem>
                </Col>
              </Row>
            </Form>
          </Loading>
        </div>
      </div>
    </>
  );
});
