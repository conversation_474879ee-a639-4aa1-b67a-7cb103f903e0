import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Space, Dialog, Popconfirm } from 'tdesign-react';
import type { DialogProps } from 'tdesign-react';
import { EditIcon, DeleteIcon, AddIcon } from 'tdesign-icons-react';
import { useAppDispatch, useAppSelector } from 'modules/store';
import { setActive, selectResourceManageQuestionSlice, deleteSubject } from 'modules/resourceManage/question';
import Style from './index.module.less';
import Froms from './Froms';

interface IListItemData<T = any> {
  listProps?: object;
  style?: object;
  title?: string;
  description?: string;
  image?: string;
  action?: React.ReactElement;
  listItemProps?: T;
  id: T;
  name: string;
  subjectId: number;
  subjectName: string;
}

interface IListData<T = any> {
  listProps?: object;
  IListItemData: IListItemData[];
  active?: T;
  broadsideList: IListItemData[];
}

interface IData extends React.HTMLAttributes<HTMLElement> {
  broadsideList: IListData;
}

const SideList: React.FC<IData> = ({ broadsideList }) => {
  const dispatch = useAppDispatch();
  const { active, deleteQuestionSubjectLoading } = useAppSelector(selectResourceManageQuestionSlice);
  const [listItemData, setListItemData] = useState<any>(broadsideList);
  const [visible, setVisible] = useState(false);
  const [type, setType] = useState('');
  const [row, setRow] = useState();
  const formRef = useRef<any>(null);

  useEffect(() => {
    setListItemData(broadsideList);
  }, [broadsideList]);

  const onClickCloseBtn: DialogProps['onCloseBtnClick'] = () => {
    setVisible(false);
  };

  return (
    <React.Fragment>
      <div className={Style.menuList}>
        <List split style={{ margin: '10px 0' }}>
          <List.ListItem
            action={
              <Space>
                <Button
                  variant='text'
                  shape='square'
                  onClick={() => {
                    setVisible(true);
                    setType('add');
                  }}
                >
                  <AddIcon size='1.5em' />
                </Button>
              </Space>
            }
          >
            <List.ListItemMeta title='科目名称' />
          </List.ListItem>
        </List>
        <Menu
          value={active}
          onChange={(v) => {
            dispatch(setActive(v as number));
          }}
          style={{ marginRight: 20 }}
        >
          {listItemData &&
            listItemData.length !== 0 &&
            listItemData.map((item) => (
              <Menu.MenuItem style={{ width: '100%' }} value={item.subjectId} key={item.subjectId}>
                <div className={Style.content}>
                  <span>{item.subjectName}</span>
                  <div>
                    <Button
                      className={Style.icon}
                      variant='text'
                      shape='square'
                      onClick={() => {
                        setVisible(true);
                        setType('edit');
                        setRow(item);
                      }}
                    >
                      <EditIcon className={Style.icon} />
                    </Button>
                    <Popconfirm
                      content={item.questionCount > 0 ? '该科目下有题目，确定要删除该科目吗？' : '确定要删除该数据吗？'}
                      theme='danger'
                      onConfirm={() => dispatch(deleteSubject(item.subjectId))}
                    >
                      <Button
                        loading={item.subjectId && item.subjectId === active && deleteQuestionSubjectLoading}
                        className={Style.icon}
                        variant='text'
                        shape='square'
                      >
                        <DeleteIcon />
                      </Button>
                    </Popconfirm>
                  </div>
                </div>
              </Menu.MenuItem>
            ))}
          {listItemData.length === 0 && <div style={{ textAlign: 'center' }}>暂无数据</div>}
        </Menu>
      </div>
      {visible && (
        <Dialog
          className={Style.dialog_body}
          header='新建/修改科目'
          visible={visible}
          confirmBtn={null}
          cancelBtn={null}
          onCloseBtnClick={onClickCloseBtn}
          width={600}
        >
          <Froms
            row={row}
            type={type}
            ref={formRef}
            success={() => setVisible(false)}
            onCancel={() => setVisible(false)}
          ></Froms>
        </Dialog>
      )}
    </React.Fragment>
  );
};

export default SideList;
