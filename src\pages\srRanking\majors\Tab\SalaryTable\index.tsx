import React, { memo, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Popup, TableProps } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { selectSRRankingMajors, getSalaryList } from 'modules/srRanking/majors';
import { IRef } from 'components/Form';
import { IGetSalaryListBo } from 'types/srRanking';
import { Search, Tables } from 'components';
import { strIsNull } from 'utils/tool';
import { useNavigate } from 'react-router-dom';

const SRRankingMajorsSalaryTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total, active } = useAppSelector(selectSRRankingMajors);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetSalaryListBo = { pageNum, pageSize, examId: active };

  const columns: TableProps['columns'] = [
    {
      colKey: 'serial-number',
      width: 80,
      title: '排名',
    },
    {
      colKey: 'majorsName',
      title: '专业名称',
    },
    {
      colKey: 'avgSalary',
      title: '平均薪资',
      width: 120,
    },
    {
      colKey: 'levelName',
      title: '层次',
      width: 120,
      cell: ({ row }) => <span>{row.levelName === '1' ? '专科' : '本科'}</span>,
    },
    {
      colKey: 'op',
      title: '操作',
      width: 120,
      cell({ row }) {
        return (
          <>
            <Button
              variant='text'
              theme='primary'
              size='medium'
              onClick={() =>
                navigate('/directory/speciality/details', {
                  state: { majorsId: row.majorsId, type: 2, path: '/srRanking/majors', header: '单招榜-专业榜-薪资榜' },
                })
              }
            >
              详情
            </Button>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    if (!strIsNull(active)) {
      dispatch(getSalaryList(searchParams));
    }
  }, [active]);

  return (
    <div style={{ paddingTop: 'var(--td-comp-paddingTB-l)' }}>
      <Search
        ref={searchRef}
        method={getSalaryList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '专业名称',
            field: 'majorsName',
          },
        ]}
      />

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: list ?? [],
          loading,
          rowKey: 'id',
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getSalaryList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(SRRankingMajorsSalaryTable);
