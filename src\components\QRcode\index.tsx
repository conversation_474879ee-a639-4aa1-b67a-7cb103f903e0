import React from 'react';
import QRCode from 'qrcode.react';
import './index.css';

interface Code {
  value?: string;
}
export const QRCompents = (props: Code) => (
  <div className='con'>
    <QRCode
      id='qrid'
      value={props.value}
      imageSettings={{
        src: '/src/assets/image/11.png',
        height: 50,
        width: 50,
        excavate: false,
      }}
      size={200} />
  </div>
)

export default QRCompents;
