import React, { memo, useEffect, useRef } from 'react';
import { Button, Loading, Popup, PopupProps, TableProps } from 'tdesign-react';
import { useAppSelector, useAppDispatch } from 'modules/store';
import {
  selectSRRankingMajors,
  getEnrollmentList,
  getInstitutionList,
  clearInstitutionList,
} from 'modules/srRanking/majors';
import { IRef } from 'components/Form';
import { IGetEnrollmentListBo, IGetInstitutionListBo } from 'types/srRanking';
import { Search, Tables } from 'components';
import { strIsNull } from 'utils/tool';
import { useNavigate } from 'react-router-dom';

const SRRankingMajorsEnrollmentTable: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, list, pageNum, pageSize, total, active, institutionList, institutionListLoading } =
    useAppSelector(selectSRRankingMajors);
  const tableRef = useRef(null);
  const searchRef = useRef<IRef | null>(null);
  const searchParams: IGetEnrollmentListBo = { pageNum, pageSize, examId: active };

  const renderInstitutionContent = () => (
    <Loading loading={institutionListLoading}>
      {institutionList && (
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)',
            gap: '10px',
            maxWidth: 'calc(4 * (200px + 10px))',
            width: '100%',
            margin: '0 auto',
          }}
        >
          {institutionList.map((item) => (
            <div key={item.id}>{item.educationalName}</div>
          ))}
        </div>
      )}
    </Loading>
  );

  const handleInstitutionNumberClick = (majorsId: number) => {
    const bo: IGetInstitutionListBo = {
      examId: active,
      majorsId,
    };

    dispatch(getInstitutionList(bo));
  };

  const handleVisibleChange: PopupProps['onVisibleChange'] = (visible) => {
    if (!visible) {
      clearInstitutionList();
    }
  };

  const columns: TableProps['columns'] = [
    {
      colKey: 'serial-number',
      width: 80,
      title: '排名',
    },
    {
      colKey: 'majorsName',
      title: '专业名称',
    },
    {
      colKey: 'enrollmentNumber',
      title: '招生人数',
      width: 120,
    },
    {
      colKey: 'institutionNumber',
      title: '招生院校',
      width: 120,
      cell: ({ row }) =>
        row.institutionNumber ? (
          <Popup trigger='click' showArrow content={renderInstitutionContent()} onVisibleChange={handleVisibleChange}>
            <Button onClick={() => handleInstitutionNumberClick(row.majorsId)} theme='primary' variant='text'>
              {row.institutionNumber}
            </Button>
          </Popup>
        ) : (
          ''
        ),
    },
    {
      colKey: 'levelName',
      title: '层次',
      width: 120,
      cell: ({ row }) => <span>{row.levelName === '1' ? '专科' : '本科'}</span>,
    },
    {
      colKey: 'op',
      title: '操作',
      width: 120,
      cell({ row }) {
        return (
          <>
            <Button
              variant='text'
              theme='primary'
              size='medium'
              onClick={() =>
                navigate('/directory/speciality/details', {
                  state: { majorsId: row.majorsId, type: 1, path: '/srRanking/majors', header: '单招榜-专业榜-招生榜' },
                })
              }
            >
              详情
            </Button>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    if (!strIsNull(active)) {
      dispatch(getEnrollmentList(searchParams));
    }
  }, [active]);

  return (
    <div style={{ paddingTop: 'var(--td-comp-paddingTB-l)' }}>
      <Search
        ref={searchRef}
        method={getEnrollmentList}
        params={searchParams}
        list={[
          {
            type: 'input',
            label: '专业名称',
            field: 'majorsName',
          },
        ]}
      />

      <Tables
        ref={tableRef}
        formRef={searchRef}
        tabletData={{
          columns,
          list: list ?? [],
          loading,
          rowKey: 'id',
        }}
        paging={{
          pageNum,
          pageSize,
          total,
        }}
        method={getEnrollmentList}
        params={searchParams}
      />
    </div>
  );
};

export default memo(SRRankingMajorsEnrollmentTable);
