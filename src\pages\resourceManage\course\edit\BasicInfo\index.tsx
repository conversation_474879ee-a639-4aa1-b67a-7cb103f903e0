/* eslint-disable no-nested-ternary */
import React, { useEffect, useRef, useState } from 'react';
import {
  selectResourceManageCourse,
  editResourceCourse,
  getTeacherList,
  getCourseInfo,
} from 'modules/resourceManage/course';
import { useAppSelector, useAppDispatch } from 'modules/store';
import { Button, Form, Input, Loading, Select, Space, Textarea } from 'tdesign-react';
import type { FormInstanceFunctions, FormProps, TabValue } from 'tdesign-react';
import classnames from 'classnames';
import CommonStyle from 'styles/common.module.less';
import { Editor, Upload } from 'components';
import { IEditResourceCourseBo } from 'types/resourceManage';
import { useLocation, useNavigate } from 'react-router-dom';
import { strIsNull } from 'utils/tool';

const { FormItem } = Form;
const { Option } = Select;

const EditCourseBasicInfo: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id } = location.state || {};
  const dispatch = useAppDispatch();
  const { courseInfo, courseInfoLoading, addEditGoodsLoading, teacherList, teacherLoading } =
    useAppSelector(selectResourceManageCourse);
  const formRef = useRef<FormInstanceFunctions>();
  const [form] = Form.useForm();
  const description = Form.useWatch('description', form);
  const [desc, setDesc] = useState('');
  const [fileUrl, setFileUrl] = useState('');

  const INITIAL_DATA = {
    courseName: '',
    teacherIds: [],
    coverImage: '',
    brief: '',
    description: '',
  };

  const rules: FormProps['rules'] = {
    courseName: [
      {
        required: true,
        message: '课程名称不能为空',
        trigger: 'all',
      },
    ],
    // teacherIds: [
    //   {
    //     required: true,
    //     message: '课程讲师不能为空',
    //     trigger: 'all',
    //   },
    // ],
    coverImage: [
      {
        required: true,
        message: '课程封面不能为空',
        trigger: 'all',
      },
    ],
    brief: [
      {
        required: true,
        message: '课程简介不能为空',
        trigger: 'all',
      },
    ],
  };

  const handleSubmit: FormProps['onSubmit'] = ({ validateResult, fields }) => {
    if (validateResult === true) {
      const bo: IEditResourceCourseBo = {
        ...fields,
        id,
      };

      dispatch(editResourceCourse(bo));
    }
  };

  const handleReset: FormProps['onReset'] = () => {
    dispatch(getCourseInfo(id));
  };

  const handleCancel = () => {
    navigate('/resource/course');
  };

  const handleUploadImg = ({
    file: {
      response: {
        data: { id, fileUrl },
      },
    },
  }: {
    file: { response: { data: { id: number; fileUrl: string } } };
  }) => {
    formRef.current?.setFieldsValue?.({ coverImage: id });
    setFileUrl(fileUrl);
  };

  const handleChangeIntroduction = (val: string) => {
    formRef.current?.setFieldsValue?.({ description: val });
  };

  useEffect(() => {
    dispatch(getTeacherList());

    dispatch(getCourseInfo(id));
  }, []);

  useEffect(() => {
    if (!strIsNull(courseInfo)) {
      formRef.current?.setFieldsValue(courseInfo);

      setFileUrl(courseInfo.coverImageUrl === null ? '' : courseInfo.coverImageUrl);
      setDesc(courseInfo.description);
    }
  }, [courseInfo]);

  useEffect(() => {
    setDesc(description);
  }, [description]);

  return (
    <Loading
      loading={courseInfoLoading}
      showOverlay
      style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      <div className={classnames(CommonStyle.pageWithColor)}>
        <Form
          ref={formRef}
          form={form}
          initialData={INITIAL_DATA}
          resetType='initial'
          rules={rules}
          colon
          className={classnames(CommonStyle.commonSubjectForm)}
          onSubmit={handleSubmit}
          onReset={handleReset}
        >
          <FormItem label='课程名称' name='courseName'>
            <Input placeholder='请输入课程名称' clearable />
          </FormItem>
          <FormItem label='课程讲师' name='teacherIds'>
            {teacherLoading ? (
              <Loading size='small' />
            ) : (
              <Select placeholder='请选择课程讲师' clearable filterable multiple>
                {teacherList.map(({ id, teacherName }) => (
                  <Option value={id} label={teacherName} key={id} />
                ))}
              </Select>
            )}
          </FormItem>
          <FormItem label='课程封面' name='coverImage'>
            <Upload
              url='/oss/uploadFile'
              theme='image'
              tips='请上传 .jpg/.jpeg/.png 格式的图片 文件大小5M'
              accept='.jpg, .jpeg, .png'
              maxFileSize={5}
              max={1}
              draggable
              success={handleUploadImg}
              files={fileUrl ? [{ url: fileUrl }] : []}
              params={{
                businessType: 13,
              }}
            />
          </FormItem>
          <FormItem label='课程简介' name='brief'>
            <Textarea placeholder='请输入课程简介' />
          </FormItem>
          <FormItem label='课程详情' name='description'>
            <Editor
              fixedToolbar='hide'
              border
              text={desc}
              editorStyle={{ height: '300px', width: '100%' }}
              maxLength={2000}
              uploadVideoConfig={{ allowedFileTypes: ['mp4'], maxFileSize: 30 }}
              uploadImgConfig={{ allowedFileTypes: ['jpg', 'jpeg', 'png'], maxFileSize: 5 }}
              onChange={handleChangeIntroduction}
            />
          </FormItem>
          <FormItem className={classnames(CommonStyle.commonSubjectFormBtn)}>
            <Space>
              <Button loading={addEditGoodsLoading} type='submit' theme='primary'>
                提交
              </Button>
              <Button theme='default' onClick={handleCancel}>
                取消
              </Button>
              <Button type='reset' theme='warning'>
                重置
              </Button>
            </Space>
          </FormItem>
        </Form>
      </div>
    </Loading>
  );
};

export default EditCourseBasicInfo;
