import { IEditDataScopeBo } from 'types/system';
import request from 'utils/request/index';

export interface IGetRoleListApi {
  pageNum: number;
  pageSize: number;
}

/**
 * 获取角色列表
 * @param params
 * @returns
 */
export const getRoleListApi = async (params: IGetRoleListApi) => {
  const result = await request.get({
    url: '/system/role/list',
    params,
  });
  return result;
};

/**
 * 根据角色Id获取角色信息
 * @param params
 * @returns
 */
export const getRoleInfoApi = async (roleId: number) => {
  const result = await request.get({
    url: `/system/role/${roleId}`,
  });
  return result;
};

export const getDeptTreeSelectApi = async (roleId: number) => {
  const result = await request.get({
    url: `/system/role/deptTree/${roleId}`,
  });
  return result;
};

export interface IRoleChangeStatusApi {
  roleId: number;
  status: '1' | '0';
}

export const roleChangeStatusApi = async (data: IRoleChangeStatusApi) => {
  const result = await request.put({
    url: '/system/role/changeStatus',
    data,
  });
  return result;
};

/**
 * 获取全部权限
 * @returns
 */
export const getTreeselectApi = async () => {
  const result = await request.get({
    url: '/system/menu/treeselect',
  });
  return result;
};

/**
 * 获取指定角色权限
 * @returns
 */
export const getRoleMenuTreeselectApi = async (roleId: number) => {
  const result = await request.get({
    url: `/system/menu/roleMenuTreeselect/${roleId}`,
  });
  return result;
};

export interface IAddRoleApi {
  roleName: string;
  roleKey: string;
  roleSort: number;
  status: '1' | '0';
  menuIds: number[];
  remark: string;
}

/**
 * 添加角色
 * @returns
 */
export const addRoleApi = async (data: IAddRoleApi) => {
  const result = await request.post({
    url: '/system/role',
    data,
  });
  return result;
};

/**
 * 添加角色
 * @returns
 */
export const putRoleApi = async (data: IAddRoleApi) => {
  const result = await request.put({
    url: '/system/role',
    data,
  });
  return result;
};

/**
 * 删除角色
 * @returns
 */
export const delRoleApi = async (roleId: number) => {
  const result = await request.delete({
    url: `/system/role/${roleId}`,
  });
  return result;
};

/**
 * 修改数据权限
 * @returns
 */
export const editDataScopeApi = async (bo: IEditDataScopeBo) => {
  const result = await request.put({
    url: '/system/role/dataScope',
    data: bo,
  });
  return result;
};
